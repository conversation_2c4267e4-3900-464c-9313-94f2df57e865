#!/usr/bin/env python3
"""
Check commit messages in database vs repository
"""

import sys
sys.path.append('/app')

from document_database import DocumentDatabase
from repository_backends import get_backend_manager
from config_manager import ConfigManager

def check_commit_messages():
    # Load database
    db = DocumentDatabase('/app/data/documents.db')
    docs = db.get_documents(limit=50)
    
    # Load config and backend
    config_manager = ConfigManager('/app/data/config.json')
    config = config_manager.load_config()
    backend_manager = get_backend_manager()
    
    print('=== COMMIT MESSAGES COMPARISON ===')
    print()
    
    for doc in sorted(docs, key=lambda x: x.revision):
        print(f'--- Revision {doc.revision} ---')
        print(f'Database message: "{doc.commit_message}"')
        
        # Get actual commit message from repository
        repo = None
        for r in config.repositories:
            if r.name == doc.repository_name:
                repo = r
                break
        
        if repo:
            backend = backend_manager.get_backend_for_repository(repo, config)
            if backend:
                commit_info = backend.get_commit_info(repo, str(doc.revision))
                if commit_info:
                    print(f'Repository message: "{commit_info.message}"')
                    
                    # Check if they match
                    if doc.commit_message != commit_info.message:
                        print(f'❌ MISMATCH! Database has different message than repository')
                    else:
                        print(f'✅ Messages match')
                else:
                    print(f'❌ Could not get commit info from repository')
            else:
                print(f'❌ Could not get backend for repository')
        else:
            print(f'❌ Repository {doc.repository_name} not found in config')
        
        print()

if __name__ == "__main__":
    check_commit_messages()
