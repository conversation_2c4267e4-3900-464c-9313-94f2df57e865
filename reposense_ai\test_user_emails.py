#!/usr/bin/env python3
"""
Test script to send emails to users via MailHog
"""

import sys
import os
sys.path.append('/app')

from config_manager import ConfigManager
from email_service import EmailService
from models import UserRole
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

def test_user_emails():
    """Test sending emails to all users"""
    print("📧 Testing Email Functionality with Users")
    print("=" * 50)
    
    try:
        # Load configuration
        print("1. Loading configuration...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print(f"   ✅ Config loaded")
        print(f"   📊 Users found: {len(config.users)}")
        print(f"   📧 SMTP Host: {config.smtp_host}")
        print(f"   🔌 SMTP Port: {config.smtp_port}")
        print(f"   📤 From Email: {config.email_from}")
        
        if not config.users:
            print("   ❌ No users found to send emails to!")
            return False
        
        # Show users
        print("\n2. Users to receive test emails:")
        for user in config.users:
            status = "✅ Enabled" if user.enabled else "❌ Disabled"
            notifications = "🔔 Notifications ON" if user.receive_all_notifications else "🔕 Notifications OFF"
            print(f"   - {user.username} ({user.email}) - {user.role.value} - {status} - {notifications}")
        
        # Test SMTP connection first
        print("\n3. Testing SMTP connection to MailHog...")
        try:
            with smtplib.SMTP(config.smtp_host, config.smtp_port) as server:
                # MailHog doesn't require authentication
                print("   ✅ SMTP connection successful")
        except Exception as e:
            print(f"   ❌ SMTP connection failed: {e}")
            return False
        
        # Send test emails to each user
        print("\n4. Sending test emails...")
        
        for i, user in enumerate(config.users, 1):
            print(f"\n   📧 Sending email {i}/{len(config.users)} to {user.username}...")
            
            try:
                # Create test email
                msg = MIMEMultipart()
                msg['From'] = config.email_from
                msg['To'] = user.email
                msg['Subject'] = f"RepoSense AI Test Email - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                
                # Create personalized email body
                body = f"""
Hello {user.full_name or user.username},

This is a test email from RepoSense AI to verify that email notifications are working correctly.

User Details:
- Username: {user.username}
- Email: {user.email}
- Role: {user.role.value.title()}
- Department: {user.department or 'Not specified'}
- Phone: {user.phone or 'Not specified'}
- Notifications Enabled: {'Yes' if user.receive_all_notifications else 'No'}
- Account Status: {'Active' if user.enabled else 'Disabled'}

Email Configuration:
- SMTP Server: {config.smtp_host}:{config.smtp_port}
- From Address: {config.email_from}
- Test Time: {datetime.now().isoformat()}

If you receive this email in MailHog (http://localhost:8025), the email system is working correctly!

This email was sent as part of the RepoSense AI email functionality test.

Best regards,
RepoSense AI System
                """.strip()
                
                msg.attach(MIMEText(body, 'plain'))
                
                # Send email via MailHog
                with smtplib.SMTP(config.smtp_host, config.smtp_port) as server:
                    server.send_message(msg)
                
                print(f"      ✅ Email sent successfully to {user.email}")
                
            except Exception as e:
                print(f"      ❌ Failed to send email to {user.email}: {e}")
        
        # Test EmailService class if available
        print("\n5. Testing EmailService class...")
        try:
            email_service = EmailService(config)
            
            # Send a test email using the EmailService
            test_subject = f"EmailService Test - {datetime.now().strftime('%H:%M:%S')}"
            test_body = """
This email was sent using the RepoSense AI EmailService class.

This tests the integrated email functionality that would be used for:
- Repository change notifications
- System alerts
- User account notifications
- Historical scan completion notices

If you see this in MailHog, the EmailService integration is working correctly!
            """.strip()
            
            # Get all user emails
            recipient_emails = [user.email for user in config.users if user.enabled]
            
            if recipient_emails:
                success = email_service.send_email(
                    to_emails=recipient_emails,
                    subject=test_subject,
                    body=test_body
                )
                
                if success:
                    print(f"   ✅ EmailService test successful - sent to {len(recipient_emails)} recipients")
                else:
                    print("   ❌ EmailService test failed")
            else:
                print("   ⚠️  No enabled users found for EmailService test")
                
        except Exception as e:
            print(f"   ❌ EmailService test failed: {e}")
        
        print("\n" + "=" * 50)
        print("📊 EMAIL TEST SUMMARY")
        print("=" * 50)
        
        print(f"✅ SMTP Connection: Working")
        print(f"📧 Users Found: {len(config.users)}")
        print(f"📤 Emails Sent: Individual test emails to each user")
        print(f"🔧 EmailService: Tested with bulk email functionality")
        
        print(f"\n🌐 Next Steps:")
        print(f"1. Open MailHog Web Interface: http://localhost:8025")
        print(f"2. Check for {len(config.users)} + 1 test emails")
        print(f"3. Verify email content and formatting")
        print(f"4. Test email notifications in RepoSense AI")
        
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_user_emails()
    exit(0 if success else 1)
