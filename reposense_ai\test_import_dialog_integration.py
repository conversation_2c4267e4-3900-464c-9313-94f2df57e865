#!/usr/bin/env python3
"""
Test script to verify the repository import dialog integration with branch monitoring
"""

import sys
import os
import json
import requests
sys.path.append('/app')

from config_manager import Config<PERSON><PERSON><PERSON>

def test_import_endpoint():
    """Test the repository import endpoint with branch monitoring fields"""
    print("🔗 Testing Repository Import Endpoint Integration")
    print("=" * 60)
    
    # Test data simulating what the import dialog would send
    import_data = {
        'name': 'test_import_multi_branch',
        'url': 'http://sundc:81/svn/reposense_branch_tag_test',
        'username': 'fvaneijk',
        'password': 'ankeanke',
        'last_revision': 0,
        'product_documentation_files': ['README.md', 'docs/api.md'],
        'risk_aggressiveness': 'HIGH',
        'risk_description': 'Test import with multi-branch monitoring',
        'monitor_all_branches': True,  # ✅ New field
        'branch_path': 'trunk'         # ✅ New field
    }
    
    print("📤 Sending import request with branch monitoring data:")
    print(f"   Repository: {import_data['name']}")
    print(f"   URL: {import_data['url']}")
    print(f"   Monitor All Branches: {import_data['monitor_all_branches']}")
    print(f"   Branch Path: {import_data['branch_path']}")
    print()
    
    try:
        # Send POST request to import endpoint
        response = requests.post(
            'http://localhost:5000/repositories/import',
            json=import_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Import request successful!")
                print(f"   Message: {result.get('message')}")
                print(f"   Repository ID: {result.get('repository_id')}")
                
                # Verify the repository was added with correct branch monitoring settings
                print("\n🔍 Verifying repository configuration...")
                config_manager = ConfigManager('/app/data/config.json')
                config = config_manager.load_config()
                
                # Find the imported repository
                imported_repo = None
                for repo in config.repositories:
                    if repo.name == import_data['name']:
                        imported_repo = repo
                        break
                
                if imported_repo:
                    print("✅ Repository found in configuration:")
                    print(f"   Name: {imported_repo.name}")
                    print(f"   URL: {imported_repo.url}")
                    print(f"   Monitor All Branches: {imported_repo.monitor_all_branches}")
                    print(f"   Branch Path: {imported_repo.branch_path}")
                    print(f"   Risk Aggressiveness: {imported_repo.risk_aggressiveness}")
                    print(f"   Product Doc Files: {imported_repo.product_documentation_files}")
                    
                    # Test branch expansion with the imported repository
                    print("\n🌿 Testing branch expansion with imported repository...")
                    from branch_expansion_service import BranchExpansionService
                    from repository_backends import get_backend_manager
                    
                    backend_manager = get_backend_manager()
                    branch_service = BranchExpansionService(backend_manager)
                    
                    if imported_repo.monitor_all_branches:
                        expanded_repos = branch_service._expand_repository(imported_repo, config)
                        print(f"✅ Expanded into {len(expanded_repos)} branch configs:")
                        for branch_repo in expanded_repos:
                            print(f"   - {branch_repo.name} (branch: {branch_repo.branch_path})")
                    else:
                        print(f"📍 Single branch monitoring: {imported_repo.branch_path}")
                    
                    # Clean up - remove the test repository
                    config.repositories = [r for r in config.repositories if r.id != imported_repo.id]
                    config_manager.save_config(config)
                    print("\n🧹 Test repository cleaned up")
                    
                else:
                    print("❌ Repository not found in configuration after import")
                    
            else:
                print(f"❌ Import failed: {result.get('message')}")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing import endpoint: {e}")

def test_import_dialog_scenarios():
    """Test different import dialog scenarios"""
    print(f"\n🎭 Testing Import Dialog Scenarios")
    print("=" * 50)
    
    scenarios = [
        {
            'name': 'Single Branch Import',
            'data': {
                'name': 'single_branch_test',
                'url': 'http://sundc:81/svn/reposense_branch_tag_test/trunk',
                'username': 'fvaneijk',
                'password': 'ankeanke',
                'monitor_all_branches': False,
                'branch_path': 'trunk',
                'risk_aggressiveness': 'BALANCED'
            }
        },
        {
            'name': 'Multi-Branch Import',
            'data': {
                'name': 'multi_branch_test',
                'url': 'http://sundc:81/svn/reposense_branch_tag_test',
                'username': 'fvaneijk',
                'password': 'ankeanke',
                'monitor_all_branches': True,
                'branch_path': '',  # Not used when monitor_all_branches=True
                'risk_aggressiveness': 'HIGH'
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🧪 Testing: {scenario['name']}")
        print("-" * 30)
        
        import_data = {
            'last_revision': 0,
            'product_documentation_files': [],
            'risk_description': f"Test scenario: {scenario['name']}",
            **scenario['data']
        }
        
        try:
            response = requests.post(
                'http://localhost:5000/repositories/import',
                json=import_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ {scenario['name']} successful")
                    
                    # Verify configuration
                    config_manager = ConfigManager('/app/data/config.json')
                    config = config_manager.load_config()
                    
                    imported_repo = None
                    for repo in config.repositories:
                        if repo.name == import_data['name']:
                            imported_repo = repo
                            break
                    
                    if imported_repo:
                        print(f"   Monitor All Branches: {imported_repo.monitor_all_branches}")
                        print(f"   Branch Path: {imported_repo.branch_path}")
                        
                        # Clean up
                        config.repositories = [r for r in config.repositories if r.id != imported_repo.id]
                        config_manager.save_config(config)
                        print(f"   🧹 Cleaned up")
                    
                else:
                    print(f"❌ {scenario['name']} failed: {result.get('message')}")
            else:
                print(f"❌ {scenario['name']} HTTP error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error in {scenario['name']}: {e}")

def test_api_integration():
    """Test API integration for import dialog"""
    print(f"\n🌐 Testing API Integration")
    print("=" * 30)
    
    try:
        # Test branch summary endpoint
        response = requests.get('http://localhost:5000/api/branch-summary', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Branch summary endpoint working")
            print(f"   Repositories in summary: {len(data)}")
        else:
            print(f"❌ Branch summary endpoint failed: {response.status_code}")
            
        # Test status endpoint
        response = requests.get('http://localhost:5000/api/status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status endpoint working")
            if 'branch_summary' in data:
                print(f"   Branch summary included in status: ✅")
            else:
                print(f"   Branch summary missing from status: ❌")
        else:
            print(f"❌ Status endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing API integration: {e}")

if __name__ == "__main__":
    test_import_endpoint()
    test_import_dialog_scenarios()
    test_api_integration()
    print(f"\n🎉 Repository import dialog integration tests completed!")
    print(f"The import dialog now fully supports multi-branch monitoring! 🚀")
