#!/usr/bin/env python3
"""
Test that fresh documents are processed with specialized models
"""

import logging
import tempfile
from datetime import datetime
from pathlib import Path
from document_processor import DocumentProcessor
from document_database import DocumentDatabase
from monitor_service import MonitorService

def setup_logging():
    """Setup logging to see model usage"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def create_test_document():
    """Create a test document that should trigger specialized model usage"""
    content = """# Revision 999 - OAuth2 Security Enhancement

**Repository:** reposense_ai  
**Author:** test_user  
**Date:** 2025-08-11T11:20:00Z  
**Message:** Implement OAuth2 authentication with JWT tokens for enhanced security

## Summary

This revision introduces a comprehensive OAuth2 authentication system with JWT token validation. The implementation includes secure token generation, validation middleware, and enhanced user session management.

## Code Review Recommendation

**RECOMMENDED** - This change introduces critical security enhancements that require thorough review.

**Priority:** HIGH - Authentication changes affect system security and require careful validation.

## Impact Assessment

- **Security:** HIGH - New authentication mechanism with token-based validation
- **Performance:** MEDIUM - Additional token validation overhead in requests  
- **Compatibility:** LOW - Backward compatible API with existing authentication
- **Database:** MEDIUM - New user session and token storage tables

## Risk Level

**HIGH RISK** - Authentication system changes require extensive testing and security review.

## Documentation Impact

**YES** - API documentation requires updates for new OAuth2 authentication flow and JWT token usage.

## Changes Made

- Implemented OAuth2 authorization server with JWT tokens
- Added user authentication middleware with token validation
- Created secure token storage and session management
- Enhanced security logging and audit trails
- Updated API endpoints for OAuth2 compatibility

## Testing Requirements

- Security penetration testing required
- Load testing for token validation performance
- Integration testing with existing user systems
"""
    
    return content

def test_fresh_document_processing():
    """Test that fresh documents use specialized models"""
    print("🧪 Testing Fresh Document Processing with Specialized Models")
    print("=" * 65)
    
    setup_logging()
    
    # Initialize monitor service
    monitor_service = MonitorService("data/config.json")
    
    print(f"📋 Configuration:")
    print(f"  Default model: {monitor_service.config.ollama_model}")
    print(f"  Risk assessment model: {monitor_service.config.ollama_model_risk_assessment}")
    print(f"  Code review model: {monitor_service.config.ollama_model_code_review}")
    
    # Create DocumentProcessor with Ollama client (the fixed version)
    processor = DocumentProcessor(
        monitor_service.config.output_dir,
        "/app/data/documents.db",
        monitor_service.config_manager,
        monitor_service.ollama_client  # This is the key fix!
    )
    
    print(f"\n🔍 Processor setup:")
    print(f"  Has Ollama client: {processor.ollama_client is not None}")
    print(f"  MetadataExtractor has Ollama client: {processor.metadata_extractor.ollama_client is not None}")
    
    # Create a temporary test document
    test_content = create_test_document()
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='_revision_999.md', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        print(f"\n📝 Created test document: {temp_file_path}")
        print(f"📊 Content includes: OAuth2, JWT, HIGH RISK, authentication")
        print(f"🎯 Expected: Should use specialized model (gpt-oss:20b)")
        
        # Process the document
        print(f"\n🔄 Processing document...")
        processor._process_document(temp_file_path)
        
        # Check if document was processed and stored with correct model
        db = DocumentDatabase("/app/data/documents.db")
        docs = db.get_documents(limit=1)  # Get most recent document
        
        if docs:
            doc = docs[0]
            print(f"\n✅ Document processed successfully!")
            print(f"📋 Document details:")
            print(f"  Filename: {doc.filename}")
            print(f"  AI Model Used: {doc.ai_model_used or 'Not set'}")
            print(f"  Risk Level: {doc.risk_level or 'Not set'}")
            print(f"  Code Review: {doc.code_review_recommended}")
            print(f"  Doc Impact: {doc.documentation_impact}")
            
            if doc.ai_model_used and doc.ai_model_used != monitor_service.config.ollama_model:
                print(f"\n🎉 SUCCESS: Specialized model used!")
                print(f"🎯 Used: {doc.ai_model_used}")
                print(f"📊 Default would have been: {monitor_service.config.ollama_model}")
                return True
            elif doc.ai_model_used == monitor_service.config.ollama_model:
                print(f"\n⚠️  WARNING: Default model used instead of specialized")
                print(f"🔍 Used: {doc.ai_model_used}")
                print(f"🎯 Expected specialized: {monitor_service.config.ollama_model_risk_assessment}")
                return False
            else:
                print(f"\n❌ ISSUE: No AI model recorded")
                return False
        else:
            print(f"\n❌ ERROR: No documents found in database")
            return False
            
    except Exception as e:
        print(f"\n❌ ERROR during processing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up temporary file
        try:
            Path(temp_file_path).unlink()
        except:
            pass

if __name__ == "__main__":
    success = test_fresh_document_processing()
    
    print("\n" + "=" * 65)
    if success:
        print("🎉 Fresh document processing is working with specialized models!")
        print("🌐 The web interface should now show the correct AI model.")
        print("🔄 Restart the web application to apply all fixes.")
    else:
        print("❌ Fresh document processing still needs debugging.")
        print("🔍 Check the logs above for specific issues.")
