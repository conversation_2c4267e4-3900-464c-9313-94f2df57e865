#!/usr/bin/env python3
"""
Debug the rescan issue - why documents aren't being generated
"""

import sys
import os
sys.path.append('/app')

from config_manager import ConfigManager
from unified_document_processor import UnifiedDocumentProcessor
from models import CommitInfo, RepositoryConfig
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_rescan_issue():
    """Debug why rescan isn't creating documents"""
    print("🔍 Debugging Rescan Document Issue")
    print("=" * 60)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Get repository config
        repo_config = config.get_repository_by_id('c42f3018-3ffc-4434-91df-e1d1d892bb9e')
        if not repo_config:
            print("❌ Repository not found")
            return False
        
        print(f"1. Repository: {repo_config.name}")
        print(f"   URL: {repo_config.url}")
        print(f"   Type: {repo_config.type}")
        print(f"   Aggressiveness: {repo_config.risk_aggressiveness}")
        
        # Create unified processor
        print("\n2. Creating UnifiedDocumentProcessor...")
        unified_processor = UnifiedDocumentProcessor(
            output_dir="/app/data/output",
            config_manager=config_manager
        )
        print("   ✅ UnifiedDocumentProcessor created")
        
        # Create CommitInfo for revision 17
        print("\n3. Creating CommitInfo for revision 17...")
        commit_info = CommitInfo(
            repository_id=repo_config.id,
            repository_name=repo_config.name,
            revision="17",
            author="fvaneijk",
            date=datetime.now().isoformat(),
            message="add",
            changed_paths=[
                "/.vscode/settings.json",
                "/tests/test_memory.cpp"
            ],
            diff="@@ -0,0 +1,10 @@\n+// Test diff content\n+This is a test diff for debugging"
        )
        
        print(f"   ✅ CommitInfo created:")
        print(f"      Repository: {commit_info.repository_name}")
        print(f"      Revision: {commit_info.revision}")
        print(f"      Author: {commit_info.author}")
        print(f"      Files: {len(commit_info.changed_paths)}")
        
        # Test the process_commit method
        print("\n4. Testing process_commit method...")
        print("   📝 Calling unified_processor.process_commit()...")
        
        try:
            success = unified_processor.process_commit(
                commit_info=commit_info,
                repository_config=repo_config,
                documentation="",  # Empty for rescan - will be regenerated
                priority=10  # High priority for rescan
            )
            
            print(f"   📊 Process result: {success}")
            
            if success:
                print("   ✅ process_commit returned True")
                
                # Check if file was created
                # repo_config.name now includes branch path (e.g., "reposense_cpp_test/trunk")
                expected_file = f"/app/data/output/repositories/{repo_config.name}/docs/revision_17.md"
                if os.path.exists(expected_file):
                    file_size = os.path.getsize(expected_file)
                    print(f"   ✅ Document file created: {expected_file}")
                    print(f"   📏 File size: {file_size} bytes")
                    
                    # Show first few lines
                    with open(expected_file, 'r') as f:
                        content = f.read(500)
                        print(f"   📄 Content preview:\n{content[:200]}...")
                else:
                    print(f"   ❌ Document file NOT created: {expected_file}")
                    
                    # Check if directory exists
                    doc_dir = os.path.dirname(expected_file)
                    if os.path.exists(doc_dir):
                        files = os.listdir(doc_dir)
                        print(f"   📁 Directory exists with {len(files)} files:")
                        for f in files[:5]:  # Show first 5 files
                            print(f"      - {f}")
                    else:
                        print(f"   ❌ Directory doesn't exist: {doc_dir}")
            else:
                print("   ❌ process_commit returned False")
                
        except Exception as e:
            print(f"   ❌ Error calling process_commit: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n" + "=" * 60)
        print("🔍 RESCAN DEBUG SUMMARY")
        print("=" * 60)
        
        if success:
            print("✅ UnifiedProcessor: process_commit returned True")
            if os.path.exists(expected_file):
                print("✅ File Creation: Document file was created")
                print("🎯 Issue: RESOLVED - Rescan should work")
            else:
                print("❌ File Creation: Document file was NOT created")
                print("🎯 Issue: process_commit succeeds but doesn't create file")
        else:
            print("❌ UnifiedProcessor: process_commit returned False")
            print("🎯 Issue: process_commit method failing")
        
        return success
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_rescan_issue()
    exit(0 if success else 1)
