#!/usr/bin/env python3
"""
Test script to verify that revision handling works correctly with non-sequential revisions.
This script tests the new revision handling logic in both the SVN backend and web interface.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from reposense_ai.models import RepositoryConfig
from reposense_ai.repository_backends.svn_backend import SVNBackend
from reposense_ai.config_manager import Config<PERSON>anager

def test_svn_revision_methods():
    """Test the SVN backend revision methods"""
    print("Testing SVN Backend Revision Methods")
    print("=" * 50)
    
    # Create a test repository config (you'll need to adjust this to your actual repo)
    test_repo = RepositoryConfig(
        id="test-repo",
        name="Test Repository",
        url="https://your-svn-repo-url/trunk",  # Replace with actual URL
        username="your-username",  # Replace with actual username
        password="your-password",  # Replace with actual password
        enabled=True
    )
    
    # Create config manager and SVN backend
    config = ConfigManager()
    svn_backend = SVNBackend(config)
    
    try:
        print(f"Testing repository: {test_repo.name}")
        print(f"URL: {test_repo.url}")
        
        # Test get_latest_revision
        print("\n1. Testing get_latest_revision...")
        latest = svn_backend.get_latest_revision(test_repo)
        print(f"Latest revision: {latest}")
        
        if not latest:
            print("❌ Could not get latest revision - check repository URL and credentials")
            return False
        
        # Test get_all_available_revisions
        print("\n2. Testing get_all_available_revisions (last 20)...")
        available_revisions = svn_backend.get_all_available_revisions(test_repo, limit=20)
        print(f"Found {len(available_revisions)} available revisions:")
        for i, rev in enumerate(available_revisions[:10]):  # Show first 10
            print(f"  {i+1}. r{rev}")
        if len(available_revisions) > 10:
            print(f"  ... and {len(available_revisions) - 10} more")
        
        # Test get_revision_range with actual revisions
        if len(available_revisions) >= 5:
            print("\n3. Testing get_revision_range with actual revisions...")
            # Get a range from the middle of available revisions
            start_idx = len(available_revisions) // 2
            end_idx = min(start_idx + 5, len(available_revisions) - 1)
            
            start_rev = available_revisions[end_idx]  # Remember: available_revisions is newest first
            end_rev = available_revisions[start_idx]
            
            print(f"Testing range: r{start_rev} to r{end_rev}")
            range_revisions = svn_backend.get_revision_range(test_repo, start_rev, end_rev)
            print(f"Found {len(range_revisions)} revisions in range:")
            for rev in range_revisions:
                print(f"  r{rev}")
        
        print("\n✅ SVN backend tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing SVN backend: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_revision_count_logic():
    """Test the revision count logic for bulk scanning"""
    print("\n\nTesting Revision Count Logic")
    print("=" * 50)
    
    # Simulate available revisions with gaps (like a real SVN repo might have)
    mock_available_revisions = ['100', '98', '95', '92', '90', '88', '85', '82', '80', '78']
    
    print("Mock available revisions (newest first):")
    print(mock_available_revisions)
    
    # Test "last 5 revisions" logic
    revision_count = 5
    revisions_to_scan = mock_available_revisions[:revision_count]
    print(f"\nLast {revision_count} revisions to scan:")
    print(revisions_to_scan)
    
    # Calculate range (chronological order)
    revisions_chrono = sorted(revisions_to_scan, key=int)
    start_revision = int(revisions_chrono[0])
    end_revision = int(revisions_chrono[-1])
    
    print(f"Calculated range: r{start_revision} to r{end_revision}")
    print(f"This will scan {len(revisions_to_scan)} actual revisions, not {end_revision - start_revision + 1} sequential ones")
    
    print("\n✅ Revision count logic test completed!")

if __name__ == "__main__":
    print("RepoSense AI - Revision Handling Test")
    print("=" * 60)
    
    print("This test verifies that the system correctly handles non-sequential revisions.")
    print("You'll need to update the test repository configuration in the script.")
    print()
    
    # Test revision count logic (always works)
    test_revision_count_logic()
    
    # Test SVN backend (requires actual repository)
    print("\n" + "=" * 60)
    print("To test the SVN backend, update the repository configuration in this script")
    print("and uncomment the following line:")
    print("# test_svn_revision_methods()")
    
    # Uncomment this line and update the repository config above to test with real repo
    # test_svn_revision_methods()
