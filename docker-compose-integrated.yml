# RepoSense AI Integration for Larger Docker Compose Setup
# This file shows how to integrate RepoSense AI into your existing docker-compose.yml
# Copy the reposense-ai service definition into your main docker-compose.yml

version: '3.8'

services:
  # Add this service to your existing docker-compose.yml
  reposense-ai:
    build:
      context: ./reposense_ai
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      # Essential volumes - always mounted
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
      # Development volumes - mount source code for hot reloads (optional)
      - ./reposense_ai:/app
      # Exclude node_modules and other build artifacts to avoid conflicts
      - /app/node_modules
      - /app/.git
    environment:
      # Web interface settings
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      # Development settings (override via .env file)
      - REPOSENSE_AI_LOG_LEVEL=${REPOSENSE_AI_LOG_LEVEL:-INFO}
      - REPOSENSE_AI_DB_DEBUG=${REPOSENSE_AI_DB_DEBUG:-false}
      # Integration with your Ollama service
      - OLLAMA_BASE_URL=http://ollama:11434
      # Optional model override
      - OLLAMA_MODEL=${REPOSENSE_AI_MODEL:-qwen3:14b}
    networks:
      - ollama-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      - ollama

# Your existing networks section should include:
networks:
  ollama-network:
    driver: bridge

# Your existing volumes section (if any) can remain unchanged
# RepoSense AI uses bind mounts for data persistence
