#!/usr/bin/env python3

import sqlite3

def check_repository_names():
    conn = sqlite3.connect('/app/data/documents.db')
    cursor = conn.cursor()
    
    # Check what repository_name values exist
    cursor.execute('SELECT DISTINCT repository_name FROM documents ORDER BY repository_name')
    repo_names = cursor.fetchall()
    
    print('Distinct repository names in database:')
    for name in repo_names:
        print(f'  "{name[0]}"')
    
    # Check specific documents
    cursor.execute('SELECT id, repository_name FROM documents WHERE revision IN (25, 26) ORDER BY revision')
    docs = cursor.fetchall()
    
    print('\nDocuments for revisions 25 and 26:')
    for doc in docs:
        print(f'  ID: {doc[0]}')
        print(f'  Repo Name: "{doc[1]}"')
        print()
    
    # Update if needed
    uuid_name = 'a3a1ffba-d176-4021-89c7-def023be10e1'
    cursor.execute('SELECT COUNT(*) FROM documents WHERE repository_name = ?', (uuid_name,))
    count = cursor.fetchone()[0]
    
    if count > 0:
        print(f'Found {count} documents with UUID as repository_name')
        cursor.execute('UPDATE documents SET repository_name = ? WHERE repository_name = ?', 
                      ('reposense_ai', uuid_name))
        affected = cursor.rowcount
        conn.commit()
        print(f'Updated {affected} documents')
    else:
        print('No documents found with UUID as repository_name')
    
    conn.close()

if __name__ == '__main__':
    check_repository_names()
