#!/usr/bin/env python3
"""
Analyze if the system is truly utilizing the different specialized AI models
"""

import sys
import os
import json
import requests
sys.path.append('/app')

def analyze_current_model_configuration():
    """Analyze current specialized model configuration"""
    print("🤖 Analyzing Current Specialized Model Configuration")
    print("=" * 60)
    
    try:
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        models = {
            'Default Model': config.get('ollama_model', 'Not set'),
            'Documentation Model': config.get('ollama_model_documentation', 'Use default model'),
            'Code Review Model': config.get('ollama_model_code_review', 'Use default model'),
            'Risk Assessment Model': config.get('ollama_model_risk_assessment', 'Use default model')
        }
        
        print("📊 Current Model Configuration:")
        specialized_count = 0
        for model_type, model_value in models.items():
            if model_value and model_value != 'Use default model' and model_value != 'Not set':
                print(f"   ✅ {model_type}: {model_value}")
                if 'Default' not in model_type:
                    specialized_count += 1
            else:
                print(f"   ❌ {model_type}: {model_value}")
        
        print(f"\n📈 Specialization Status:")
        print(f"   🎯 Specialized models configured: {specialized_count}/3")
        print(f"   📊 Specialization level: {specialized_count/3*100:.1f}%")
        
        if specialized_count == 0:
            print("   ⚠️  NO specialized models configured - all using default")
        elif specialized_count < 3:
            print("   ⚠️  PARTIAL specialization - some models using default")
        else:
            print("   🎉 FULL specialization - all models configured")
            
        return models, specialized_count
        
    except Exception as e:
        print(f"❌ Error analyzing model configuration: {e}")
        return {}, 0

def analyze_model_usage_in_code():
    """Analyze where specialized models are actually used in the code"""
    print(f"\n🔍 Analyzing Specialized Model Usage in Code")
    print("=" * 50)
    
    usage_locations = {
        'Documentation Model (ollama_model_documentation)': [
            "✅ document_service.py - _generate_product_documentation_suggestions()",
            "✅ ollama_client.py - generate_documentation() for revision docs",
            "✅ ollama_client.py - generate_email_content() for email generation",
            "📍 Used for: Product documentation analysis and email content"
        ],
        'Code Review Model (ollama_model_code_review)': [
            "✅ document_service.py - _extract_metadata_with_llm() (fallback)",
            "✅ metadata_extractor.py - _extract_metadata_with_llm() (fallback)",
            "✅ historical_scanner.py - _extract_metadata_with_llm() (fallback)",
            "📍 Used for: Code review analysis when risk model not available"
        ],
        'Risk Assessment Model (ollama_model_risk_assessment)': [
            "✅ document_service.py - _extract_metadata_with_llm() (primary)",
            "✅ metadata_extractor.py - _extract_metadata_with_llm() (primary)",
            "✅ historical_scanner.py - _extract_metadata_with_llm() (primary)",
            "📍 Used for: Risk assessment and metadata extraction (primary choice)"
        ]
    }
    
    print("📋 Model Usage Locations:")
    for model_type, locations in usage_locations.items():
        print(f"\n🎯 {model_type}:")
        for location in locations:
            print(f"   {location}")

def analyze_model_selection_logic():
    """Analyze the model selection logic"""
    print(f"\n🧠 Analyzing Model Selection Logic")
    print("=" * 40)
    
    selection_patterns = {
        'Documentation Tasks': {
            'pattern': 'Uses ollama_model_documentation if configured, else default',
            'locations': [
                'Product documentation suggestions',
                'Revision documentation generation',
                'Email content generation'
            ],
            'fallback': 'Default model (ollama_model)'
        },
        'Risk Assessment & Metadata': {
            'pattern': 'Prefers ollama_model_risk_assessment, falls back to ollama_model_code_review, then default',
            'locations': [
                'Document metadata extraction',
                'Risk level assessment',
                'Code review recommendations',
                'Historical scanning analysis'
            ],
            'fallback': 'Code review model → Default model'
        },
        'General AI Tasks': {
            'pattern': 'Uses default model (ollama_model)',
            'locations': [
                'General content generation',
                'Section ranking and analysis',
                'Fallback for all specialized tasks'
            ],
            'fallback': 'No fallback - this IS the fallback'
        }
    }
    
    print("🎯 Model Selection Patterns:")
    for task_type, details in selection_patterns.items():
        print(f"\n📊 {task_type}:")
        print(f"   🔄 Pattern: {details['pattern']}")
        print(f"   📍 Used for:")
        for location in details['locations']:
            print(f"      • {location}")
        print(f"   🔄 Fallback: {details['fallback']}")

def test_actual_model_utilization():
    """Test if specialized models are actually being used"""
    print(f"\n🧪 Testing Actual Model Utilization")
    print("=" * 40)
    
    try:
        # Check current configuration
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        models = {
            'default': config.get('ollama_model'),
            'documentation': config.get('ollama_model_documentation'),
            'code_review': config.get('ollama_model_code_review'),
            'risk_assessment': config.get('ollama_model_risk_assessment')
        }
        
        print("📊 Model Configuration Analysis:")
        for model_type, model_value in models.items():
            if model_value and model_value != 'Use default model':
                print(f"   ✅ {model_type}: {model_value}")
            else:
                print(f"   ❌ {model_type}: Not configured (using default)")
        
        # Analyze utilization based on configuration
        print(f"\n🎯 Utilization Analysis:")
        
        if not models['documentation']:
            print("   ❌ Documentation tasks: Using default model (no specialization)")
        else:
            print(f"   ✅ Documentation tasks: Using {models['documentation']}")
        
        if not models['risk_assessment'] and not models['code_review']:
            print("   ❌ Risk assessment: Using default model (no specialization)")
        elif models['risk_assessment']:
            print(f"   ✅ Risk assessment: Using {models['risk_assessment']}")
        else:
            print(f"   ⚠️  Risk assessment: Using code review model {models['code_review']} (partial specialization)")
        
        if not models['code_review']:
            print("   ❌ Code review fallback: Using default model (no specialization)")
        else:
            print(f"   ✅ Code review fallback: Using {models['code_review']}")
        
        # Calculate actual utilization
        specialized_tasks = 0
        total_tasks = 3
        
        if models['documentation']:
            specialized_tasks += 1
        if models['risk_assessment']:
            specialized_tasks += 1
        elif models['code_review']:
            specialized_tasks += 0.5  # Partial specialization
        
        utilization_percent = (specialized_tasks / total_tasks) * 100
        
        print(f"\n📈 Actual Utilization:")
        print(f"   🎯 Specialized model utilization: {utilization_percent:.1f}%")
        
        if utilization_percent == 0:
            print("   ❌ NO specialized models being used - all tasks use default model")
        elif utilization_percent < 50:
            print("   ⚠️  LOW specialization - mostly using default model")
        elif utilization_percent < 100:
            print("   ⚠️  PARTIAL specialization - some tasks specialized")
        else:
            print("   🎉 FULL specialization - all tasks using specialized models")
            
        return utilization_percent
        
    except Exception as e:
        print(f"❌ Error testing model utilization: {e}")
        return 0

def analyze_model_effectiveness():
    """Analyze if using specialized models would be more effective"""
    print(f"\n💡 Analyzing Specialized Model Effectiveness")
    print("=" * 50)
    
    task_analysis = {
        'Documentation Tasks': {
            'current_model': 'Default (smollm2:latest)',
            'optimal_model': 'Larger model with better writing capabilities',
            'benefits': [
                'Better technical writing quality',
                'More accurate user-facing impact analysis',
                'Improved email content generation',
                'Better product documentation suggestions'
            ],
            'recommended_models': ['llama3.1:8b', 'qwen2.5:7b', 'mistral:7b']
        },
        'Risk Assessment Tasks': {
            'current_model': 'Default (smollm2:latest)',
            'optimal_model': 'Model trained on security and risk analysis',
            'benefits': [
                'More accurate risk level assessment',
                'Better security vulnerability detection',
                'Improved confidence scoring',
                'More nuanced risk reasoning'
            ],
            'recommended_models': ['llama3.1:8b', 'qwen2.5:7b', 'codellama:7b']
        },
        'Code Review Tasks': {
            'current_model': 'Default (smollm2:latest)',
            'optimal_model': 'Code-specialized model',
            'benefits': [
                'Better code quality analysis',
                'More accurate review recommendations',
                'Improved code pattern recognition',
                'Better technical debt detection'
            ],
            'recommended_models': ['codellama:7b', 'deepseek-coder:6.7b', 'qwen2.5-coder:7b']
        }
    }
    
    print("🎯 Task-Specific Analysis:")
    for task_type, analysis in task_analysis.items():
        print(f"\n📊 {task_type}:")
        print(f"   🔄 Current: {analysis['current_model']}")
        print(f"   🎯 Optimal: {analysis['optimal_model']}")
        print(f"   ✅ Benefits:")
        for benefit in analysis['benefits']:
            print(f"      • {benefit}")
        print(f"   💡 Recommended: {', '.join(analysis['recommended_models'])}")

def check_available_models():
    """Check what models are actually available on the Ollama server"""
    print(f"\n🌐 Checking Available Models on Ollama Server")
    print("=" * 50)
    
    try:
        # Get Ollama host from config
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        ollama_host = config.get('ollama_host', 'http://localhost:11434')
        print(f"📍 Ollama host: {ollama_host}")
        
        # Try to get available models
        response = requests.get(f"{ollama_host}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            print(f"✅ Connected to Ollama server")
            print(f"📊 Available models: {len(models)}")
            
            if models:
                print(f"\n📋 Available Models:")
                for model in models:
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 0)
                    size_gb = size / (1024**3) if size else 0
                    print(f"   📦 {name} ({size_gb:.1f}GB)")
                
                # Analyze model suitability
                print(f"\n🎯 Model Suitability Analysis:")
                
                current_default = config.get('ollama_model', 'smollm2:latest')
                print(f"   🔄 Current default: {current_default}")
                
                # Check for code-specialized models
                code_models = [m['name'] for m in models if any(keyword in m['name'].lower() for keyword in ['code', 'coder', 'llama'])]
                if code_models:
                    print(f"   💻 Code-specialized models available: {', '.join(code_models[:3])}")
                else:
                    print(f"   ❌ No obvious code-specialized models found")
                
                # Check for larger models (better for documentation)
                large_models = [m['name'] for m in models if m.get('size', 0) > 4 * 1024**3]  # > 4GB
                if large_models:
                    print(f"   📚 Larger models (better for docs): {', '.join(large_models[:3])}")
                else:
                    print(f"   ⚠️  No large models found - may limit documentation quality")
                
                return models
            else:
                print("   ❌ No models found on server")
                return []
        else:
            print(f"❌ Cannot connect to Ollama server: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error checking available models: {e}")
        return []

def analyze_utilization_reality():
    """Analyze the reality of specialized model utilization"""
    print(f"\n🔍 Reality Check: Are Specialized Models Actually Used?")
    print("=" * 60)
    
    try:
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        # Check current configuration
        default_model = config.get('ollama_model', 'smollm2:latest')
        doc_model = config.get('ollama_model_documentation')
        code_model = config.get('ollama_model_code_review')
        risk_model = config.get('ollama_model_risk_assessment')
        
        print("🎯 UTILIZATION REALITY:")
        
        # Documentation tasks
        if doc_model and doc_model != 'Use default model':
            print(f"   ✅ Documentation tasks: Using specialized model '{doc_model}'")
            print(f"      📍 Used in: Product docs, revision docs, email generation")
        else:
            print(f"   ❌ Documentation tasks: Using default model '{default_model}'")
            print(f"      📍 Impact: Lower quality documentation and emails")
        
        # Risk assessment tasks
        if risk_model and risk_model != 'Use default model':
            print(f"   ✅ Risk assessment: Using specialized model '{risk_model}'")
            print(f"      📍 Used in: Metadata extraction, risk analysis, security assessment")
        elif code_model and code_model != 'Use default model':
            print(f"   ⚠️  Risk assessment: Using code review model '{code_model}' (fallback)")
            print(f"      📍 Impact: Partial specialization for risk assessment")
        else:
            print(f"   ❌ Risk assessment: Using default model '{default_model}'")
            print(f"      📍 Impact: Lower quality risk assessment and security analysis")
        
        # Code review tasks
        if code_model and code_model != 'Use default model':
            print(f"   ✅ Code review: Using specialized model '{code_model}'")
            print(f"      📍 Used in: Code analysis, review recommendations")
        else:
            print(f"   ❌ Code review: Using default model '{default_model}'")
            print(f"      📍 Impact: Lower quality code review analysis")
        
        # Overall assessment
        specialized_count = sum([
            1 if doc_model and doc_model != 'Use default model' else 0,
            1 if risk_model and risk_model != 'Use default model' else 0,
            0.5 if code_model and code_model != 'Use default model' and not risk_model else 0
        ])
        
        print(f"\n📊 Overall Utilization:")
        utilization = (specialized_count / 3) * 100
        print(f"   🎯 Actual specialization: {utilization:.1f}%")
        
        if utilization == 0:
            print("   ❌ RESULT: NO specialized models being used")
            print("   💡 RECOMMENDATION: Configure specialized models for better results")
        elif utilization < 50:
            print("   ⚠️  RESULT: LOW specialization")
            print("   💡 RECOMMENDATION: Configure more specialized models")
        elif utilization < 100:
            print("   ⚠️  RESULT: PARTIAL specialization")
            print("   💡 RECOMMENDATION: Complete the specialization setup")
        else:
            print("   🎉 RESULT: FULL specialization achieved")
        
        return utilization
        
    except Exception as e:
        print(f"❌ Error analyzing utilization reality: {e}")
        return 0

def recommend_model_configuration():
    """Recommend optimal model configuration"""
    print(f"\n💡 Recommended Model Configuration")
    print("=" * 40)
    
    try:
        # Get available models
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        ollama_host = config.get('ollama_host', 'http://localhost:11434')
        response = requests.get(f"{ollama_host}/api/tags", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            available_models = [m['name'] for m in data.get('models', [])]
            
            print("🎯 Recommended Configuration Based on Available Models:")
            
            recommendations = {
                'Documentation Model': {
                    'purpose': 'Product documentation, emails, user-facing content',
                    'preferred': ['llama3.1:8b', 'qwen2.5:7b', 'mistral:7b'],
                    'current': config.get('ollama_model_documentation'),
                    'why': 'Better writing quality, user-friendly language, comprehensive analysis'
                },
                'Risk Assessment Model': {
                    'purpose': 'Security analysis, risk evaluation, metadata extraction',
                    'preferred': ['llama3.1:8b', 'qwen2.5:7b', 'codellama:7b'],
                    'current': config.get('ollama_model_risk_assessment'),
                    'why': 'Better reasoning, security awareness, risk pattern recognition'
                },
                'Code Review Model': {
                    'purpose': 'Code analysis, review recommendations, technical assessment',
                    'preferred': ['codellama:7b', 'deepseek-coder:6.7b', 'qwen2.5-coder:7b'],
                    'current': config.get('ollama_model_code_review'),
                    'why': 'Code-specific training, better technical analysis, programming patterns'
                }
            }
            
            for model_type, details in recommendations.items():
                print(f"\n📊 {model_type}:")
                print(f"   🎯 Purpose: {details['purpose']}")
                print(f"   🔄 Current: {details['current'] or 'Not configured'}")
                
                # Find best available model
                best_available = None
                for preferred in details['preferred']:
                    if preferred in available_models:
                        best_available = preferred
                        break
                
                if best_available:
                    print(f"   ✅ Recommended: {best_available} (available)")
                    if details['current'] != best_available:
                        print(f"   💡 Action: Configure {best_available} for better results")
                else:
                    print(f"   ⚠️  Recommended: {details['preferred'][0]} (not available)")
                    print(f"   💡 Action: Install recommended model or use best available")
                
                print(f"   📝 Why: {details['why']}")
        else:
            print("❌ Cannot connect to Ollama to check available models")
            
    except Exception as e:
        print(f"❌ Error generating recommendations: {e}")

def show_configuration_impact():
    """Show the impact of proper specialized model configuration"""
    print(f"\n📈 Impact of Specialized Model Configuration")
    print("=" * 50)
    
    impacts = {
        'Current State (Default Model Only)': {
            'quality': 'Basic - One model handles all tasks',
            'accuracy': 'Limited - Generic responses for specialized tasks',
            'performance': 'Consistent but not optimized',
            'user_experience': 'Functional but could be much better'
        },
        'With Specialized Models': {
            'quality': 'High - Each model optimized for its task',
            'accuracy': 'Improved - Task-specific training and capabilities',
            'performance': 'Optimized - Right model for right task',
            'user_experience': 'Professional - Noticeably better results'
        }
    }
    
    for state, metrics in impacts.items():
        print(f"\n🎯 {state}:")
        for metric, value in metrics.items():
            print(f"   📊 {metric.title()}: {value}")
    
    print(f"\n🎯 Specific Improvements with Specialization:")
    improvements = [
        "📚 Documentation: Better writing, clearer explanations, user-friendly language",
        "🔒 Risk Assessment: More accurate security analysis, better threat detection",
        "💻 Code Review: Improved code quality analysis, better technical recommendations",
        "📧 Email Content: More professional and informative notifications",
        "🎯 Overall: More accurate AI analysis across all system functions"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")

if __name__ == "__main__":
    models, specialized_count = analyze_current_model_configuration()
    analyze_model_usage_in_code()
    analyze_model_selection_logic()
    utilization = test_actual_model_utilization()
    check_available_models()
    recommend_model_configuration()
    show_configuration_impact()
    
    print(f"\n🎯 FINAL ANALYSIS:")
    print(f"   📊 Current specialization: {utilization:.1f}%")
    
    if utilization == 0:
        print(f"   ❌ VERDICT: NOT utilizing specialized models")
        print(f"   💡 All AI tasks are using the default model (smollm2:latest)")
        print(f"   🎯 RECOMMENDATION: Configure specialized models for better results")
    elif utilization < 100:
        print(f"   ⚠️  VERDICT: PARTIALLY utilizing specialized models")
        print(f"   💡 Some tasks specialized, others using default")
        print(f"   🎯 RECOMMENDATION: Complete the specialization setup")
    else:
        print(f"   🎉 VERDICT: FULLY utilizing specialized models")
        print(f"   💡 All AI tasks using appropriate specialized models")
    
    print(f"\n🚀 Visit http://localhost:5001/config to configure specialized models!")
