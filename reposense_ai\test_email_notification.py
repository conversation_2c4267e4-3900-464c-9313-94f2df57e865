#!/usr/bin/env python3
"""
Test email notifications by simulating a commit processing event
"""

import sys
import os
sys.path.append('/app')

from config_manager import ConfigManager
from email_service import EmailService
from models import CommitInfo, RepositoryConfig
from datetime import datetime
import uuid

def test_email_notification():
    """Test email notification by simulating a commit event"""
    print("📧 Testing Email Notifications with Simulated Commit")
    print("=" * 60)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print("1. Email configuration:")
        print(f"   📧 SMTP Host: {config.smtp_host}:{config.smtp_port}")
        print(f"   📤 From Email: {config.email_from}")
        print(f"   🔔 Send Emails: {config.send_emails}")
        print(f"   📮 Email Recipients: {config.email_recipients}")
        print(f"   👥 Users with notifications: {len([u for u in config.users if u.receive_all_notifications])}")
        
        if not config.send_emails:
            print("   ⚠️  Email sending is disabled!")
            return False
        
        # Create EmailService
        print("\n2. Initializing EmailService...")
        email_service = EmailService(config)
        print("   ✅ EmailService initialized")
        
        # Create a simulated repository config
        print("\n3. Creating simulated repository and commit...")
        
        # Create a test repository config
        test_repo = RepositoryConfig(
            id="test-repo-001",
            name="Test Repository",
            url="https://github.com/test/repo.git",
            type="git",
            enabled=True,
            email_recipients=[]  # Will use global recipients
        )
        
        # Create a simulated commit
        test_commit = CommitInfo(
            repository_id=test_repo.id,
            repository_name=test_repo.name,
            revision="abc123def456",
            author="Test Developer <<EMAIL>>",
            date=datetime.now().isoformat(),
            message="Add new feature for email testing\n\nThis commit adds email notification testing functionality to verify that the email system is working correctly with user management.",
            changed_paths=[
                "src/email_test.py",
                "docs/email_setup.md",
                "config/email_config.json"
            ],
            diff="@@ -0,0 +1,10 @@\n+# Email Testing\n+This is a test file for email notifications.\n+\n+def test_email():\n+    return True"
        )
        
        print(f"   📁 Repository: {test_repo.name}")
        print(f"   🔄 Commit: {test_commit.revision[:8]} by {test_commit.author}")
        print(f"   📝 Message: {test_commit.message.split(chr(10))[0]}")
        print(f"   📄 Files: {len(test_commit.changed_paths)} files changed")
        
        # Send email notification
        print("\n4. Sending email notification...")
        
        try:
            success = email_service.send_email(
                subject=f"Repository Update: {test_repo.name}",
                body=f"""
A new commit has been processed in {test_repo.name}:

Commit Details:
- Revision: {test_commit.revision}
- Author: {test_commit.author}
- Date: {test_commit.date}
- Message: {test_commit.message}

Changed Files:
{chr(10).join(f'  - {file}' for file in test_commit.changed_paths)}

This is a test email to verify that RepoSense AI email notifications are working correctly with the user management system.

Repository: {test_repo.name}
URL: {test_repo.url}
Type: {test_repo.type.upper()}

Email sent at: {datetime.now().isoformat()}
                """.strip(),
                commit=test_commit
            )
            
            if success:
                print("   ✅ Email notification sent successfully!")
            else:
                print("   ❌ Email notification failed!")
                return False
                
        except Exception as e:
            print(f"   ❌ Email sending failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Get recipient information
        print("\n5. Email recipient details:")
        recipients = config.get_all_recipients_for_repository(test_repo.id)
        print(f"   📧 Total recipients: {len(recipients)}")
        
        for recipient in recipients:
            # Check if this is a user email
            user = next((u for u in config.users if u.email == recipient), None)
            if user:
                print(f"   👤 {recipient} - {user.username} ({user.role.value})")
            else:
                print(f"   📧 {recipient} - Global recipient")
        
        print("\n" + "=" * 60)
        print("📊 EMAIL NOTIFICATION TEST SUMMARY")
        print("=" * 60)
        
        print(f"✅ Email Service: Working")
        print(f"📧 Recipients: {len(recipients)} total")
        print(f"👥 User Recipients: {len([u for u in config.users if u.receive_all_notifications])}")
        print(f"📮 Global Recipients: {len(config.email_recipients)}")
        print(f"🔔 Notification Sent: Repository update email")
        
        print(f"\n🌐 Next Steps:")
        print(f"1. Open MailHog: http://localhost:8025")
        print(f"2. Look for 'Repository Update: Test Repository' email")
        print(f"3. Verify all {len(recipients)} recipients received the email")
        print(f"4. Check email content and formatting")
        
        return True
        
    except Exception as e:
        print(f"❌ Email notification test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_email_notification()
    exit(0 if success else 1)
