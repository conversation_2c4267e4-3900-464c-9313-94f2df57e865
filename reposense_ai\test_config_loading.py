#!/usr/bin/env python3
"""
Test config loading to verify specialized models are loaded correctly
"""

import json
from models import Config
from config_manager import Config<PERSON>anager

def test_direct_json_loading():
    """Test loading config directly from JSON"""
    print("=== Testing Direct JSON Loading ===")
    
    with open('data/config.json', 'r') as f:
        config_dict = json.load(f)
    
    print("📋 Raw JSON config:")
    print(f"  ollama_model_documentation: {config_dict.get('ollama_model_documentation')}")
    print(f"  ollama_model_code_review: {config_dict.get('ollama_model_code_review')}")
    print(f"  ollama_model_risk_assessment: {config_dict.get('ollama_model_risk_assessment')}")
    
    # Create Config object
    config = Config(**config_dict)
    print("\n🎯 Config object:")
    print(f"  ollama_model_documentation: {config.ollama_model_documentation}")
    print(f"  ollama_model_code_review: {config.ollama_model_code_review}")
    print(f"  ollama_model_risk_assessment: {config.ollama_model_risk_assessment}")
    
    return config

def test_config_manager_loading():
    """Test loading config through ConfigManager"""
    print("\n=== Testing ConfigManager Loading ===")
    
    config_manager = ConfigManager("data/config.json")
    config = config_manager.load_config()
    
    print("🎯 ConfigManager loaded config:")
    print(f"  ollama_model_documentation: {config.ollama_model_documentation}")
    print(f"  ollama_model_code_review: {config.ollama_model_code_review}")
    print(f"  ollama_model_risk_assessment: {config.ollama_model_risk_assessment}")
    
    return config

def test_model_selection_logic(config):
    """Test the model selection logic"""
    print("\n=== Testing Model Selection Logic ===")
    
    # Test risk assessment model selection
    risk_model = getattr(config, 'ollama_model_risk_assessment', None)
    code_review_model = getattr(config, 'ollama_model_code_review', None)
    specialized_model = risk_model or code_review_model
    
    print("🎯 Model selection for risk assessment:")
    print(f"  Risk model: {risk_model}")
    print(f"  Code review model: {code_review_model}")
    print(f"  Selected specialized model: {specialized_model}")
    
    # Test documentation model selection
    doc_model = getattr(config, 'ollama_model_documentation', None)
    print(f"\n🎯 Documentation model: {doc_model}")

if __name__ == "__main__":
    print("🧪 Testing Config Loading for Specialized Models")
    print("=" * 60)
    
    try:
        # Test direct JSON loading
        config1 = test_direct_json_loading()
        
        # Test ConfigManager loading
        config2 = test_config_manager_loading()
        
        # Test model selection logic
        test_model_selection_logic(config2)
        
        print("\n" + "=" * 60)
        if (config2.ollama_model_documentation and 
            config2.ollama_model_code_review and 
            config2.ollama_model_risk_assessment):
            print("✅ SUCCESS: All specialized models loaded correctly!")
        else:
            print("❌ ISSUE: Some specialized models are missing!")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
