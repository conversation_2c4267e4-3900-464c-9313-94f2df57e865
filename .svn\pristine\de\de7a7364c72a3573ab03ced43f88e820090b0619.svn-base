# Quick Start Guide

Get RepoSense AI up and running in minutes with MailHog email testing included!

## 🚀 One-Command Startup

```bash
docker-compose up -d
```

This starts:
- **RepoSense AI** at [http://localhost:5001](http://localhost:5001)
- **MailHog Email Testing** at [http://localhost:8025](http://localhost:8025)

## 📧 Email Testing Ready

RepoSense AI comes pre-configured with **MailHog** for safe email testing:

### ✅ What's Already Configured:
- **SMTP Server**: `mailhog:1025` (internal Docker network)
- **Email Capture**: All emails are captured, not sent to real recipients
- **Web Interface**: View captured emails at [http://localhost:8025](http://localhost:8025)
- **Safe Testing**: Perfect for development and testing

### 🎯 How to Test Emails:
1. **Access RepoSense AI**: [http://localhost:5001](http://localhost:5001)
2. **Go to Configuration** → Email Settings
3. **Enable "Send Email Notifications"**
4. **Add test email addresses** to "Global Recipients"
5. **Save configuration**
6. **Process a repository** to trigger emails
7. **View captured emails** at [http://localhost:8025](http://localhost:8025)

## 🔧 Quick Configuration

### 1. Access the Web Interface
Open [http://localhost:5001](http://localhost:5001) in your browser.

### 2. Configure Basic Settings
Go to **Configuration** page and set:
- **Ollama Host**: Your AI service URL (e.g., `http://************:11434`)
- **Ollama Model**: Your preferred model (e.g., `qwen3-coder`, `smollm2`, `granite3.3`)
- **Email Settings**: Pre-configured for MailHog testing

💡 **Pro Tip**: You can change AI models per document later using the rescan feature!

### 3. Add a Repository
Go to **Repositories** page and:
- Click **"Add Repository"**
- Enter repository details (SVN URL, credentials if needed)
- Save and enable monitoring

### 4. Test Email Functionality
- Enable email notifications in Configuration
- Add test email addresses
- Process a repository
- Check MailHog at [http://localhost:8025](http://localhost:8025)

## 📊 Monitor Progress

### RepoSense AI Dashboard
- **Repository Status**: See which repositories are being monitored
- **Recent Activity**: View latest processed documents
- **System Health**: Check service status

### MailHog Email Interface
- **Captured Emails**: All emails sent by RepoSense AI
- **Email Content**: View HTML and text versions
- **Search & Filter**: Find specific emails easily

### Documents Page Features
- **AI Model Visibility**: See which AI model processed each document with green badges
- **Document Rescan**: Reprocess documents with different AI models
- **Smart Caching**: Automatic refresh when processing completes
- **Multiple View Modes**: Table, Card, and Repository Groups views

## 🔄 When Ready for Production

### Switch to Real Email:
1. **Go to Configuration** → Email Settings
2. **Click email provider preset**:
   - **"Gmail"** for Gmail setup
   - **"Outlook"** for Outlook setup
   - **"Custom"** for other providers
3. **Enter real credentials**
4. **Test with real email addresses**

### Keep MailHog for Testing:
- MailHog is safe to keep running alongside real email
- Use for testing new email templates
- Perfect for debugging email issues

## 🛠️ Troubleshooting

### RepoSense AI Not Accessible
```bash
# Check container status
docker ps

# Check logs
docker logs reposense-ai

# Restart if needed
docker-compose restart reposense-ai
```

### MailHog Not Accessible
```bash
# Check MailHog container
docker ps | grep mailhog

# Check MailHog logs
docker logs reposense-ai-mailhog

# Restart MailHog
docker-compose restart mailhog
```

### Emails Not Appearing in MailHog
1. **Check email configuration** in RepoSense AI
2. **Verify SMTP settings**: Host=`mailhog`, Port=`1025`
3. **Check RepoSense AI logs** for email sending attempts
4. **Trigger email manually** by processing a repository

## 📚 Next Steps

### Learn More:
- [MailHog Setup Guide](mailhog-setup.md) - Detailed MailHog documentation
- [Configuration Guide](configuration.md) - Complete configuration options
- [User Guide](usage.md) - Detailed usage instructions

### Advanced Setup:
- [Integration Guide](integration.md) - Connect with other systems
- [Development Guide](development.md) - Customize and extend
- [Deployment Guide](DEPLOYMENT.md) - Production deployment

## 🎉 You're Ready!

With this setup, you have:
- ✅ **Full RepoSense AI functionality**
- ✅ **Safe email testing with MailHog**
- ✅ **Easy configuration interface**
- ✅ **Ready for development and testing**

Start adding repositories and watch RepoSense AI automatically generate documentation and send email notifications (safely captured in MailHog)!
