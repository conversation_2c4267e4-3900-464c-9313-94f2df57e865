#!/usr/bin/env python3
"""
Test LLM Parameter Optimization

Tests that different LLM tasks get appropriate parameter settings
for optimal performance and consistency.
"""

import sys
import logging
from config_manager import ConfigManager
from ollama_client import OllamaClient

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_parameter_optimization():
    """Test that different task types get appropriate LLM parameters"""
    print("🧪 Testing LLM Parameter Optimization for Different Task Types")
    print("=" * 70)
    
    # Create a mock config and client
    config_manager = ConfigManager('data/config.json')
    config = config_manager.load_config()
    client = OllamaClient(config)
    
    # Test cases for different task types
    test_cases = [
        {
            "name": "Risk Assessment",
            "prompt": "Analyze the risk_level for this commit with security vulnerabilities",
            "system_prompt": "You are a risk assessment expert. Provide confidence and reasoning.",
            "expected_temp": 0.1,
            "expected_top_k": 20,
            "expected_num_predict": 2048
        },
        {
            "name": "Documentation Generation", 
            "prompt": "Generate comprehensive documentation for this commit with code changes",
            "system_prompt": "Create technical documentation in markdown format with headers",
            "expected_temp": 0.4,
            "expected_top_k": 50,
            "expected_num_predict": 4096
        },
        {
            "name": "Email Generation",
            "prompt": "Generate email subject and email body for stakeholders about business impact",
            "system_prompt": "Create professional email notification for repository commit",
            "expected_temp": 0.3,
            "expected_top_k": 40,
            "expected_num_predict": 1024
        },
        {
            "name": "Structured JSON Output",
            "prompt": "Respond in JSON format with exactly this structure: {\"result\": \"value\"}",
            "system_prompt": "You must respond in JSON format with the specified structure",
            "expected_temp": 0.1,
            "expected_top_k": 20,
            "expected_num_predict": 2048
        },
        {
            "name": "Content Scoring",
            "prompt": "Rate the relevance of each section and score them for batch scoring",
            "system_prompt": "Score content sections based on relevance criteria",
            "expected_temp": 0.05,
            "expected_top_k": 15,
            "expected_num_predict": 512
        },
        {
            "name": "Documentation Suggestions",
            "prompt": "Provide documentation suggestions to improve user documentation input",
            "system_prompt": "Generate helpful suggestions for documentation improvement",
            "expected_temp": 0.6,
            "expected_top_k": 60,
            "expected_num_predict": 2048
        },
        {
            "name": "General Content",
            "prompt": "Generate some general content about software development",
            "system_prompt": "You are a helpful assistant for general content generation",
            "expected_temp": 0.5,
            "expected_top_k": 40,
            "expected_num_predict": 3072
        }
    ]
    
    # Test each case
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing {test_case['name']}:")
        print(f"   Prompt: {test_case['prompt'][:60]}...")
        
        # Get optimized parameters
        params = client._get_optimized_parameters(test_case['prompt'], test_case['system_prompt'])
        
        # Check key parameters
        temp_match = params['temperature'] == test_case['expected_temp']
        top_k_match = params['top_k'] == test_case['expected_top_k']
        num_predict_match = params['num_predict'] == test_case['expected_num_predict']
        
        print(f"   Temperature: {params['temperature']} {'✅' if temp_match else '❌'} (expected: {test_case['expected_temp']})")
        print(f"   Top-K: {params['top_k']} {'✅' if top_k_match else '❌'} (expected: {test_case['expected_top_k']})")
        print(f"   Num Predict: {params['num_predict']} {'✅' if num_predict_match else '❌'} (expected: {test_case['expected_num_predict']})")
        print(f"   Top-P: {params['top_p']}, Repeat Penalty: {params['repeat_penalty']}")
        
        if temp_match and top_k_match and num_predict_match:
            print(f"   ✅ {test_case['name']} parameters optimized correctly")
        else:
            print(f"   ❌ {test_case['name']} parameters not optimized correctly")
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 All LLM parameter optimization tests passed!")
        print("✅ Different task types will get appropriate parameter settings")
    else:
        print("❌ Some parameter optimization tests failed")
        print("⚠️  LLM performance may not be optimal for all task types")
    
    return all_passed

def show_parameter_summary():
    """Show summary of parameter optimization strategy"""
    print("\n📊 LLM Parameter Optimization Strategy Summary:")
    print("=" * 60)
    
    strategies = [
        ("Risk Assessment & JSON", "Very Conservative", "temp=0.1, top_k=20", "Consistency & Accuracy"),
        ("Documentation Generation", "Balanced Creative", "temp=0.4, top_k=50", "Comprehensive & Readable"),
        ("Email Generation", "Professional", "temp=0.3, top_k=40", "Concise & Business-appropriate"),
        ("Content Scoring", "Ultra Conservative", "temp=0.05, top_k=15", "Consistent Scoring"),
        ("Doc Suggestions", "Creative Helpful", "temp=0.6, top_k=60", "Useful & Varied Suggestions"),
        ("General Content", "Balanced Default", "temp=0.5, top_k=40", "Good All-around Performance")
    ]
    
    for task, approach, params, goal in strategies:
        print(f"{task:25} | {approach:18} | {params:20} | {goal}")
    
    print("\n🎯 Benefits:")
    print("• Risk assessments get consistent, reliable results")
    print("• Documentation gets comprehensive, well-structured content")
    print("• Emails get professional, concise communication")
    print("• Scoring gets consistent, comparable results")
    print("• Suggestions get creative, helpful recommendations")
    print("• Each task type optimized for its specific requirements")

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced LLM Parameter Optimization")
    print("Testing parameter selection for different RepoSense AI tasks\n")
    
    success = test_parameter_optimization()
    show_parameter_summary()
    
    if success:
        print("\n🎯 LLM parameter optimization is working correctly!")
        print("The system will automatically optimize parameters for each task type.")
    else:
        print("\n⚠️  Some issues found with parameter optimization.")
        print("Manual review of parameter selection logic may be needed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
