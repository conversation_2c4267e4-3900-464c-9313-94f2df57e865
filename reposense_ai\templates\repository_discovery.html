{% extends "base.html" %}

{% block title %}Repository Discovery - RepoSense AI{% endblock %}

{% block extra_css %}
<style>
.advanced-options {
    margin: 1rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.repository-type-legend .badge {
    margin-right: 0.5rem;
}

.repo-type-badge {
    font-size: 0.75rem;
}

/* Tree View Styles */
.repositories-tree {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tree-node {
    margin: 0;
    padding: 0;
    list-style: none;
}

.tree-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 2px 0;
    transition: background-color 0.2s ease;
    border-left: 3px solid transparent;
}

.tree-item:hover {
    background-color: #f8f9fa;
}

.tree-item.repository-root {
    background-color: #f0f9ff;
    border-left-color: #0ea5e9;
    font-weight: 600;
}

.tree-item.branch-item {
    margin-left: 24px;
    background-color: #fefefe;
    border-left-color: #e5e7eb;
}

.tree-item.root-node {
    background-color: #f8fafc;
    border-left-color: #3b82f6;
    font-weight: 600;
}

.tree-item.root-node .tree-actions {
    display: none; /* Root nodes cannot be imported */
}

.tree-expand-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    cursor: pointer;
    color: #6b7280;
    transition: transform 0.2s ease;
}

.tree-expand-icon.expanded {
    transform: rotate(90deg);
}

.tree-expand-icon:hover {
    color: #374151;
}

.tree-children {
    margin-left: 16px;
}

.tree-children.collapsed {
    display: none;
}

/* Tree Header Styles */
.tree-header {
    background-color: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    position: sticky;
    top: 0;
    z-index: 10;
}

.tree-header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.tree-header-name {
    flex: 1;
    min-width: 200px;
}

.tree-header-meta {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tree-header-revision {
    min-width: 35px;
    text-align: center;
}

.tree-header-author {
    min-width: 70px;
    text-align: center;
}

.tree-header-date {
    min-width: 80px;
    text-align: center;
}

.tree-header-size {
    min-width: 50px;
    text-align: center;
}

.tree-header-actions {
    min-width: 60px;
    text-align: center;
}

.tree-item.branch-item:hover {
    background-color: #f3f4f6;
    border-left-color: #9ca3af;
}

.tree-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    color: #6b7280;
}

.tree-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tree-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.tree-name {
    font-weight: 500;
    color: #1f2937;
    min-width: 0;
    flex: 1;
}

.tree-name.repository-root {
    color: #0369a1;
    font-weight: 600;
}

.tree-name.branch-name {
    color: #374151;
    font-weight: 400;
}

.tree-badge {
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.tree-badge.root {
    background-color: #dcfce7;
    color: #166534;
}

.tree-badge.trunk {
    background-color: #dbeafe;
    color: #1e40af;
}

.tree-badge.branch {
    background-color: #e0f2fe;
    color: #0369a1;
}

.tree-badge.tag {
    background-color: #fef3c7;
    color: #92400e;
}

.tree-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #6b7280;
    font-size: 0.75rem;
}

.tree-revision {
    min-width: 35px;
    text-align: center;
    font-weight: 500;
}

.tree-author {
    min-width: 70px;
    text-align: center;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tree-date {
    min-width: 80px;
    text-align: center;
    font-size: 0.7rem;
}

.tree-size {
    min-width: 50px;
    text-align: center;
    font-size: 0.7rem;
    color: #9ca3af;
}

.tree-actions {
    margin-left: 12px;
}

.tree-actions .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}



/* Responsive repository display */
.repo-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
}

.repo-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.repo-header:hover {
    background: #e9ecef;
}

.repo-branches {
    padding: 0;
}

.branch-item {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.branch-item:last-child {
    border-bottom: none;
}

.branch-info {
    flex: 1;
    min-width: 200px;
}

.branch-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
    flex-wrap: wrap;
}

.branch-actions {
    flex-shrink: 0;
}

/* Branch type styling */
.branch-trunk { background-color: rgba(25, 135, 84, 0.05); }
.branch-branches { background-color: rgba(13, 110, 253, 0.05); }
.branch-tags { background-color: rgba(255, 193, 7, 0.05); }

.branch-type-legend {
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.branch-type-legend .badge {
    margin-right: 0.5rem;
}

/* Compact desktop table */
.compact-table {
    font-size: 0.9rem;
}

.compact-table th {
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.compact-table td {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
}

.repo-name {
    font-weight: 600;
    color: #495057;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.branch-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.branch-badge.trunk { background: rgba(25, 135, 84, 0.1); color: #198754; }
.branch-badge.branches { background: rgba(13, 110, 253, 0.1); color: #0d6efd; }
.branch-badge.tags { background: rgba(255, 193, 7, 0.1); color: #ffc107; }

.revision-info {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
}

.author-info {
    font-size: 0.8rem;
    color: #6c757d;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .branch-meta {
        flex-direction: column;
        gap: 0.25rem;
    }

    .branch-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .branch-actions {
        align-self: flex-end;
        margin-top: 0.5rem;
    }
}

/* Always show cards on mobile, compact table on desktop */
@media (min-width: 768px) {
    .repo-cards {
        display: none;
    }
    .repo-table {
        display: block;
    }
}

@media (max-width: 767px) {
    .repo-cards {
        display: block;
    }
    .repo-table {
        display: none;
    }
}

/* Resizable modal styles for import document browser */
.modal-dialog-resizable {
    resize: both;
    overflow: auto;
    min-width: 600px;
    min-height: 400px;
    max-width: 95vw;
    max-height: 95vh;
}

.modal-dialog-resizable .modal-content {
    height: 100%;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.modal-dialog-resizable .modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-dialog-resizable .modal-body .row {
    flex: 1;
    margin: 0;
}

.modal-dialog-resizable .modal-body .col-md-8,
.modal-dialog-resizable .modal-body .col-md-4 {
    display: flex;
    flex-direction: column;
    padding: 0 15px;
}

.modal-dialog-resizable .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
}

.modal-dialog-resizable .card-body {
    flex: 1;
    overflow: hidden;
    padding: 0;
}

/* Resize handle indicator */
.modal-dialog-resizable::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(-45deg, transparent 30%, #999 30%, #999 40%, transparent 40%, transparent 60%, #999 60%, #999 70%, transparent 70%);
    cursor: nw-resize;
    pointer-events: none;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.modal-dialog-resizable:hover::after {
    opacity: 1;
}

/* Scrollable areas */
#import_file_browser,
#import_selected_files {
    overflow-y: auto;
    flex: 1;
}
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Repository Discovery</h1>
            <p class="page-subtitle">Discover and import repositories from SVN servers</p>
        </div>
        <a href="{{ url_for('repositories_page') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Repositories
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-search"></i>Discovery Settings</h5>
            </div>
            <div class="card-body">
                {% if not config.svn_server_url %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tip:</strong> Configure your SVN server settings in
                    <a href="{{ url_for('config_page') }}" class="alert-link">Settings</a>
                    to pre-populate these fields.
                </div>
                {% endif %}
                <form id="discoveryForm">
                    <div class="mb-3">
                        <label for="base_url" class="form-label">SVN Server Base URL *</label>
                        <input type="text" class="form-control" id="base_url" name="base_url"
                               value="{{ config.svn_server_url or '' }}"
                               placeholder="http://svn.example.com/repos">
                        <div class="form-text">Base URL of your SVN server (configured in <a href="{{ url_for('config_page') }}">Settings</a>)</div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="{{ config.svn_server_username or '' }}"
                               autocomplete="username"
                               placeholder="Optional username">
                        <div class="form-text">Optional: Username for authentication</div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password"
                               value="{{ config.svn_server_password or '' }}"
                               autocomplete="current-password"
                               placeholder="Optional password">
                        <div class="form-text">Optional: Password for authentication</div>
                    </div>
                    
                    <div class="cache-selector">
                        <label for="cache_duration" class="form-label">
                            <i class="fas fa-clock"></i> Cache Duration
                        </label>
                        <select class="form-select" id="cache_duration" name="cache_duration">
                            <option value="300">5 minutes - Always fresh</option>
                            <option value="900" selected>15 minutes - Balanced [Recommended]</option>
                            <option value="1800">30 minutes - Performance focused</option>
                            <option value="3600">1 hour - Maximum performance</option>
                        </select>
                        <div class="cache-help form-text">
                            <small class="text-muted">
                                <strong>Intelligent Discovery</strong><br>
                                Repositories are discovered recursively and cached for performance. Tree nodes expand dynamically as you explore.
                            </small>
                        </div>
                    </div>



                    <button type="submit" class="btn btn-primary w-100" id="scanBtn">
                        <i class="fas fa-search"></i> Start Discovery
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i>Discovery Tips</h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>Discovery looks for standard SVN layouts (trunk, branches, tags)</li>
                    <li>Repositories are identified by their structure</li>
                    <li>Use authentication if your server requires it</li>
                    <li>Repositories are discovered recursively with intelligent caching</li>
                    <li>Discovered repositories start disabled for safety</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-code-branch"></i>Discovered Repositories</h5>
                <div id="discoveryStatus" class="text-muted">
                    Ready to scan
                </div>
            </div>
            <div class="card-body">
                <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Scanning...</span>
                    </div>
                    <p class="mt-2">Scanning for repositories...</p>
                </div>
                
                <div id="noResults" class="text-center py-4" style="display: none;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No Repositories Found</h5>
                    <p class="text-muted">Try adjusting your search parameters or check the server URL.</p>
                </div>
                
                <div id="resultsContainer" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span id="resultsCount" class="text-muted"></span>
                        <button class="btn btn-sm btn-success" onclick="importAllRepositories()">
                            <i class="fas fa-download"></i> Import All
                        </button>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="repository-type-legend">
                                <small class="text-muted">
                                    <strong>Repository Types:</strong>
                                    <span class="badge bg-success"><i class="fas fa-home"></i> Repository Root</span>
                                    <span class="badge bg-primary"><i class="fas fa-code-branch"></i> Trunk</span>
                                    <span class="badge bg-info"><i class="fas fa-code-branch"></i> Branch</span>
                                    <span class="badge bg-warning"><i class="fas fa-tag"></i> Tag</span>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select form-select-sm" id="repoTypeFilter" onchange="filterResults()">
                                    <option value="all">All Types</option>
                                    <option value="root">Repository Roots Only</option>
                                    <option value="trunk">Trunk Only</option>
                                    <option value="branch">Branches Only</option>
                                    <option value="tag">Tags Only</option>
                                </select>
                                <input type="text" class="form-control form-control-sm" id="repoSearch"
                                       placeholder="Search repositories..." onkeyup="filterResults()">
                            </div>
                        </div>
                    </div>

                    <!-- Card layout for mobile/small screens -->
                    <div class="repo-cards" id="repositoriesCards">
                    </div>

                    <!-- Compact table layout for desktop -->
                    <div class="repo-table">
                        <!-- Tree View -->
                        <!-- Column Headers -->
                        <div class="tree-header">
                            <div class="tree-header-content">
                                <div class="tree-header-name">Repository</div>
                                <div class="tree-header-meta">
                                    <div class="tree-header-revision">Rev</div>
                                    <div class="tree-header-author">Author</div>
                                    <div class="tree-header-date">Date</div>
                                    <div class="tree-header-size">Size</div>
                                </div>
                                <div class="tree-header-actions">Actions</div>
                            </div>
                        </div>

                        <div id="repositoriesTree" class="repositories-tree">
                            <!-- Tree structure will be populated here -->
                        </div>
                    </div>
                </div>
                
                <div id="errorMessage" class="alert alert-danger" style="display: none;">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Repository Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Validation Message -->
                <div id="importValidationMessage" class="alert" style="display: none;"></div>

                <form id="importRepositoryForm" onsubmit="return false;">
                    <div class="mb-3">
                        <label class="form-label">Repository Name</label>
                        <input type="text" class="form-control" id="import_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Repository URL</label>
                        <input type="text" class="form-control" id="import_url" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="import_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="import_username"
                               autocomplete="username"
                               placeholder="Optional username">
                        <div class="form-text">Leave empty if no authentication required</div>
                    </div>
                    <div class="mb-3">
                        <label for="import_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="import_password"
                               autocomplete="current-password"
                               placeholder="Optional password">
                        <div class="form-text">Leave empty if no authentication required</div>
                    </div>



                <!-- Product Documentation Files Section -->
                <div class="mb-3">
                    <label class="form-label">Product Documentation Files</label>
                    <div class="d-flex gap-2 mb-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="openImportDocumentBrowser()">
                            <i class="fas fa-folder-open"></i> Browse Repository
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearImportDocumentFiles()">
                            <i class="fas fa-times"></i> Clear All
                        </button>
                    </div>
                    <textarea class="form-control" id="import_product_documentation_files" rows="3"
                              placeholder="Click 'Browse Repository' to select documentation files, or enter file paths manually (one per line)"></textarea>
                    <div class="form-text">
                        <i class="fas fa-info-circle"></i> Select files that contain product documentation (README.md, user guides, API docs, etc.)
                    </div>
                </div>

                <!-- Risk Assessment Aggressiveness Section -->
                <div class="mb-3">
                    <label for="import_risk_aggressiveness" class="form-label">Risk Assessment Aggressiveness</label>
                    <select class="form-select" id="import_risk_aggressiveness">
                        <option value="CONSERVATIVE">Conservative - High scrutiny for stable/legacy systems (stricter thresholds)</option>
                        <option value="BALANCED" selected>Balanced - Standard risk assessment for active development</option>
                        <option value="AGGRESSIVE">Aggressive - Tolerant of changes in fast-moving development</option>
                        <option value="VERY_AGGRESSIVE">Very Aggressive - Most tolerant, suitable for experimental/prototype code</option>
                    </select>
                    <div class="form-text">
                        <i class="fas fa-info-circle text-info"></i>
                        Choose based on your development context - Conservative is strictest, Very Aggressive is most tolerant.
                    </div>
                </div>
                <div class="mb-3">
                    <label for="import_risk_description" class="form-label">Risk Assessment Context</label>
                    <textarea class="form-control" id="import_risk_description" rows="2"
                              placeholder="e.g., 'Legacy billing system - Conservative mode for careful change review' or 'Dev sandbox - Very Aggressive to reduce noise'"></textarea>
                    <div class="form-text">Document why this sensitivity level fits this repository's importance and development pace.</div>
                </div>
                </form> <!-- Close the import repository form -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmImport()">Import Repository</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Document Browser Modal -->
<div class="modal fade" id="importDocumentBrowserModal" tabindex="-1" aria-labelledby="importDocumentBrowserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-resizable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importDocumentBrowserModalLabel">
                    <i class="fas fa-folder-open"></i> Browse Repository Documentation
                    <small class="text-muted ms-2" title="Drag the bottom-right corner to resize this dialog. Use Ctrl+1/2/3/0 for quick sizing.">
                        <i class="fas fa-expand-arrows-alt"></i> Resizable
                    </small>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card h-100">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-folder"></i> Repository Files
                                        <span id="import_current_path" class="text-muted ms-2">/</span>
                                    </h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="import_filter_docs" checked>
                                        <label class="form-check-label" for="import_filter_docs" title="Show only documentation files">
                                            <i class="fas fa-filter"></i> Docs Only
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <!-- Loading indicator -->
                                <div id="import_loading_indicator" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div class="mt-2">Loading repository files...</div>
                                </div>
                                <div id="import_file_browser" class="list-group list-group-flush" style="display: none;">
                                    <!-- File list will be populated here -->
                                </div>
                                <div id="import_error_message" class="alert alert-danger m-3" style="display: none;">
                                    <!-- Error messages will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt"></i> Selected Files
                                    <span id="import_selected_count" class="badge bg-primary ms-2">0</span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="import_selected_files" class="list-group list-group-flush">
                                    <div class="list-group-item text-muted text-center">
                                        <i class="fas fa-info-circle"></i>
                                        <br>Select documentation files from the repository browser
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="applyImportSelectedFiles()">Apply Selected Files</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let discoveredRepositories = [];
let currentImportRepo = null;

document.getElementById('discoveryForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Validate base URL
    const baseUrlField = document.getElementById('base_url');
    const baseUrl = baseUrlField.value.trim();

    // Clear previous validation state
    baseUrlField.classList.remove('is-invalid');

    if (!baseUrl) {
        baseUrlField.classList.add('is-invalid');
        showError('Please enter a base URL');
        baseUrlField.focus();
        return;
    }

    // Basic URL validation
    try {
        new URL(baseUrl);
    } catch (e) {
        baseUrlField.classList.add('is-invalid');
        showError('Please enter a valid URL (e.g., http://sundc:81/svn)');
        baseUrlField.focus();
        return;
    }

    startDiscovery();
});

// Clear validation state when user types
document.getElementById('base_url').addEventListener('input', function() {
    this.classList.remove('is-invalid');
    document.getElementById('errorMessage').style.display = 'none';
});

// Clear any initial validation state on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('base_url').classList.remove('is-invalid');
    document.getElementById('errorMessage').style.display = 'none';


});

function startDiscovery() {
    const formData = new FormData(document.getElementById('discoveryForm'));

    // Trim the base URL to remove any whitespace
    const baseUrl = document.getElementById('base_url').value.trim();
    formData.set('base_url', baseUrl);

    const scanBtn = document.getElementById('scanBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const errorMessage = document.getElementById('errorMessage');
    const discoveryStatus = document.getElementById('discoveryStatus');
    
    // Show loading state
    scanBtn.disabled = true;
    scanBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
    loadingIndicator.style.display = 'block';
    resultsContainer.style.display = 'none';
    noResults.style.display = 'none';
    errorMessage.style.display = 'none';
    discoveryStatus.textContent = 'Scanning...';
    
    fetch('/repositories/discover/scan', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            discoveredRepositories = data.repositories;
            displayResults(data.repositories);
            discoveryStatus.textContent = `Found ${data.repositories.length} repositories`;
        } else {
            showError(data.message);
            discoveryStatus.textContent = 'Scan failed';
        }
    })
    .catch(error => {
        showError('Network error: ' + error.message);
        discoveryStatus.textContent = 'Scan failed';
    })
    .finally(() => {
        scanBtn.disabled = false;
        scanBtn.innerHTML = '<i class="fas fa-search"></i> Start Discovery';
        loadingIndicator.style.display = 'none';
    });
}

function displayResults(repositories) {
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const resultsCount = document.getElementById('resultsCount');
    const repositoriesTree = document.getElementById('repositoriesTree');

    if (repositories.length === 0) {
        noResults.style.display = 'block';
        resultsContainer.style.display = 'none';
        return;
    }

    resultsCount.textContent = `Found ${repositories.length} repositories`;
    repositoriesTree.innerHTML = '';

    // Group repositories by base name for tree structure
    const groupedRepos = {};
    repositories.forEach((repo, index) => {
        const parts = repo.name.split('/');
        const baseName = parts[0];

        if (!groupedRepos[baseName]) {
            groupedRepos[baseName] = {
                root: null,
                branches: []
            };
        }

        if (repo.repo_type === 'root') {
            groupedRepos[baseName].root = { ...repo, index };
        } else {
            groupedRepos[baseName].branches.push({ ...repo, index });
        }
    });

    // Create hierarchical tree structure
    Object.keys(groupedRepos).sort().forEach(repoName => {
        const repoGroup = groupedRepos[repoName];

        if (repoGroup.branches.length > 0) {
            // Create root node with children
            const rootContainer = document.createElement('div');
            rootContainer.className = 'tree-node-container';

            // Create root item (non-importable parent node)
            const rootItem = createParentTreeItem(repoName, repoGroup.branches.length);
            rootContainer.appendChild(rootItem);

            // Create children container (start collapsed)
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'tree-children collapsed';
            childrenContainer.id = `children-${repoName}`;

            // Add all branches as importable leaf nodes
            repoGroup.branches.forEach(branch => {
                const branchItem = createTreeItem(branch, false, true); // true = is leaf (importable)
                childrenContainer.appendChild(branchItem);
            });

            rootContainer.appendChild(childrenContainer);
            repositoriesTree.appendChild(rootContainer);
        } else if (repoGroup.root) {
            // Standalone repository (no children) - importable leaf node
            const rootItem = createTreeItem(repoGroup.root, true, true); // true = is leaf (importable)
            repositoriesTree.appendChild(rootItem);
        }
    });



    resultsContainer.style.display = 'block';
    noResults.style.display = 'none';
}

function createTreeItem(repo, isRoot, isLeaf = true) {
    const {repoTypeInfo} = getRepositoryTypeInfo(repo);

    const item = document.createElement('div');
    item.className = `tree-item ${isRoot ? 'repository-root' : 'branch-item'}`;

    // Determine display name
    let displayName = repo.name;
    if (!isRoot) {
        // For branches, show just the branch part
        const parts = repo.name.split('/');
        if (parts.length > 1) {
            displayName = parts.slice(1).join('/');
        }
    }

    // Determine badge class and text
    let badgeClass = 'tree-badge ';
    let badgeText = '';

    if (repo.repo_type === 'root') {
        badgeClass += 'root';
        badgeText = 'Repository Root';
    } else {
        switch (repo.branch_type) {
            case 'trunk':
                badgeClass += 'trunk';
                badgeText = 'Trunk';
                break;
            case 'branch':
                badgeClass += 'branch';
                badgeText = 'Branch';
                break;
            case 'tag':
                badgeClass += 'tag';
                badgeText = 'Tag';
                break;
            default:
                badgeClass += 'branch';
                badgeText = 'Branch';
        }
    }

    // Only show import button for leaf nodes
    const importButton = isLeaf ? `
        <div class="tree-actions">
            <button class="btn btn-sm btn-primary" onclick="showImportModal(${repo.index})" title="Import ${repo.name}">
                <i class="fas fa-download"></i>
            </button>
        </div>
    ` : '';

    item.innerHTML = `
        <div class="tree-icon">
            ${repoTypeInfo.icon}
        </div>
        <div class="tree-content">
            <div class="tree-info">
                <div class="tree-name ${isRoot ? 'repository-root' : 'branch-name'}" title="${repo.url}">
                    ${displayName}
                </div>
                <span class="${badgeClass}">${badgeText}</span>
            </div>
            <div class="tree-meta">
                <div class="tree-revision" title="Revision ${repo.last_revision || 'Unknown'}">${repo.last_revision || '-'}</div>
                <div class="tree-author" title="${repo.last_author || 'Unknown author'}">${repo.last_author || '-'}</div>
                <div class="tree-date" title="${repo.last_date || 'Unknown date'}">${formatDate(repo.last_date)}</div>
                <div class="tree-size" title="Repository size">${formatSize(repo.size)}</div>
            </div>
            ${importButton}
        </div>
    `;

    // Store repository type for filtering
    item.setAttribute('data-repo-type', repo.repo_type || 'root');
    item.setAttribute('data-branch-type', repo.branch_type || 'trunk');
    item.setAttribute('data-repo-name', repo.name.toLowerCase());

    return item;
}

function createParentTreeItem(repoName, childCount, repoUrl = '') {
    const item = document.createElement('div');
    item.className = 'tree-item root-node';

    item.innerHTML = `
        <div class="tree-expand-icon" onclick="toggleTreeNode('${repoName}', '${repoUrl}')">
            <i class="fas fa-chevron-right"></i>
        </div>
        <div class="tree-icon">
            <i class="fas fa-folder"></i>
        </div>
        <div class="tree-content">
            <div class="tree-info">
                <div class="tree-name repository-root" title="Repository: ${repoName}&#10;${repoUrl}">
                    ${repoName}
                </div>
                <span class="tree-badge root">Repository Root</span>
            </div>
            <div class="tree-meta">
                <div class="tree-revision" title="${childCount} child repositories">${childCount}</div>
                <div class="tree-author">-</div>
                <div class="tree-date">-</div>
                <div class="tree-size">-</div>
            </div>
        </div>
    `;

    // Store for filtering and lazy loading
    item.setAttribute('data-repo-type', 'root');
    item.setAttribute('data-branch-type', 'trunk');
    item.setAttribute('data-repo-name', repoName.toLowerCase());
    item.setAttribute('data-repo-url', repoUrl);
    item.setAttribute('data-children-loaded', 'true'); // Mark as already loaded for now

    return item;
}

function toggleTreeNode(repoName, repoUrl = '') {
    const childrenContainer = document.getElementById(`children-${repoName}`);
    const expandIcon = document.querySelector(`[onclick*="toggleTreeNode('${repoName}'"] i`);

    if (childrenContainer.classList.contains('collapsed')) {
        // Expand - check if we need to lazy load children
        const parentItem = expandIcon.closest('.tree-item');
        const childrenLoaded = parentItem.getAttribute('data-children-loaded') === 'true';

        if (!childrenLoaded && repoUrl) {
            // Lazy load children
            loadRepositoryChildren(repoName, repoUrl);
        }

        childrenContainer.classList.remove('collapsed');
        expandIcon.className = 'fas fa-chevron-down';
    } else {
        // Collapse
        childrenContainer.classList.add('collapsed');
        expandIcon.className = 'fas fa-chevron-right';
    }
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: '2-digit'
        });
    } catch (e) {
        return '-';
    }
}

function formatSize(size) {
    if (!size || size === 0) return '-';

    const units = ['B', 'KB', 'MB', 'GB'];
    let unitIndex = 0;
    let fileSize = size;

    while (fileSize >= 1024 && unitIndex < units.length - 1) {
        fileSize /= 1024;
        unitIndex++;
    }

    return `${fileSize.toFixed(1)}${units[unitIndex]}`;
}

function loadRepositoryChildren(repoName, repoUrl) {
    // Show loading indicator
    const childrenContainer = document.getElementById(`children-${repoName}`);
    childrenContainer.innerHTML = `
        <div class="tree-item">
            <div class="tree-content">
                <div class="tree-info">
                    <i class="fas fa-spinner fa-spin"></i> Loading children...
                </div>
            </div>
        </div>
    `;

    // Get current form values for the request
    const baseUrl = document.getElementById('base_url').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const cacheDuration = document.getElementById('cache_duration').value;
    const backendType = document.getElementById('backend_type').value;

    // Make request to load children for this specific repository
    const formData = new FormData();
    formData.append('base_url', repoUrl); // Use the specific repo URL
    formData.append('username', username);
    formData.append('password', password);
    formData.append('cache_duration', cacheDuration);
    formData.append('backend_type', backendType);
    formData.append('parent_only', 'true'); // Flag to indicate we want only direct children

    fetch('/repositories/discover/scan', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.repositories) {
            // Clear loading indicator
            childrenContainer.innerHTML = '';

            // Add children
            data.repositories.forEach((repo, index) => {
                if (repo.name !== repoName) { // Don't include the parent itself
                    const childItem = createTreeItem(repo, false, true);
                    childrenContainer.appendChild(childItem);
                }
            });

            // Mark as loaded
            const parentItem = document.querySelector(`[data-repo-name="${repoName.toLowerCase()}"]`);
            if (parentItem) {
                parentItem.setAttribute('data-children-loaded', 'true');
            }
        } else {
            childrenContainer.innerHTML = `
                <div class="tree-item">
                    <div class="tree-content">
                        <div class="tree-info text-muted">
                            <i class="fas fa-exclamation-triangle"></i> Failed to load children
                        </div>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading repository children:', error);
        childrenContainer.innerHTML = `
            <div class="tree-item">
                <div class="tree-content">
                    <div class="tree-info text-danger">
                        <i class="fas fa-exclamation-triangle"></i> Error loading children
                    </div>
                </div>
            </div>
        `;
    });
}

function getRepositoryTypeInfo(repo) {
    const repoType = repo.repo_type || 'root';
    const branchType = repo.branch_type || 'trunk';


    let repoTypeInfo = {};

    // Repository type information
    switch (repoType) {
        case 'root':
            repoTypeInfo = {
                label: 'Repository Root',
                icon: '<i class="fas fa-home"></i>',
                badgeClass: 'badge bg-success',
                description: 'Repository root - can monitor all branches automatically'
            };
            break;
        case 'branch':
            switch (branchType) {
                case 'trunk':
                    repoTypeInfo = {
                        label: 'Trunk',
                        icon: '<i class="fas fa-code-branch"></i>',
                        badgeClass: 'badge bg-primary',
                        description: 'Main development branch (trunk)'
                    };
                    break;
                case 'branch':
                    repoTypeInfo = {
                        label: 'Branch',
                        icon: '<i class="fas fa-code-branch"></i>',
                        badgeClass: 'badge bg-info',
                        description: 'Individual development branch'
                    };
                    break;
                case 'tag':
                    repoTypeInfo = {
                        label: 'Tag',
                        icon: '<i class="fas fa-tag"></i>',
                        badgeClass: 'badge bg-warning',
                        description: 'Release tag or version'
                    };
                    break;
                default:
                    repoTypeInfo = {
                        label: 'Other',
                        icon: '<i class="fas fa-folder"></i>',
                        badgeClass: 'badge bg-secondary',
                        description: 'Other repository path'
                    };
            }
            break;
        default:
            repoTypeInfo = {
                label: 'Unknown',
                icon: '<i class="fas fa-question"></i>',
                badgeClass: 'badge bg-secondary',
                description: 'Unknown repository type'
            };
    }

    return { repoTypeInfo };
}

function getBranchInfo(repoName) {
    const parts = repoName.split('/');
    const branchInfo = parts.slice(1).join('/');

    let branchType = '';
    let branchIcon = '';
    let branchClass = '';
    let branchBadgeClass = '';

    if (branchInfo.startsWith('trunk')) {
        branchType = 'trunk';
        branchIcon = '<i class="fas fa-code-branch"></i>';
        branchClass = 'branch-trunk';
        branchBadgeClass = 'trunk';
    } else if (branchInfo.startsWith('branches/')) {
        branchType = branchInfo.replace('branches/', '');
        // Truncate long branch names
        if (branchType.length > 20) {
            branchType = branchType.substring(0, 17) + '...';
        }
        branchIcon = '<i class="fas fa-code-branch"></i>';
        branchClass = 'branch-branches';
        branchBadgeClass = 'branches';
    } else if (branchInfo.startsWith('tags/')) {
        branchType = branchInfo.replace('tags/', '');
        // Truncate long tag names
        if (branchType.length > 15) {
            branchType = branchType.substring(0, 12) + '...';
        }
        branchIcon = '<i class="fas fa-tag"></i>';
        branchClass = 'branch-tags';
        branchBadgeClass = 'tags';
    } else {
        branchType = branchInfo || 'root';
        branchIcon = '<i class="fas fa-folder"></i>';
        branchClass = '';
        branchBadgeClass = '';
    }

    return {branchType, branchIcon, branchClass, branchBadgeClass};
}

function toggleRepoGroup(repoName) {
    const branches = document.getElementById(`branches-${repoName}`);
    const chevron = document.getElementById(`chevron-${repoName}`);

    if (branches.style.display === 'none') {
        branches.style.display = 'block';
        chevron.className = 'fas fa-chevron-down';
    } else {
        branches.style.display = 'none';
        chevron.className = 'fas fa-chevron-right';
    }
}

function filterResults() {
    const repoTypeFilter = document.getElementById('repoTypeFilter').value;
    const searchTerm = document.getElementById('repoSearch').value.toLowerCase();
    const treeItems = document.querySelectorAll('.tree-item');

    let visibleCount = 0;

    treeItems.forEach(item => {
        const repoName = item.getAttribute('data-repo-name') || '';
        const repoType = item.getAttribute('data-repo-type') || 'root';
        const branchType = item.getAttribute('data-branch-type') || 'trunk';

        // Check search term
        const matchesSearch = searchTerm === '' || repoName.includes(searchTerm);

        // Check repository type filter
        let matchesFilter = true;
        if (repoTypeFilter === 'root') {
            matchesFilter = repoType === 'root';
        } else if (repoTypeFilter === 'trunk') {
            matchesFilter = repoType === 'branch' && branchType === 'trunk';
        } else if (repoTypeFilter === 'branch') {
            matchesFilter = repoType === 'branch' && branchType === 'branch';
        } else if (repoTypeFilter === 'tag') {
            matchesFilter = repoType === 'branch' && branchType === 'tag';
        }

        if (matchesSearch && matchesFilter) {
            item.style.display = '';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // Update results count
    const resultsCount = document.getElementById('resultsCount');
    resultsCount.textContent = `Showing ${visibleCount} of ${discoveredRepositories.length} repositories`;
}





function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}

function showImportModal(index) {
    currentImportRepo = discoveredRepositories[index];

    document.getElementById('import_name').value = currentImportRepo.name;
    document.getElementById('import_url').value = currentImportRepo.url;
    document.getElementById('import_username').value = document.getElementById('username').value;
    document.getElementById('import_password').value = document.getElementById('password').value;

    // Repository type detection messages removed - no longer functionally relevant



    new bootstrap.Modal(document.getElementById('importModal')).show();
}

function showImportValidationMessage(type, message) {
    const messageDiv = document.getElementById('importValidationMessage');
    if (!messageDiv) return;

    messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'warning'} mb-3`;
    messageDiv.innerHTML = message;
    messageDiv.style.display = 'block';
}

function confirmImport() {
    if (!currentImportRepo) return;

    // Get product documentation files
    const productDocFiles = document.getElementById('import_product_documentation_files').value;
    const productDocFilesList = productDocFiles ?
        productDocFiles.split('\n').map(f => f.trim()).filter(f => f.length > 0) : [];

    // Get risk assessment settings
    const riskAggressiveness = document.getElementById('import_risk_aggressiveness').value;
    const riskDescription = document.getElementById('import_risk_description').value;



    const importData = {
        ...currentImportRepo,
        username: document.getElementById('import_username').value || null,
        password: document.getElementById('import_password').value || null,
        product_documentation_files: productDocFilesList,
        risk_aggressiveness: riskAggressiveness,
        risk_description: riskDescription
    };
    
    fetch('/repositories/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(importData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Repository imported successfully!');
            bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();
            // Remove from discovered list
            const index = discoveredRepositories.indexOf(currentImportRepo);
            if (index > -1) {
                discoveredRepositories.splice(index, 1);
                displayResults(discoveredRepositories);
            }
        } else {
            alert('Import failed: ' + data.message);
        }
    })
    .catch(error => {
        alert('Network error: ' + error.message);
    });
}

function importAllRepositories() {
    if (discoveredRepositories.length === 0) return;
    
    if (!confirm(`Import all ${discoveredRepositories.length} repositories?`)) return;
    
    const username = document.getElementById('username').value || null;
    const password = document.getElementById('password').value || null;
    
    let imported = 0;
    let failed = 0;
    
    discoveredRepositories.forEach(repo => {
        const importData = {
            ...repo,
            username: username,
            password: password,
            risk_aggressiveness: 'BALANCED',  // Default for bulk import
            risk_description: 'Bulk imported repository - using balanced risk assessment'
        };
        
        fetch('/repositories/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(importData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                imported++;
            } else {
                failed++;
            }
            
            // Check if all requests completed
            if (imported + failed === discoveredRepositories.length) {
                alert(`Import completed: ${imported} successful, ${failed} failed`);
                if (imported > 0) {
                    discoveredRepositories = [];
                    displayResults([]);
                }
            }
        })
        .catch(error => {
            failed++;
            if (imported + failed === discoveredRepositories.length) {
                alert(`Import completed: ${imported} successful, ${failed} failed`);
            }
        });
    });
}



// Import Document Browser functionality
let importCurrentPath = '/';
let importSelectedFiles = new Set();

function openImportDocumentBrowser() {
    // Get repository details from the import modal
    const repoUrl = document.getElementById('import_url').value;
    const username = document.getElementById('import_username').value;
    const password = document.getElementById('import_password').value;

    if (!repoUrl) {
        alert('Repository URL is required to browse files');
        return;
    }

    // Reset browser state
    importCurrentPath = '/';
    importSelectedFiles.clear();
    updateImportSelectedFilesDisplay();

    // Show the browser modal
    const modal = new bootstrap.Modal(document.getElementById('importDocumentBrowserModal'));
    modal.show();

    // Add event listener for filter toggle
    document.getElementById('import_filter_docs').addEventListener('change', function() {
        // Reload current directory with new filter setting
        loadImportDirectoryContents(importCurrentPath);
    });

    // Load root directory
    loadImportDirectoryContents('/');
}

function loadImportDirectoryContents(path) {
    const loadingIndicator = document.getElementById('import_loading_indicator');
    const fileBrowser = document.getElementById('import_file_browser');
    const errorMessage = document.getElementById('import_error_message');
    const currentPathSpan = document.getElementById('import_current_path');

    // Show loading state
    loadingIndicator.style.display = 'block';
    fileBrowser.style.display = 'none';
    errorMessage.style.display = 'none';

    // Update current path display
    currentPathSpan.textContent = path || '/';
    importCurrentPath = path || '/';

    // Get repository details
    const repoUrl = document.getElementById('import_url').value;
    const username = document.getElementById('import_username').value;
    const password = document.getElementById('import_password').value;
    const filterDocs = document.getElementById('import_filter_docs').checked;

    // Make API call to browse repository
    const params = new URLSearchParams({
        url: repoUrl,
        path: path || '/',
        username: username || '',
        password: password || '',
        filter_docs: filterDocs ? 'true' : 'false'
    });

    fetch(`/api/repositories/browse?${params}`)
        .then(response => response.json())
        .then(data => {
            loadingIndicator.style.display = 'none';

            if (data.error) {
                errorMessage.textContent = data.error;
                errorMessage.style.display = 'block';
                return;
            }

            renderImportFileList(data.files || []);
            fileBrowser.style.display = 'block';
        })
        .catch(error => {
            console.error('Error loading directory:', error);
            loadingIndicator.style.display = 'none';
            errorMessage.textContent = 'Failed to load directory contents: ' + error.message;
            errorMessage.style.display = 'block';
        });
}

function renderImportFileList(files) {
    const fileBrowser = document.getElementById('import_file_browser');
    fileBrowser.innerHTML = '';

    // Add parent directory link if not at root
    if (importCurrentPath !== '/') {
        const parentPath = importCurrentPath.split('/').slice(0, -1).join('/') || '/';
        const parentItem = createImportFileItem({
            name: '..',
            type: 'directory',
            path: parentPath
        }, true);
        fileBrowser.appendChild(parentItem);
    }

    // Sort files: directories first, then files
    files.sort((a, b) => {
        if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
    });

    // Add file items
    files.forEach(file => {
        const fileItem = createImportFileItem(file);
        fileBrowser.appendChild(fileItem);
    });
}

function createImportFileItem(file, isParent = false) {
    const item = document.createElement('div');
    item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

    const isDirectory = file.type === 'directory';
    const isSelected = importSelectedFiles.has(file.path);

    if (isSelected && !isDirectory) {
        item.classList.add('list-group-item-primary');
    }

    const leftContent = document.createElement('div');
    leftContent.className = 'd-flex align-items-center';

    const icon = document.createElement('i');
    icon.className = isDirectory ? 'fas fa-folder text-warning me-2' : 'fas fa-file text-muted me-2';
    leftContent.appendChild(icon);

    const name = document.createElement('span');
    name.textContent = file.name;
    if (isParent) {
        name.textContent = '.. (Parent Directory)';
        name.className = 'text-muted';
    }
    leftContent.appendChild(name);

    item.appendChild(leftContent);

    // Add action buttons
    const actions = document.createElement('div');

    if (isDirectory) {
        // Browse button for directories
        const browseBtn = document.createElement('button');
        browseBtn.className = 'btn btn-sm btn-outline-primary';
        browseBtn.innerHTML = '<i class="fas fa-folder-open"></i>';
        browseBtn.title = 'Browse directory';
        browseBtn.onclick = (e) => {
            e.stopPropagation();
            loadImportDirectoryContents(file.path);
        };
        actions.appendChild(browseBtn);
    } else {
        // Select/deselect button for files
        const selectBtn = document.createElement('button');
        selectBtn.className = isSelected ? 'btn btn-sm btn-success' : 'btn btn-sm btn-outline-success';
        selectBtn.innerHTML = isSelected ? '<i class="fas fa-check"></i>' : '<i class="fas fa-plus"></i>';
        selectBtn.title = isSelected ? 'Remove from selection' : 'Add to selection';
        selectBtn.onclick = (e) => {
            e.stopPropagation();
            toggleImportFileSelection(file);
        };
        actions.appendChild(selectBtn);
    }

    item.appendChild(actions);

    // Double-click to browse directories or select files
    item.ondblclick = () => {
        if (isDirectory) {
            loadImportDirectoryContents(file.path);
        } else {
            toggleImportFileSelection(file);
        }
    };

    return item;
}

function toggleImportFileSelection(file) {
    if (importSelectedFiles.has(file.path)) {
        importSelectedFiles.delete(file.path);
    } else {
        importSelectedFiles.add(file.path);
    }

    updateImportSelectedFilesDisplay();

    // Refresh the current directory to update selection indicators
    renderImportFileList(Array.from(document.querySelectorAll('#import_file_browser .list-group-item')).map(item => {
        const name = item.querySelector('span').textContent;
        const isDirectory = item.querySelector('.fa-folder') !== null;
        return {
            name: name,
            type: isDirectory ? 'directory' : 'file',
            path: importCurrentPath === '/' ? `/${name}` : `${importCurrentPath}/${name}`
        };
    }).filter(f => f.name !== '.. (Parent Directory)'));
}

function updateImportSelectedFilesDisplay() {
    const selectedFilesContainer = document.getElementById('import_selected_files');
    const selectedCount = document.getElementById('import_selected_count');

    selectedCount.textContent = importSelectedFiles.size;

    if (importSelectedFiles.size === 0) {
        selectedFilesContainer.innerHTML = `
            <div class="list-group-item text-muted text-center">
                <i class="fas fa-info-circle"></i>
                <br>Select documentation files from the repository browser
            </div>
        `;
        return;
    }

    selectedFilesContainer.innerHTML = '';

    Array.from(importSelectedFiles).sort().forEach(filePath => {
        const item = document.createElement('div');
        item.className = 'list-group-item d-flex justify-content-between align-items-center';

        const fileName = filePath.split('/').pop();
        const fileInfo = document.createElement('div');
        fileInfo.innerHTML = `
            <div class="fw-bold">${fileName}</div>
            <small class="text-muted">${filePath}</small>
        `;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-sm btn-outline-danger';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.title = 'Remove from selection';
        removeBtn.onclick = () => {
            importSelectedFiles.delete(filePath);
            updateImportSelectedFilesDisplay();
        };

        item.appendChild(fileInfo);
        item.appendChild(removeBtn);
        selectedFilesContainer.appendChild(item);
    });
}

function applyImportSelectedFiles() {
    const textarea = document.getElementById('import_product_documentation_files');
    const selectedPaths = Array.from(importSelectedFiles).sort();

    if (selectedPaths.length === 0) {
        alert('No files selected');
        return;
    }

    // Set the selected files in the textarea
    textarea.value = selectedPaths.join('\n');

    // Close the browser modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('importDocumentBrowserModal'));
    modal.hide();

    // Show success message
    const count = selectedPaths.length;
    alert(`${count} documentation file${count !== 1 ? 's' : ''} selected successfully`);
}

function clearImportDocumentFiles() {
    document.getElementById('import_product_documentation_files').value = '';
}


</script>
{% endblock %}
