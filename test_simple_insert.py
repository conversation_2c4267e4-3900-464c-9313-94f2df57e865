#!/usr/bin/env python3
"""
Simple test to verify database insert with heuristic_context works
"""

import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from datetime import datetime
from reposense_ai.document_database import DocumentDatabase, DocumentRecord

def test_simple_insert():
    """Test simple document insert with heuristic context"""
    
    print("🧪 Testing simple document insert with heuristic context...")
    
    # Initialize database
    db = DocumentDatabase("/app/data/documents.db")
    
    # Create minimal heuristic context
    heuristic_context = {
        'indicators': {
            'risk_assessment': 'CRITICAL - test document',
            'risk_confidence': '0.95'
        }
    }
    
    # Create minimal document record
    test_doc = DocumentRecord(
        id="test-simple-001",
        repository_id="test",
        repository_name="Test Repo",
        revision=1,
        date=datetime.now(),
        filename="test.md",
        filepath="/test/test.md",
        size=100,
        author="Test",
        commit_message="Test commit",
        risk_level="CRITICAL",
        heuristic_context=heuristic_context
    )
    
    print(f"Attempting to insert document with ID: {test_doc.id}")
    print(f"Risk level: {test_doc.risk_level}")
    print(f"Heuristic context: {json.dumps(heuristic_context, indent=2)}")
    
    # Try to insert
    try:
        success = db.upsert_document(test_doc)
        if success:
            print("✅ Document inserted successfully!")
            
            # Try to retrieve it
            retrieved = db.get_document_by_id(test_doc.id)
            if retrieved:
                print("✅ Document retrieved successfully!")
                print(f"Retrieved risk level: {retrieved.risk_level}")
                if retrieved.heuristic_context:
                    print(f"Retrieved heuristic context: {json.dumps(retrieved.heuristic_context, indent=2)}")
                else:
                    print("⚠️  No heuristic context in retrieved document")
            else:
                print("❌ Failed to retrieve document")
        else:
            print("❌ Failed to insert document")
            
    except Exception as e:
        print(f"❌ Exception during insert: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_insert()
