#!/bin/bash
# Validation Script for RepoSense AI Directory Structure
# This script validates that the directory structure is correct for integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 RepoSense AI Structure Validation${NC}"
echo "This script validates the directory structure for integration readiness."
echo

# Function to log messages
log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

VALIDATION_PASSED=true

log_step "1. Checking root directory structure..."

# Check root files (Dockerfile is now in reposense_ai/)
ROOT_FILES=("docker-compose.yml" "README.md")
for file in "${ROOT_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_info "Found root file: $file"
    else
        log_error "Missing root file: $file"
        VALIDATION_PASSED=false
    fi
done

# Check reposense_ai directory exists
if [ -d "reposense_ai" ]; then
    log_info "Found reposense_ai/ directory"
else
    log_error "Missing reposense_ai/ directory"
    VALIDATION_PASSED=false
    exit 1
fi

log_step "2. Checking application files..."

# Check essential application files
APP_FILES=(
    "reposense_ai/Dockerfile"
    "reposense_ai/start_reposense_ai.py"
    "reposense_ai/reposense_ai_app.py"
    "reposense_ai/requirements.txt"
    "reposense_ai/entrypoint.sh"
)

for file in "${APP_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_info "Found application file: $file"
    else
        log_error "Missing application file: $file"
        VALIDATION_PASSED=false
    fi
done

# Check essential directories
APP_DIRS=(
    "reposense_ai/data"
    "reposense_ai/logs"
    "reposense_ai/static"
    "reposense_ai/templates"
)

for dir in "${APP_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        log_info "Found application directory: $dir"
    else
        log_warn "Missing application directory: $dir (will be created automatically)"
    fi
done

log_step "3. Checking Docker configuration..."

# Check Dockerfile content (now in reposense_ai/ directory)
if [ -f "reposense_ai/Dockerfile" ]; then
    if grep -q "COPY requirements.txt" reposense_ai/Dockerfile; then
        log_info "Dockerfile has correct requirements.txt path"
    else
        log_error "Dockerfile needs to be updated for new structure"
        VALIDATION_PASSED=false
    fi

    if grep -q "COPY \. \." reposense_ai/Dockerfile; then
        log_info "Dockerfile has correct COPY commands"
    else
        log_error "Dockerfile COPY commands need updating"
        VALIDATION_PASSED=false
    fi
else
    log_error "Dockerfile not found in reposense_ai/ directory"
    VALIDATION_PASSED=false
fi

# Check docker-compose.yml content
if [ -f "docker-compose.yml" ]; then
    if grep -q "./reposense_ai/data:/app/data" docker-compose.yml; then
        log_info "docker-compose.yml has correct data volume path"
    else
        log_error "docker-compose.yml data volume path needs updating"
        VALIDATION_PASSED=false
    fi

    if grep -q "./reposense_ai/logs:/app/logs" docker-compose.yml; then
        log_info "docker-compose.yml has correct logs volume path"
    else
        log_error "docker-compose.yml logs volume path needs updating"
        VALIDATION_PASSED=false
    fi

    if grep -q "context: ./reposense_ai" docker-compose.yml; then
        log_info "docker-compose.yml has correct build context"
    else
        log_error "docker-compose.yml build context needs updating"
        VALIDATION_PASSED=false
    fi
    
    if grep -q "ollama-network" docker-compose.yml; then
        log_info "docker-compose.yml uses ollama-network"
    else
        log_warn "docker-compose.yml should use ollama-network for integration"
    fi
fi

log_step "4. Checking configuration..."

# Check config.json if it exists
CONFIG_FILE="reposense_ai/data/config.json"
if [ -f "$CONFIG_FILE" ]; then
    log_info "Found configuration file: $CONFIG_FILE"
    
    if command -v jq >/dev/null 2>&1; then
        OLLAMA_HOST=$(jq -r '.ollama_host' "$CONFIG_FILE" 2>/dev/null || echo "null")
        if [ "$OLLAMA_HOST" = "http://ollama:11434" ]; then
            log_info "Configuration uses Docker service name for Ollama"
        elif [ "$OLLAMA_HOST" != "null" ]; then
            log_warn "Configuration uses $OLLAMA_HOST - consider updating to http://ollama:11434 for Docker integration"
        fi
    else
        log_warn "jq not available - cannot validate config.json content"
    fi
else
    log_warn "Configuration file not found - will be created on first run"
fi

log_step "5. Checking permissions..."

# Check data directory permissions
if [ -d "reposense_ai/data" ]; then
    if [ -w "reposense_ai/data" ]; then
        log_info "Data directory is writable"

        # Check ownership for Docker compatibility
        DATA_OWNER=$(stat -c '%u' reposense_ai/data 2>/dev/null || echo "unknown")
        if [ "$DATA_OWNER" = "1000" ]; then
            log_info "Data directory has correct ownership (uid 1000) for Docker"
        elif [ "$DATA_OWNER" != "unknown" ]; then
            log_warn "Data directory owned by uid $DATA_OWNER - should be 1000 for Docker. Run: sudo chown -R 1000:1000 reposense_ai/data"
        fi
    else
        log_error "Data directory is not writable - run: sudo chown -R 1000:1000 reposense_ai/data && chmod -R 755 reposense_ai/data"
        VALIDATION_PASSED=false
    fi
fi

# Check logs directory permissions
if [ -d "reposense_ai/logs" ]; then
    if [ -w "reposense_ai/logs" ]; then
        log_info "Logs directory is writable"

        # Check ownership for Docker compatibility
        LOGS_OWNER=$(stat -c '%u' reposense_ai/logs 2>/dev/null || echo "unknown")
        if [ "$LOGS_OWNER" = "1000" ]; then
            log_info "Logs directory has correct ownership (uid 1000) for Docker"
        elif [ "$LOGS_OWNER" != "unknown" ]; then
            log_warn "Logs directory owned by uid $LOGS_OWNER - should be 1000 for Docker. Run: sudo chown -R 1000:1000 reposense_ai/logs"
        fi
    else
        log_error "Logs directory is not writable - run: sudo chown -R 1000:1000 reposense_ai/logs && chmod -R 755 reposense_ai/logs"
        VALIDATION_PASSED=false
    fi
fi

# Check entrypoint script permissions
if [ -f "reposense_ai/entrypoint.sh" ]; then
    if [ -x "reposense_ai/entrypoint.sh" ]; then
        log_info "Entrypoint script is executable"
    else
        log_error "Entrypoint script is not executable - run: chmod +x reposense_ai/entrypoint.sh"
        VALIDATION_PASSED=false
    fi
fi

log_step "6. Checking integration files..."

# Check integration files
INTEGRATION_FILES=(
    "setup-integration.sh"
    "INTEGRATION_README.md"
    "docker-compose-integrated.yml"
    "docker-compose-full-example.yml"
)

for file in "${INTEGRATION_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_info "Found integration file: $file"
    else
        log_warn "Missing integration file: $file (optional)"
    fi
done

echo
if [ "$VALIDATION_PASSED" = true ]; then
    echo -e "${GREEN}✅ Validation passed! Structure is ready for integration.${NC}"
    echo
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Run ./setup-integration.sh to prepare for integration"
    echo "2. Add the RepoSense AI service to your docker-compose.yml"
    echo "3. Start services: docker-compose up -d"
    echo "4. Access web interface: http://localhost:5000"
    echo
    echo -e "${YELLOW}📖 See INTEGRATION_README.md for detailed instructions${NC}"
else
    echo -e "${RED}❌ Validation failed! Please fix the issues above.${NC}"
    echo
    echo -e "${BLUE}Common fixes:${NC}"
    echo "1. Run ./migrate-to-subdirectory.sh if migrating from old structure"
    echo "2. Update Dockerfile and docker-compose.yml paths"
    echo "3. Set proper permissions: chmod -R 755 reposense_ai/"
    echo "4. Make entrypoint executable: chmod +x reposense_ai/entrypoint.sh"
    exit 1
fi
