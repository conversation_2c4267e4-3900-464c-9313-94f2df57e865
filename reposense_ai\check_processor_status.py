#!/usr/bin/env python3
"""
Check unified processor status
"""

import sys
sys.path.append('/app')

from unified_document_processor import UnifiedDocumentProcessor
from config_manager import ConfigManager

def check_processor_status():
    """Check if unified processor is running"""
    try:
        config_manager = ConfigManager()
        processor = UnifiedDocumentProcessor(output_dir='/app/data/output', config_manager=config_manager)
        
        stats = processor.get_stats()
        print('Unified Processor Status:')
        print(f'  Running: {stats["running"]}')
        print(f'  Queue Size: {stats["queue_size"]}')
        print(f'  Active Threads: {stats["active_threads"]}')
        print(f'  Processed Count: {stats["processed_count"]}')
        print(f'  Error Count: {stats["error_count"]}')
        
        if not stats['running']:
            print('Starting unified processor...')
            processor.start()
            print('Unified processor started')
            
            # Check status again
            stats = processor.get_stats()
            print('Updated Status:')
            print(f'  Running: {stats["running"]}')
            print(f'  Active Threads: {stats["active_threads"]}')
        
        return stats['running']
        
    except Exception as e:
        print(f'Error checking processor status: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_processor_status()
