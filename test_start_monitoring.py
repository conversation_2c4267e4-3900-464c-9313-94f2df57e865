#!/usr/bin/env python3
"""
Test starting monitoring via the web API
"""

import requests
import json
import time

def test_start_monitoring():
    """Test the /api/start endpoint"""
    print("🚀 Testing Start Monitoring API...")
    
    try:
        # First check current status
        print("📊 Checking current status...")
        status_response = requests.get('http://localhost:5000/api/status', timeout=10)
        if status_response.status_code == 200:
            status = status_response.json()
            print(f"  Current monitoring status: {status.get('monitoring_running', 'Unknown')}")
            print(f"  Enabled repositories: {status.get('enabled_repositories', 0)}")
        
        # Try to start monitoring
        print("\n🟢 Attempting to start monitoring...")
        response = requests.post('http://localhost:5000/api/start', timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Start monitoring succeeded!")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if data.get('success'):
                print(f"  ✅ Monitoring started successfully")
                print(f"  📊 Repositories being monitored: {data.get('repositories', 0)}")
                
                # Wait a moment and check status again
                print(f"\n⏳ Waiting 5 seconds to check if monitoring is active...")
                time.sleep(5)
                
                status_response = requests.get('http://localhost:5000/api/status', timeout=10)
                if status_response.status_code == 200:
                    new_status = status_response.json()
                    print(f"📊 Updated status:")
                    print(f"  Monitoring running: {new_status.get('monitoring_running', 'Unknown')}")
                    print(f"  Last check time: {new_status.get('last_check_time', 'Never')}")
                    
                    if new_status.get('monitoring_running'):
                        print(f"  ✅ Periodic monitoring is now ACTIVE!")
                        print(f"  🔄 System will check for commits every 5 minutes")
                        print(f"  🎯 Revision 9 should be detected in the next scan cycle")
                    else:
                        print(f"  ❌ Monitoring still not active - may need container restart")
            else:
                print(f"  ❌ Start failed: {data.get('error', 'Unknown error')}")
                
        elif response.status_code == 400:
            data = response.json()
            print(f"❌ Start monitoring failed (Bad Request):")
            print(f"  Error: {data.get('error', 'Unknown error')}")
            print(f"  💡 This usually means no repositories are configured or enabled")
        else:
            print(f"❌ Start monitoring failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"  Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"  Response: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to http://localhost:5000")
        print("   Make sure the RepoSense AI web interface is running")
    except Exception as e:
        print(f"❌ Error testing start monitoring: {e}")

def check_repositories():
    """Check what repositories are configured"""
    print(f"\n📋 Checking configured repositories...")
    
    try:
        response = requests.get('http://localhost:5000/api/repositories', timeout=10)
        if response.status_code == 200:
            repos = response.json()
            print(f"Found {len(repos)} repositories:")
            
            for repo in repos:
                name = repo.get('name', 'Unknown')
                enabled = repo.get('enabled', False)
                last_revision = repo.get('last_revision', 0)
                
                print(f"  - {name}")
                print(f"    Enabled: {'✅' if enabled else '❌'}")
                print(f"    Last revision: {last_revision}")
                
                if 'reposense_cpp_test' in name.lower():
                    print(f"    🎯 This is your target repository!")
                    if enabled:
                        print(f"    ✅ Ready for monitoring")
                    else:
                        print(f"    ❌ Repository is disabled - enable it first")
        else:
            print(f"❌ Could not get repositories (status: {response.status_code})")
            
    except Exception as e:
        print(f"❌ Error checking repositories: {e}")

if __name__ == "__main__":
    check_repositories()
    test_start_monitoring()
    
    print(f"\n💡 Instructions:")
    print(f"  1. If start succeeded: Periodic monitoring is now active")
    print(f"  2. If 'no repositories configured': Add/enable repositories first")
    print(f"  3. If monitoring started: Check logs for 'Checking for new commits' messages")
    print(f"  4. Revision 9 should be detected within the next 5-minute scan cycle")
