#!/usr/bin/env python3
"""
Test script to verify that processing status correctly shows idle vs. busy state
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unified_document_processor import UnifiedDocumentProcessor
from config_manager import Config<PERSON><PERSON><PERSON>

def test_processing_status():
    """Test that processing status correctly shows idle when no work is being done"""
    print("🔍 Testing processing status accuracy...")
    
    # Initialize config manager and processor
    config_manager = ConfigManager('data/config.json')
    processor = UnifiedDocumentProcessor(config_manager=config_manager)
    
    print("📊 Starting processor...")
    processor.start()
    
    # Give threads time to start
    time.sleep(1)
    
    # Check initial status (should be idle)
    stats = processor.get_stats()
    current_tasks = processor._get_current_tasks()
    
    print(f"\n📈 Initial Status:")
    print(f"   Running: {stats['running']}")
    print(f"   Queue size: {stats['queue_size']}")
    print(f"   Active threads: {stats['active_threads']}")
    print(f"   Busy threads: {len(processor.busy_threads)}")
    print(f"   Current tasks: {len(current_tasks)}")
    
    # Check if status correctly shows idle
    is_idle = (stats['queue_size'] == 0 and 
               stats['active_threads'] == 0 and 
               len(current_tasks) == 0)
    
    if is_idle:
        print("✅ Status correctly shows IDLE when no processing is happening")
        result = True
    else:
        print("❌ Status incorrectly shows PROCESSING when idle")
        print(f"   Current tasks details: {current_tasks}")
        result = False
    
    # Clean up
    print("\n🛑 Stopping processor...")
    processor.stop()
    
    return result

if __name__ == "__main__":
    success = test_processing_status()
    print(f"\n{'🎉 Test passed!' if success else '❌ Test failed!'}")
    sys.exit(0 if success else 1)
