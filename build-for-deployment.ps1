# RepoSense AI Docker Image Build & Deploy Script for Windows
# Builds a production-ready Docker image for remote server deployment

param(
    [switch]$Push,
    [string]$Deploy = "",
    [string]$Save = "",
    [string]$Tag = "latest",
    [switch]$Help
)

# Configuration
$ImageName = "reposense-ai"
$RegistryPrefix = ""  # Set to your registry if using one (e.g., "your-registry.com/")
$RemoteUser = "fvaneijk"
$RemotePath = "/home/<USER>/home-ai-system"

# Colors for output (Windows PowerShell compatible)
function Write-Success { param($Message) Write-Host "[✓] $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "[!] $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "[✗] $Message" -ForegroundColor Red }
function Write-Step { param($Message) Write-Host "[STEP] $Message" -ForegroundColor Blue }
function Write-Info { param($Message) Write-Host "    $Message" -ForegroundColor Cyan }

Write-Host "🐳 RepoSense AI Docker Build & Deploy Script" -ForegroundColor Green
Write-Host "==============================================" -ForegroundColor Green

if ($Help) {
    Write-Host ""
    Write-Host "Usage: .\build-for-deployment.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Push                     Push image to registry"
    Write-Host "  -Deploy SERVER_IP         Deploy to remote server"
    Write-Host "  -Save FILENAME            Save image to tar file"
    Write-Host "  -Tag TAG                  Set image tag (default: latest)"
    Write-Host "  -Help                     Show this help"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\build-for-deployment.ps1                                    # Build image locally"
    Write-Host "  .\build-for-deployment.ps1 -Save reposense-ai.tar            # Build and save to file"
    Write-Host "  .\build-for-deployment.ps1 -Deploy *************             # Build and deploy to server"
    Write-Host "  .\build-for-deployment.ps1 -Tag v1.0.0 -Push                 # Build, tag, and push to registry"
    exit 0
}

# Check if we're in the right directory
if (!(Test-Path "reposense_ai")) {
    Write-Error "reposense_ai directory not found. Please run this script from the parent directory."
    exit 1
}

if (!(Test-Path "reposense_ai\Dockerfile")) {
    Write-Error "Dockerfile not found in reposense_ai\. Please check the directory structure."
    exit 1
}

Write-Step "1. Preparing build context..."

# Validate build context
Write-Info "Validating build context..."
$requiredFiles = @("requirements.txt", "entrypoint.sh", "reposense_ai_app.py")
foreach ($file in $requiredFiles) {
    if (!(Test-Path "reposense_ai\$file")) {
        Write-Error "$file not found in reposense_ai\"
        exit 1
    }
}

Write-Success "Build context validated"

Write-Step "2. Building Docker image..."

# Build the image
$FullImageName = "${RegistryPrefix}${ImageName}:${Tag}"

Write-Info "Building image: $FullImageName"
Write-Info "Build context: .\reposense_ai"

try {
    $buildResult = docker build -t $FullImageName .\reposense_ai
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker image built successfully!"
    } else {
        Write-Error "Docker build failed!"
        exit 1
    }
} catch {
    Write-Error "Docker build failed: $_"
    exit 1
}

# Show image info
try {
    $imageInfo = docker images $FullImageName --format "{{.Size}}"
    Write-Info "Image size: $imageInfo"
} catch {
    Write-Warning "Could not get image size information"
}

Write-Step "3. Testing built image..."

# Test the image
Write-Info "Running quick test of the built image..."
try {
    $testContainer = docker run --rm -d --name reposense-ai-test -p 5001:5000 $FullImageName
    if ($LASTEXITCODE -eq 0) {
        Start-Sleep -Seconds 5
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5001/health" -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "Image test passed - health check successful"
            } else {
                Write-Warning "Image test warning - health check returned status $($response.StatusCode)"
            }
        } catch {
            Write-Warning "Image test warning - health check failed (may be normal if Ollama not available)"
        }
        docker stop reposense-ai-test | Out-Null
    } else {
        Write-Warning "Could not test image (port 5001 may be in use)"
    }
} catch {
    Write-Warning "Could not test image: $_"
}

# Handle different deployment options
if ($Save -ne "") {
    Write-Step "4. Saving image to file..."
    
    $OutputFile = $Save
    if ($OutputFile -eq "") {
        $OutputFile = "reposense-ai-${Tag}.tar"
    }
    
    Write-Info "Saving image to: $OutputFile"
    try {
        docker save $FullImageName -o $OutputFile
        if ($LASTEXITCODE -eq 0) {
            $fileSize = (Get-Item $OutputFile).Length
            $fileSizeFormatted = "{0:N2} MB" -f ($fileSize / 1MB)
            Write-Success "Image saved successfully! Size: $fileSizeFormatted"
            Write-Info "Transfer to remote server with: scp $OutputFile user@server:/path/"
            Write-Info "Load on remote server with: docker load -i $OutputFile"
        } else {
            Write-Error "Failed to save image to file"
            exit 1
        }
    } catch {
        Write-Error "Failed to save image to file: $_"
        exit 1
    }
}

if ($Push) {
    Write-Step "4. Pushing to registry..."
    
    if ($RegistryPrefix -eq "") {
        Write-Error "RegistryPrefix not set. Please configure your registry in the script."
        exit 1
    }
    
    Write-Info "Pushing image: $FullImageName"
    try {
        docker push $FullImageName
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Image pushed successfully!"
        } else {
            Write-Error "Failed to push image"
            exit 1
        }
    } catch {
        Write-Error "Failed to push image: $_"
        exit 1
    }
}

if ($Deploy -ne "") {
    Write-Step "4. Deploying to remote server..."
    
    # Save image to temporary file
    $TempFile = "reposense-ai-deploy-${Tag}.tar"
    Write-Info "Creating deployment package..."
    
    try {
        docker save $FullImageName -o $TempFile
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to create deployment package"
            exit 1
        }
        
        # Transfer and deploy using SCP and SSH
        Write-Info "Transferring to $Deploy..."
        
        # Use scp to transfer file
        $scpCommand = "scp `"$TempFile`" `"${RemoteUser}@${Deploy}:${RemotePath}/`""
        Write-Info "Running: $scpCommand"
        
        $scpResult = cmd /c $scpCommand
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Transfer completed"
            
            # Deploy on remote server using SSH
            Write-Info "Deploying on remote server..."
            
            $sshCommands = @"
cd ${RemotePath}
echo "Loading Docker image..."
docker load -i ${TempFile}
rm ${TempFile}

echo "Stopping existing container..."
docker-compose stop reposense-ai || true

echo "Starting RepoSense AI with new image..."
docker-compose up -d reposense-ai

echo "Checking status..."
docker-compose ps reposense-ai
"@
            
            $sshCommand = "ssh `"${RemoteUser}@${Deploy}`" `"$sshCommands`""
            Write-Info "Running deployment commands on remote server..."
            
            $sshResult = cmd /c $sshCommand
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Deployment completed successfully!"
                Write-Info "Check status with: ssh ${RemoteUser}@${Deploy} 'cd ${RemotePath} && docker-compose logs reposense-ai'"
            } else {
                Write-Error "Deployment failed"
                exit 1
            }
        } else {
            Write-Error "Failed to transfer image to remote server"
            exit 1
        }
    } catch {
        Write-Error "Deployment failed: $_"
        exit 1
    } finally {
        # Clean up local temp file
        if (Test-Path $TempFile) {
            Remove-Item $TempFile -Force
        }
    }
}

Write-Host ""
Write-Success "🎉 Build process completed successfully!"
Write-Host ""
Write-Host "Image Details:" -ForegroundColor Blue
Write-Host "  Name: $FullImageName"
try {
    $imageSize = docker images $FullImageName --format "{{.Size}}"
    Write-Host "  Size: $imageSize"
} catch {
    Write-Host "  Size: Unknown"
}
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Blue

if ($Deploy -eq "" -and $Save -eq "" -and !$Push) {
    Write-Host "  1. Save to file: .\build-for-deployment.ps1 -Save reposense-ai.tar"
    Write-Host "  2. Deploy to server: .\build-for-deployment.ps1 -Deploy YOUR_SERVER_IP"
    Write-Host "  3. Push to registry: .\build-for-deployment.ps1 -Push"
    Write-Host "  4. Run locally: docker run -p 5000:5000 $FullImageName"
}

if ($Deploy -ne "") {
    Write-Host "  • Access web interface: http://${Deploy}:5000"
    Write-Host "  • Check logs: ssh ${RemoteUser}@${Deploy} 'cd ${RemotePath} && docker-compose logs reposense-ai'"
}
