import sqlite3

conn = sqlite3.connect('/app/data/documents.db')
cursor = conn.cursor()

# Count total documents for our repository
cursor.execute('SELECT COUNT(*) FROM historical_documentation WHERE repository_id LIKE "a73daee8%"')
total = cursor.fetchone()[0]
print(f'Total documents: {total}')

# Get latest 5 documents
cursor.execute('SELECT id, repository_id, revision, created_at FROM historical_documentation WHERE repository_id LIKE "a73daee8%" ORDER BY revision DESC LIMIT 5')
results = cursor.fetchall()
print('\nLatest 5 documents:')
for row in results:
    print(f'ID: {row[0]}, Revision: {row[2]}, Created: {row[3]}')

conn.close()
