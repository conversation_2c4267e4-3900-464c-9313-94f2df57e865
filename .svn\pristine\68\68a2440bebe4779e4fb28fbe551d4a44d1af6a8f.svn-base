# User Guide

This guide covers how to use the RepoSense AI system for day-to-day operations, including user management, repository configuration, and monitoring workflows.

## Getting Started

### Accessing the Web Interface

1. **Open your browser** and navigate to the RepoSense AI URL (default: http://localhost:5000)
2. **Dashboard Overview**: The main dashboard shows system status and recent activity
3. **Navigation**: Use the sidebar to access different sections

### Initial Setup Workflow

1. **Configure System Settings** (Configuration page)
2. **Add Users** (User Management page)
3. **Add Repositories** (Repository Management page)
4. **Start Monitoring** (Dashboard)

## Web Interface Features

### Modern Interface Design

The application features a modern, responsive web interface with:

- **Sidebar Navigation**: Clean sidebar with intuitive navigation between sections
- **Modern Design**: Contemporary styling with Inter font, subtle shadows, and smooth animations
- **Responsive Layout**: Mobile-friendly design that adapts to different screen sizes
- **Status Indicators**: Animated status indicators with pulsing effects for running services
- **Card-based Layout**: Organized content in cards with hover effects and rounded corners
- **Network Accessible**: Web interface is accessible from any device on your network

### Core Interface Features

- **Dashboard**: Monitor status, control the service, view statistics
- **Configuration**: Complete settings management via web forms
- **Logs**: Advanced log management with multi-level filtering, search, and real-time monitoring
- **Documents**: Browse and manage AI-generated documentation with markdown rendering
- **API**: RESTful endpoints for integration

### Advanced Features

- **Real-time Status Updates**: Live monitoring of service status with dual timestamp tracking
- **One-Click Controls**: Start/stop monitoring, run manual checks, refresh repository status
- **Form-based Configuration**: Simplified single-file configuration with environment overrides
- **Auto-refresh**: Log viewing and status updates refresh automatically
- **Ollama Testing**: Built-in connection testing for AI services
- **Enhanced Document Management**: Professional PDF exports, AI processing transparency
- **Smart Filter-Aware Operations**: Context-aware delete functionality that respects active filters
- **Persistent User Preferences**: Display settings automatically saved and restored across sessions
- **Intelligent Interface Organization**: Logically grouped filters with visual styling and clear sections
- **Streamlined Controls**: Simplified refresh functionality with clear, purposeful actions
- **Mobile Support**: Fully responsive design works on all devices

### Enhanced Document Features

#### **Professional Document Downloads**
- **Multiple Formats**: Download documents as high-quality PDF or formatted Markdown
- **Syntax Highlighting**: Color-coded diffs with proper syntax highlighting in both formats
- **AI Processing Information**: Complete transparency about AI analysis in all exports
- **Professional Formatting**: Clean typography, proper tables, and structured layouts

#### **AI Processing Transparency**
- **Model Information**: See exactly which AI model processed each document
- **Processing Timestamps**: Know when documents were analyzed by RepoSense AI
- **Analysis Results**: View code review recommendations, risk levels, and documentation impact
- **Host Information**: Complete visibility into AI processing infrastructure

#### **Enhanced Document Operations**
- **Smart Filter-Aware Delete**: Revolutionary delete functionality that respects active filters
  - Context-aware deletion prevents accidental system-wide operations
  - Filter-specific warning messages clearly indicate what will be deleted
  - Professional bulk operations with comprehensive safety checks
  - Real-time feedback on operation progress and results
- **Persistent User Preferences**: Seamless experience with automatically saved settings
  - Sort preferences (date, repository, author, revision, size) remembered across sessions
  - View mode selection (table, cards, repository groups) persists between visits
  - Pagination settings (10, 25, 50, 100 per page) maintained automatically
  - Smart conflict resolution between URL parameters and saved preferences
- **Intelligent Interface Organization**: Professional, intuitive filter grouping
  - **Search & Basic Filters**: Search, repository, and author selection
  - **Time Range**: Date-based filtering with clear visual separation
  - **AI Analysis Filters**: Code review, documentation impact, and risk assessment
  - **Display & Organization**: Sorting, view mode, and pagination controls
  - Enhanced visual styling with section backgrounds and icons
- **Streamlined Controls**: Simplified and purposeful interface actions
  - Clear "Refresh" button for quick page updates
  - "Clear Cache & Refresh" for thorough data refresh and cache clearing
  - Eliminated disruptive auto-refresh that interrupted user workflow
  - Context-aware tooltips and enhanced user messaging

#### **Enhanced Repository Management**
- **Dual Timestamps**: Separate tracking for commit dates (from repository) and processing dates
- **Repository Discovery**: Advanced SVN discovery with SSL support and protocol fallback
- **Branch Detection**: Automatic discovery of trunk, branches, and tags within repositories
- **Status Refresh**: Manual refresh button for real-time repository status updates

## User Management

### Adding Users

1. **Navigate to Users page** from the sidebar
2. **Click "Add User"** button
3. **Fill in user details**:
   - Username (unique identifier)
   - Email address (for notifications)
   - Full name (display name)
   - Role (Admin, Manager, Developer, Viewer)
   - Phone and department (optional)
   - Notification preferences

4. **Click "Add User"** to save

### User Roles

**Admin**
- Full system access
- Can manage all users and repositories
- System configuration access
- All notification options

**Manager**
- User management capabilities
- Repository configuration
- Team oversight functions
- Department-level notifications

**Developer**
- Repository access and monitoring
- Code change notifications
- Documentation access
- Project-specific alerts

**Viewer**
- Read-only access to repositories
- Basic monitoring information
- Limited notification options
- Report access only

### Managing User Notifications

**Global Notifications**
- Enable "Receive all notifications" for system-wide alerts
- Useful for administrators and managers
- Includes all repository changes

**Repository-Specific Notifications**
- Subscribe to specific repositories
- Targeted notifications for relevant projects
- Reduces notification noise

**Notification Types**
- Code change alerts
- Documentation updates
- System status messages
- Error notifications

## Repository Management

### Adding Repositories Manually

1. **Navigate to Repositories page**
2. **Click "Add Repository"**
3. **Enter repository details**:
   - Name (display name)
   - SVN URL
   - Authentication credentials (if required)
   - Initial settings

4. **Save and enable monitoring**

### Repository Discovery

**Automatic Discovery**
1. **Click "Discover Repositories"** from Repositories page
2. **Enter SVN server details**:
   - Base URL of SVN server
   - Authentication credentials
   - Search depth (how deep to scan)

3. **Click "Start Discovery"**
4. **Review discovered repositories**
5. **Import selected repositories**

**Discovery Tips**
- Use authentication if your server requires it
- Lower search depth for faster results
- Repositories are identified by standard SVN structure (trunk, branches, tags)
- Imported repositories start disabled for safety

### Repository Configuration

**Basic Settings**
- Repository name and description
- SVN URL and credentials
- Monitoring status (enabled/disabled)
- Polling interval

**User Assignments**
- Assign users to repositories
- Configure notification recipients
- Set access permissions
- Manage team associations

**Advanced Settings**
- Custom polling intervals
- Specific branch monitoring
- File type filters
- Documentation preferences

## Monitoring Operations

### Dashboard Overview

**System Status**
- Overall monitoring status
- Active repositories count
- Recent activity summary
- Error notifications

**Repository Status**
- Individual repository health
- Last check timestamps
- Revision information
- Change summaries

**User Activity**
- Recent user actions
- Notification history
- System usage statistics

### Monitoring Workflow

1. **System automatically polls repositories** at configured intervals
2. **Detects changes** in SVN repositories
3. **Generates documentation** using AI (if configured)
4. **Sends notifications** to relevant users
5. **Stores documentation** in organized structure

### Viewing Repository Changes

**Change Detection**
- Automatic detection of new commits
- File modification tracking
- Author and timestamp information
- Commit message analysis

**Documentation Generation**
- AI-powered change summaries
- Technical documentation creation
- Code analysis and insights
- Formatted output files

**Notification Delivery**
- Email notifications to assigned users
- HTML-formatted change summaries
- Direct links to documentation
- Customizable notification content

## Configuration Management

### System Configuration

**Ollama AI Settings**
- Model selection (qwen3, codellama, mistral, llama3.2)
- API endpoint configuration
- Timeout and performance settings
- Documentation preferences

**Email Configuration**
- SMTP server settings
- Authentication credentials
- Email formatting options
- Delivery preferences

**Web Interface Settings**
- Port and host configuration
- Security settings
- Session management
- UI customization

### Monitoring Settings

**Polling Configuration**
- Global monitoring interval
- Repository-specific intervals
- Retry policies
- Error handling

**Output Settings**
- File storage locations
- Directory organization
- Retention policies
- File permissions

## Workflows and Best Practices

### Setting Up a New Project

1. **Create project users** with appropriate roles
2. **Discover or add repositories** for the project
3. **Assign users to repositories** based on responsibilities
4. **Configure notification preferences** for team members
5. **Test monitoring** with a small change
6. **Adjust settings** based on team feedback

### Managing Team Notifications

**For Managers**
- Enable global notifications for oversight
- Subscribe to critical repositories
- Configure summary reports
- Monitor team activity

**For Developers**
- Subscribe to relevant repositories only
- Enable detailed change notifications
- Configure development-focused alerts
- Use repository-specific settings

**For Viewers**
- Limited notification subscriptions
- Read-only access to documentation
- Summary reports only
- Project status updates

### Troubleshooting Common Issues

**Repository Not Updating**
1. Check repository status on dashboard
2. Verify SVN credentials
3. Test repository accessibility
4. Review error logs

**Missing Notifications**
1. Verify user email settings
2. Check notification preferences
3. Confirm repository assignments
4. Test email configuration

**AI Documentation Issues**
1. Verify Ollama service status
2. Check model availability
3. Review API connectivity
4. Examine generation logs

## Advanced Features

### Bulk Operations

**User Management**
- Import users from CSV
- Bulk role assignments
- Mass notification updates
- Team-based operations

**Repository Management**
- Bulk repository import
- Mass configuration updates
- Batch enable/disable
- Group operations

### Integration Options

**API Access**
- RESTful API endpoints
- Programmatic configuration
- Status monitoring
- Automated operations

**External Tools**
- Webhook integrations
- Third-party notifications
- Custom reporting
- Data export options

### Customization

**Notification Templates**
- Custom email formats
- Branded communications
- Personalized content
- Multi-language support

**Documentation Formats**
- Custom AI prompts
- Output formatting
- Template customization
- Brand integration

## Maintenance Tasks

### Regular Maintenance

**Daily**
- Monitor system status
- Review error notifications
- Check disk space usage
- Verify service health

**Weekly**
- Review user activity
- Update repository settings
- Clean up old files
- Performance monitoring

**Monthly**
- User access review
- Configuration backup
- System updates
- Security audit

### Backup and Recovery

**Configuration Backup**
- Regular config.json backups
- User data export
- Repository settings backup
- System state snapshots

**Recovery Procedures**
- Configuration restoration
- User data recovery
- Repository re-synchronization
- Service restart procedures

## Getting Help

### Built-in Help

**Web Interface**
- Contextual help tooltips
- Form validation messages
- Error explanations
- Status indicators

**Documentation**
- Comprehensive guides
- Configuration examples
- Troubleshooting steps
- Best practices

### Support Resources

**System Logs**
- Application logs for debugging
- Error tracking and analysis
- Performance monitoring
- Audit trails

**Community Support**
- Documentation updates
- Feature requests
- Bug reports
- Usage examples

## Advanced Log Management

### Multi-Level Log Filtering

The enhanced log interface provides powerful filtering capabilities for efficient debugging and monitoring:

**Filter Controls**
- **Multi-Selection Checkboxes**: Filter by specific log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- **Visual Indicators**: Color-coded badges showing count of each log type
- **Quick Selection**: "All" and "None" buttons for rapid filter management
- **State Persistence**: Filter preferences are remembered across browser sessions

**Using Log Filters**
1. **Access Logs**: Navigate to the Logs page from the main menu
2. **Select Levels**: Check/uncheck log level checkboxes to filter entries
3. **View Results**: Log display updates instantly with filtered content
4. **Save State**: Your filter preferences are automatically saved

### Real-Time Log Search

**Search Functionality**
- **Live Search**: Type in the search box to filter log entries in real-time
- **Result Highlighting**: Matching entries are highlighted for easy identification
- **Search Statistics**: View count of matching entries vs. total entries
- **Clear Search**: One-click button to clear search and show all entries

**Search Tips**
- Search works across all visible log entries (respects current filters)
- Use specific terms like error codes, function names, or timestamps
- Combine with log level filtering for targeted debugging
- Search is case-insensitive for easier use

### Auto-Refresh Controls

**Pause/Resume Functionality**
- **Auto-Refresh**: Logs automatically refresh every 10 seconds by default
- **Pause Control**: Click "Pause" to stop auto-refresh during analysis
- **Resume Control**: Click "Resume" to restart automatic updates
- **Smart Refresh**: Auto-refresh pauses when you're actively scrolling

**Status Indicators**
- **Live Updates**: Visual indicator shows when auto-refresh is active
- **Pause Status**: Clear indication when auto-refresh is paused
- **Entry Counts**: Real-time display of visible vs. total log entries

### Log Management Tools

**Manual Cleanup**
- **Size Monitoring**: View current log file size in the interface
- **Cleanup Button**: Manual cleanup when logs exceed configured size limits
- **Backup Creation**: Automatic backup of full logs before cleanup
- **Confirmation**: Safety confirmation before performing cleanup operations

**Log Download**
- **Export Logs**: Download current log file with timestamped filename
- **Full Content**: Downloaded file contains complete log history
- **Analysis Ready**: Downloaded logs are ready for external analysis tools

**Configuration Options**
- **Cleanup Thresholds**: Configure maximum log file size before cleanup
- **Retention Policy**: Set number of recent log entries to keep after cleanup
- **Auto-Rotation**: Automatic log rotation with configurable backup count

### Professional Interface Features

**Visual Enhancements**
- **Dark Theme**: Professional dark-themed log container for reduced eye strain
- **Color Coding**: Different colors for each log level (DEBUG: gray, INFO: cyan, WARNING: orange, ERROR: red, CRITICAL: purple)
- **Hover Effects**: Interactive hover effects for better user experience
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

**User Experience**
- **Resizable Container**: Drag to resize log viewing area to your preference
- **Scroll Management**: Automatic scroll to bottom for new entries
- **Filter Status**: Clear indication of active filters and their effects
- **Performance**: Efficient handling of large log files without browser slowdown

## Advanced Features (v2.1.0)

### User Feedback System

RepoSense AI now includes a comprehensive user feedback system for tracking code reviews, documentation quality, and risk assessments.

#### Code Review Feedback
1. **Access Document**: Navigate to any document in the document listing
2. **Open Document View**: Click on the document to view details
3. **Locate Feedback Section**: Scroll to "User Feedback & Review" section
4. **Set Review Status**: Choose from:
   - ✅ **Approved**: Code review completed and approved
   - 🔄 **Needs Changes**: Code review completed but changes required
   - ❌ **Rejected**: Code review completed and rejected
   - ⏳ **In Progress**: Code review currently in progress
5. **Add Comments**: Provide detailed review comments
6. **Set Reviewer**: Record who performed the review
7. **Submit**: Click "Update Code Review" to save feedback

#### Documentation Quality Ratings
1. **Rate Documentation**: Use 1-5 star rating system
   - ⭐ **1 Star**: Poor quality, needs major improvement
   - ⭐⭐ **2 Stars**: Below average, needs improvement
   - ⭐⭐⭐ **3 Stars**: Average quality, acceptable
   - ⭐⭐⭐⭐ **4 Stars**: Good quality, minor improvements possible
   - ⭐⭐⭐⭐⭐ **5 Stars**: Excellent quality, no improvements needed
2. **Add Comments**: Provide specific feedback on documentation quality
3. **Record Reviewer**: Note who evaluated the documentation
4. **Submit**: Click "Update Documentation Feedback" to save

#### Risk Assessment Overrides
1. **Review AI Assessment**: Check the AI-generated risk level
2. **Override if Needed**: Select different risk level:
   - 🔴 **HIGH**: Critical changes requiring immediate attention
   - 🟡 **MEDIUM**: Moderate changes requiring review
   - 🟢 **LOW**: Minor changes with minimal impact
3. **Justify Override**: Provide comments explaining the override decision
4. **Record Assessor**: Note who made the risk assessment
5. **Submit**: Click "Update Risk Assessment" to save

#### Viewing Feedback Status
- **Document Listing**: Color-coded badges show feedback status at a glance
- **Status Indicators**: Quick visual indicators for review status, ratings, and risk levels
- **Timestamps**: All feedback includes creation and update timestamps

### Advanced Diff Viewing

The system now provides sophisticated diff viewing capabilities with multiple formats and on-demand generation.

#### Accessing Diff Content
1. **From Document Listing**: Use the dropdown menu next to each document
2. **Choose Format**: Select either:
   - **Document + Unified Diff**: Traditional unified diff format
   - **Document + Side-by-Side Diff**: Modern side-by-side comparison
3. **View Results**: Diff content loads dynamically

#### Diff Format Options

**Unified Diff Format:**
- Traditional text-based diff display
- Shows additions (+) and deletions (-) in context
- Compact format suitable for code review
- Copy-friendly for sharing and documentation

**Side-by-Side Diff Format:**
- Modern HTML table-based comparison
- Left column shows original content
- Right column shows modified content
- Color-coded highlighting:
  - 🟢 **Green**: Added lines
  - 🔴 **Red**: Deleted lines
  - ⚪ **White**: Unchanged context lines
- Line numbers for easy reference

#### Format Switching
1. **In Document View**: Use radio button toggles to switch formats
2. **Dynamic Loading**: Format changes without page reload
3. **Persistent Selection**: Format preference maintained during session

#### Binary File Handling
- **Automatic Detection**: System detects binary files (PDFs, images, etc.)
- **Appropriate Messaging**: Clear indication when diff is not available
- **No Crashes**: Robust handling prevents system errors

### Enhanced Historical Scanning

#### Revolutionary Partial Range Scanning
- **Flexible Revision Ranges**: Scan any revision sequence regardless of previous scanning history
  - Support for non-sequential scanning (scan revisions 1-4 after scanning 5-8)
  - Database-based duplicate detection eliminates scanning conflicts
  - Intelligent revision filtering based on actual document existence
- **Force Rescan Capability**: Professional reprocessing controls for enterprise workflows
  - Optional "Force rescan existing revisions" checkbox in Advanced Options
  - Complete document deletion and recreation for fresh AI analysis
  - Perfect for testing new AI models, debugging, or compliance audits
  - Enhanced logging with detailed progress tracking and transparency

#### Accurate Progress Tracking
- **Range-Aware Progress**: Progress calculated relative to selected revision range
- **Real-Time Updates**: Accurate percentage display during scanning
- **Meaningful Display**: Shows "3/10 (processing)" instead of confusing revision numbers

#### Hybrid AI Analysis
- **Fast Processing**: Heuristic pattern matching for common cases
- **AI Fallback**: LLM analysis when heuristics are insufficient
- **Consistent Results**: More reliable metadata extraction across diverse commits
- **Quality Assurance**: Validation and normalization of AI responses

#### Robust Error Handling
- **Encoding Support**: Handles UTF-8, Latin-1, CP1252, and other encodings
- **Binary File Support**: Graceful handling of non-text files
- **Network Resilience**: Proper error recovery for network issues
- **User-Friendly Messages**: Clear error messages instead of technical exceptions

### Best Practices

#### Using User Feedback Effectively
1. **Regular Reviews**: Establish regular code review cycles
2. **Consistent Ratings**: Use documentation rating scale consistently across team
3. **Detailed Comments**: Provide specific, actionable feedback
4. **Risk Assessment**: Override AI assessments when domain knowledge differs
5. **Team Coordination**: Use reviewer fields to track responsibility

#### Optimizing Diff Viewing
1. **Choose Appropriate Format**: Use unified for code review, side-by-side for visual comparison
2. **Context Understanding**: Review surrounding context for better understanding
3. **Binary File Awareness**: Understand when diffs are not available for binary files
4. **Performance Consideration**: Large diffs may take time to generate

#### Historical Scanning Efficiency
1. **Flexible Range Scanning**: Scan any revision ranges in any order (e.g., 1-4 after 5-8)
2. **Force Rescan When Needed**: Use "Force rescan existing revisions" for testing or debugging
3. **Batch Size Optimization**: Use appropriate batch sizes for your repository size
4. **Progress Monitoring**: Use progress display to estimate completion time
5. **Error Review**: Check logs for any scanning issues or failures
6. **Database-Based Tracking**: System automatically tracks which revisions need processing

This enhanced functionality provides comprehensive tools for code review, documentation quality management, and repository analysis while maintaining system robustness and user experience.
