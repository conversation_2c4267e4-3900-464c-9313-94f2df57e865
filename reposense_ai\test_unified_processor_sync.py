#!/usr/bin/env python3
"""
Synchronous test of UnifiedDocumentProcessor core functionality
"""

import logging
from datetime import datetime
from unified_document_processor import UnifiedDocumentProcessor
from document_database import DocumentRecord
from models import CommitInfo, RepositoryConfig
from monitor_service import MonitorService

def setup_logging():
    """Setup logging to see processing details"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_unified_processor_sync():
    """Test the unified processor synchronously"""
    print("🧪 Testing UnifiedDocumentProcessor (Synchronous)")
    print("=" * 55)
    
    setup_logging()
    
    # Initialize monitor service for Ollama client
    monitor_service = MonitorService("data/config.json")
    
    print(f"📋 Configuration:")
    print(f"  Default model: {monitor_service.config.ollama_model}")
    print(f"  Risk assessment model: {monitor_service.config.ollama_model_risk_assessment}")
    
    # Create unified processor
    processor = UnifiedDocumentProcessor(
        output_dir=monitor_service.config.output_dir,
        db_path="/app/data/documents.db",
        ollama_client=monitor_service.ollama_client,
        config_manager=monitor_service.config_manager,
        max_concurrent_tasks=1
    )
    
    print(f"\n🔧 Processor setup:")
    print(f"  Has Ollama client: {processor.ollama_client is not None}")
    print(f"  MetadataExtractor has Ollama client: {processor.metadata_extractor.ollama_client is not None}")
    
    # Test the core processing methods directly (synchronous)
    print(f"\n🔍 Test 1: Historical Scan Processing (Direct)")
    
    # Create test data
    test_commit_info = CommitInfo(
        revision="999",
        author="test_user",
        date=datetime.now().isoformat(),
        message="Implement OAuth2 authentication with JWT tokens",
        changed_paths=["src/auth/oauth.py", "config/auth.json"],
        diff="@@ -0,0 +1,10 @@\n+# OAuth2 Implementation\n+class OAuth2Handler:\n+    def authenticate(self, token):\n+        return validate_jwt(token)",
        repository_id="test-repo-sync",
        repository_name="reposense_ai"
    )
    
    test_repo_config = RepositoryConfig(
        name="reposense_ai",
        url="https://github.com/test/reposense_ai",
        enabled=True
    )
    
    test_documentation = """
# Revision 999 - OAuth2 Security Enhancement

## Summary
This revision introduces OAuth2 authentication with JWT tokens for enhanced security.

## Code Review Recommendation
**RECOMMENDED** - HIGH PRIORITY - Authentication changes require thorough security review.

## Risk Level
**HIGH RISK** - Authentication system changes require extensive testing.

## Documentation Impact
**YES** - API documentation requires updates for new OAuth2 flow.

## Changes Made
- Implemented OAuth2 authorization server
- Added JWT token validation middleware
- Enhanced security logging and audit trails
"""
    
    # Create a processing task manually
    from unified_document_processor import ProcessingTask, ProcessingSource
    task = ProcessingTask(
        source=ProcessingSource.HISTORICAL_SCAN,
        priority=8,
        commit_info=test_commit_info,
        content=test_documentation,
        repository_config=test_repo_config
    )
    
    # Process the task directly (synchronous)
    print(f"🔄 Processing historical scan task...")
    success = processor._process_historical_scan_task(task)
    
    if success:
        print(f"✅ Historical scan task processed successfully!")
        
        # Verify the document was stored with AI model information
        from document_database import DocumentDatabase
        db = DocumentDatabase("/app/data/documents.db")
        
        # Look for the document we just created
        docs = db.get_documents(limit=10)
        test_doc = None
        
        for doc in docs:
            if doc.repository_id == "test-repo-sync" and doc.revision == 999:
                test_doc = doc
                break
        
        if test_doc:
            print(f"\n📋 Document verification:")
            print(f"  ID: {test_doc.id}")
            print(f"  Filename: {test_doc.filename}")
            print(f"  AI Model Used: {test_doc.ai_model_used or 'Not set'}")
            print(f"  Risk Level: {test_doc.risk_level}")
            print(f"  Code Review: {test_doc.code_review_recommended}")
            print(f"  Doc Impact: {test_doc.documentation_impact}")
            
            if test_doc.ai_model_used and test_doc.ai_model_used != monitor_service.config.ollama_model:
                print(f"\n🎉 SUCCESS: Specialized model used!")
                print(f"🎯 Used: {test_doc.ai_model_used}")
                print(f"📊 Default would have been: {monitor_service.config.ollama_model}")
                return True
            elif test_doc.ai_model_used == monitor_service.config.ollama_model:
                print(f"\n⚠️  WARNING: Default model used instead of specialized")
                print(f"🔍 Used: {test_doc.ai_model_used}")
                print(f"🎯 Expected: {monitor_service.config.ollama_model_risk_assessment}")
                return False
            else:
                print(f"\n❌ ISSUE: No AI model recorded")
                return False
        else:
            print(f"\n❌ ERROR: Test document not found in database")
            return False
    else:
        print(f"❌ Historical scan task processing failed")
        return False

if __name__ == "__main__":
    success = test_unified_processor_sync()
    
    print("\n" + "=" * 55)
    if success:
        print("🎉 UnifiedDocumentProcessor core functionality is working!")
        print("🎯 Specialized models are being used and tracked correctly.")
        print("🔄 The async queue system should work correctly too.")
    else:
        print("❌ UnifiedDocumentProcessor core functionality needs debugging.")
        print("🔍 Check the logs above for specific issues.")
