#!/usr/bin/env python3
"""
Check if the system is actually scanning for repository commits/check-ins
"""

import sqlite3
import os
import json
import requests
from datetime import datetime, timedelta

def check_monitoring_status():
    """Check if monitoring is actually active"""
    print("🔍 Checking Repository Commit Scanning Status...")
    
    try:
        # Check the API status
        response = requests.get('http://localhost:5000/api/status', timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"\n📊 System Status from API:")
            print(f"  Monitoring running: {status.get('monitoring_running', 'Unknown')}")
            print(f"  Last check time: {status.get('last_check_time', 'Never')}")
            print(f"  Enabled repositories: {status.get('enabled_repositories', 0)}")
            
            if status.get('monitoring_running'):
                print(f"  ✅ Monitoring is ACTIVE")
            else:
                print(f"  ❌ Monitoring is NOT ACTIVE")
        else:
            print(f"❌ Could not get status from API (status: {response.status_code})")
            
    except Exception as e:
        print(f"❌ Error checking API status: {e}")

def check_recent_scanning_activity():
    """Check for recent scanning activity in logs"""
    print(f"\n📋 Checking Recent Scanning Activity...")
    
    try:
        log_path = "reposense_ai/data/reposense_ai.log"
        
        if not os.path.exists(log_path):
            print(f"❌ Log file not found")
            return
            
        # Read recent logs
        with open(log_path, 'r') as f:
            lines = f.readlines()
        
        # Look for scanning-related activity in last 100 lines
        recent_lines = lines[-100:]
        
        scanning_keywords = [
            'checking for new commits',
            'found new commits',
            'monitoring started',
            'daemon',
            'run_once',
            'check_for_new_commits',
            'process_commit',
            'latest revision'
        ]
        
        scanning_activity = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in scanning_keywords):
                scanning_activity.append(line.strip())
        
        if scanning_activity:
            print(f"🔍 Recent scanning activity found:")
            for line in scanning_activity[-10:]:  # Show last 10
                print(f"  {line}")
        else:
            print(f"❌ No recent scanning activity found")
            print(f"   This suggests periodic commit scanning is NOT active")
        
        # Check for any errors
        error_lines = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed']):
                error_lines.append(line.strip())
        
        if error_lines:
            print(f"\n⚠️ Recent errors:")
            for line in error_lines[-5:]:
                print(f"  {line}")
                
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

def check_repository_connectivity():
    """Check if repositories are accessible"""
    print(f"\n🔗 Checking Repository Connectivity...")
    
    try:
        # Try to get repository info from the API
        response = requests.get('http://localhost:5000/api/repositories', timeout=10)
        if response.status_code == 200:
            repos = response.json()
            print(f"📋 Found {len(repos)} repositories via API:")
            
            for repo in repos:
                name = repo.get('name', 'Unknown')
                url = repo.get('url', 'Unknown')
                enabled = repo.get('enabled', False)
                last_revision = repo.get('last_revision', 0)
                repo_type = repo.get('type', 'Unknown')
                
                print(f"  - {name} ({repo_type})")
                print(f"    URL: {url}")
                print(f"    Enabled: {'✅' if enabled else '❌'}")
                print(f"    Last revision: {last_revision}")
                
                if 'reposense_cpp_test' in name.lower():
                    print(f"    🎯 This is your reposense_cpp_test repository!")
                    if enabled:
                        print(f"    ✅ Repository is enabled for monitoring")
                        print(f"    📊 Should be checking for commits beyond revision {last_revision}")
                    else:
                        print(f"    ❌ Repository is DISABLED - no scanning will occur")
                print()
        else:
            print(f"❌ Could not get repositories from API")
            
    except Exception as e:
        print(f"❌ Error checking repositories: {e}")

def check_commit_detection_logic():
    """Check if the commit detection logic is working"""
    print(f"\n🔧 Checking Commit Detection Logic...")
    
    try:
        # Check database for recent activity
        db_path = "reposense_ai/data/documents.db"
        
        if not os.path.exists(db_path):
            print(f"❌ Database not found")
            return
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check when documents were last processed
        cursor.execute("""
            SELECT repository_name, MAX(revision), MAX(processed_time), COUNT(*)
            FROM documents 
            GROUP BY repository_name
            ORDER BY MAX(processed_time) DESC
        """)
        
        repos = cursor.fetchall()
        print(f"📊 Repository Processing Summary:")
        
        for repo_name, max_revision, last_processed, doc_count in repos:
            print(f"  - {repo_name}")
            print(f"    Latest revision processed: {max_revision}")
            print(f"    Last processed: {last_processed}")
            print(f"    Total documents: {doc_count}")
            
            # Check if processing is recent (within last hour)
            if last_processed:
                try:
                    processed_time = datetime.fromisoformat(last_processed.replace('Z', '+00:00'))
                    time_diff = datetime.now() - processed_time.replace(tzinfo=None)
                    
                    if time_diff < timedelta(hours=1):
                        print(f"    ✅ Recent activity (within last hour)")
                    elif time_diff < timedelta(hours=24):
                        print(f"    ⏳ Some activity (within last day)")
                    else:
                        print(f"    ❌ No recent activity (over 24 hours ago)")
                except:
                    print(f"    ❓ Could not parse processing time")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking commit detection: {e}")

def test_manual_check():
    """Test if manual check works"""
    print(f"\n🧪 Testing Manual Repository Check...")
    
    try:
        response = requests.post('http://localhost:5000/api/check', timeout=60)
        print(f"Manual check status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Manual check succeeded!")
            print(f"  Repositories checked: {data.get('repositories_checked', 0)}")
            print(f"  Status: {data.get('status', 'Unknown')}")
            
            if data.get('repositories_checked', 0) > 0:
                print(f"  ✅ System CAN scan repositories")
                print(f"  💡 This means the scanning logic works, but periodic scanning may be disabled")
            else:
                print(f"  ❌ No repositories were checked")
        else:
            try:
                error_data = response.json()
                print(f"❌ Manual check failed: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"❌ Manual check failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing manual check: {e}")

if __name__ == "__main__":
    check_monitoring_status()
    check_recent_scanning_activity()
    check_repository_connectivity()
    check_commit_detection_logic()
    test_manual_check()
    
    print(f"\n💡 Summary:")
    print(f"  ✅ If monitoring is active + repositories enabled + recent activity: Scanning IS working")
    print(f"  ❌ If monitoring inactive OR no repositories OR no recent activity: Scanning is NOT working")
    print(f"  🔄 If manual check works but no periodic activity: Periodic scanning is disabled")
    print(f"  🎯 For revision 9: Check if repository last_revision >= 9 and monitoring is active")
