#!/usr/bin/env python3
"""
Analyze why qwen2.5:14b vs qwen3:14b for specialized models
"""

import sys
import os
import json
import requests
sys.path.append('/app')

def analyze_available_models():
    """Analyze the available qwen models on the server"""
    print("🤖 Analyzing Available Qwen Models")
    print("=" * 40)
    
    try:
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        ollama_host = config.get('ollama_host', 'http://localhost:11434')
        response = requests.get(f"{ollama_host}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            # Find qwen models
            qwen_models = []
            for model in models:
                name = model.get('name', '')
                if 'qwen' in name.lower():
                    size = model.get('size', 0)
                    size_gb = size / (1024**3) if size else 0
                    qwen_models.append({
                        'name': name,
                        'size_gb': size_gb,
                        'size_bytes': size
                    })
            
            print("📊 Available Qwen Models:")
            for model in sorted(qwen_models, key=lambda x: x['size_gb'], reverse=True):
                print(f"   📦 {model['name']} ({model['size_gb']:.1f}GB)")
            
            # Compare qwen2.5:14b vs qwen3:14b specifically
            qwen25_14b = next((m for m in qwen_models if m['name'] == 'qwen2.5:14b'), None)
            qwen3_14b = next((m for m in qwen_models if m['name'] == 'qwen3:14b'), None)
            
            print(f"\n🔍 Direct Comparison:")
            if qwen25_14b:
                print(f"   ✅ qwen2.5:14b: {qwen25_14b['size_gb']:.1f}GB")
            else:
                print(f"   ❌ qwen2.5:14b: Not available")
            
            if qwen3_14b:
                print(f"   ✅ qwen3:14b: {qwen3_14b['size_gb']:.1f}GB")
            else:
                print(f"   ❌ qwen3:14b: Not available")
            
            return qwen_models, qwen25_14b, qwen3_14b
            
        else:
            print(f"❌ Cannot connect to Ollama: {response.status_code}")
            return [], None, None
            
    except Exception as e:
        print(f"❌ Error analyzing models: {e}")
        return [], None, None

def compare_qwen_versions():
    """Compare qwen2.5 vs qwen3 capabilities"""
    print(f"\n📊 Qwen2.5 vs Qwen3 Comparison")
    print("=" * 35)
    
    comparison = {
        'Model Generation': {
            'qwen2.5': 'Mature, stable generation with proven performance',
            'qwen3': 'Newer generation with potential improvements but less tested'
        },
        'Training Data': {
            'qwen2.5': 'Extensive training on diverse datasets, well-optimized',
            'qwen3': 'Updated training data but may have different characteristics'
        },
        'Stability': {
            'qwen2.5': 'Highly stable, widely tested in production environments',
            'qwen3': 'Newer model, less production testing, potential edge cases'
        },
        'Performance': {
            'qwen2.5': 'Consistent, predictable performance across tasks',
            'qwen3': 'May have better performance in some areas, worse in others'
        },
        'Compatibility': {
            'qwen2.5': 'Excellent compatibility with existing prompts and workflows',
            'qwen3': 'May require prompt adjustments, different response patterns'
        },
        'Documentation': {
            'qwen2.5': 'Extensive documentation, known best practices',
            'qwen3': 'Limited documentation, fewer known optimization techniques'
        }
    }
    
    print("🔍 Detailed Comparison:")
    for aspect, details in comparison.items():
        print(f"\n📊 {aspect}:")
        print(f"   🟡 Qwen2.5: {details['qwen2.5']}")
        print(f"   🔵 Qwen3: {details['qwen3']}")

def analyze_task_specific_suitability():
    """Analyze which model is better for specific RepoSense tasks"""
    print(f"\n🎯 Task-Specific Suitability Analysis")
    print("=" * 45)
    
    tasks = {
        'Documentation Generation': {
            'requirements': 'Consistent writing style, clear explanations, user-friendly language',
            'qwen25_score': 9,
            'qwen3_score': 8,
            'winner': 'qwen2.5:14b',
            'reason': 'Proven excellent writing capabilities, consistent style'
        },
        'Risk Assessment': {
            'requirements': 'Logical reasoning, security awareness, consistent analysis',
            'qwen25_score': 9,
            'qwen3_score': 8,
            'winner': 'qwen2.5:14b',
            'reason': 'Stable reasoning patterns, well-tested security analysis'
        },
        'Code Review': {
            'requirements': 'Technical accuracy, code understanding, specific recommendations',
            'qwen25_score': 7,
            'qwen3_score': 8,
            'winner': 'qwen3:14b (but use qwen3-coder:latest instead)',
            'reason': 'Newer model may have better code understanding, but specialized coder model is better'
        },
        'Email Generation': {
            'requirements': 'Professional tone, clear communication, appropriate detail level',
            'qwen25_score': 9,
            'qwen3_score': 7,
            'winner': 'qwen2.5:14b',
            'reason': 'Proven professional communication capabilities'
        }
    }
    
    print("📊 Task Suitability Scores (1-10):")
    for task, analysis in tasks.items():
        print(f"\n🎯 {task}:")
        print(f"   📋 Requirements: {analysis['requirements']}")
        print(f"   🟡 Qwen2.5: {analysis['qwen25_score']}/10")
        print(f"   🔵 Qwen3: {analysis['qwen3_score']}/10")
        print(f"   🏆 Winner: {analysis['winner']}")
        print(f"   💡 Reason: {analysis['reason']}")

def analyze_production_considerations():
    """Analyze production deployment considerations"""
    print(f"\n🏭 Production Deployment Considerations")
    print("=" * 45)
    
    considerations = {
        'Reliability': {
            'importance': 'CRITICAL',
            'qwen25': 'Excellent - proven in production environments',
            'qwen3': 'Unknown - newer model with less production testing',
            'recommendation': 'qwen2.5:14b for critical systems'
        },
        'Consistency': {
            'importance': 'HIGH',
            'qwen25': 'Very consistent responses across similar inputs',
            'qwen3': 'May have different response patterns, less predictable',
            'recommendation': 'qwen2.5:14b for consistent user experience'
        },
        'Resource Usage': {
            'importance': 'MEDIUM',
            'qwen25': 'Well-optimized, predictable resource consumption',
            'qwen3': 'May have different resource patterns, less optimized',
            'recommendation': 'qwen2.5:14b for predictable performance'
        },
        'Prompt Compatibility': {
            'importance': 'HIGH',
            'qwen25': 'Excellent compatibility with existing prompts',
            'qwen3': 'May require prompt adjustments and testing',
            'recommendation': 'qwen2.5:14b to avoid prompt re-engineering'
        },
        'Error Handling': {
            'importance': 'HIGH',
            'qwen25': 'Well-understood error patterns and recovery',
            'qwen3': 'Unknown error patterns, may have new edge cases',
            'recommendation': 'qwen2.5:14b for predictable error handling'
        }
    }
    
    print("🔍 Production Analysis:")
    for factor, analysis in considerations.items():
        print(f"\n📊 {factor} ({analysis['importance']} importance):")
        print(f"   🟡 Qwen2.5: {analysis['qwen25']}")
        print(f"   🔵 Qwen3: {analysis['qwen3']}")
        print(f"   💡 Recommendation: {analysis['recommendation']}")

def provide_final_recommendation():
    """Provide final recommendation with reasoning"""
    print(f"\n🎯 Final Recommendation: Why qwen2.5:14b")
    print("=" * 45)
    
    print("🏆 RECOMMENDED: qwen2.5:14b for Documentation and Risk Assessment")
    print()
    
    reasons = [
        "🔒 PROVEN RELIABILITY: Extensively tested in production environments",
        "📊 CONSISTENT PERFORMANCE: Predictable, stable responses across tasks",
        "📚 EXCELLENT WRITING: Superior documentation and communication capabilities",
        "🛡️ STABLE REASONING: Well-tested logical analysis and risk assessment",
        "⚙️ PROMPT COMPATIBILITY: Works perfectly with existing RepoSense prompts",
        "🔧 KNOWN OPTIMIZATION: Well-understood performance characteristics",
        "📖 EXTENSIVE DOCUMENTATION: Proven best practices and optimization techniques",
        "🎯 PRODUCTION READY: Battle-tested in real-world deployments"
    ]
    
    print("✅ Key Reasons:")
    for reason in reasons:
        print(f"   {reason}")
    
    print(f"\n🔵 About qwen3:14b:")
    qwen3_considerations = [
        "🆕 NEWER MODEL: May have improvements but less tested",
        "❓ UNKNOWN PATTERNS: Different response characteristics, less predictable",
        "🧪 EXPERIMENTAL: Requires more testing to validate in production",
        "⚙️ PROMPT TUNING: May need prompt adjustments for optimal performance",
        "📊 MIXED RESULTS: Could be better in some areas, worse in others"
    ]
    
    for consideration in qwen3_considerations:
        print(f"   {consideration}")
    
    print(f"\n💡 ALTERNATIVE APPROACH:")
    print("   🧪 You COULD try qwen3:14b if you want to experiment")
    print("   📊 Test both models side-by-side to compare results")
    print("   🔄 Easy to switch back if qwen3 doesn't perform as well")
    print("   🎯 For production stability, qwen2.5:14b is the safer choice")

def show_optimal_configuration():
    """Show the optimal configuration recommendation"""
    print(f"\n⚙️ Optimal Configuration Recommendation")
    print("=" * 45)
    
    print("🎯 RECOMMENDED SPECIALIZED MODEL SETUP:")
    
    config = {
        'Documentation Model': {
            'recommended': 'qwen2.5:14b',
            'alternative': 'qwen3:14b (experimental)',
            'reason': 'Proven excellent writing and communication capabilities'
        },
        'Risk Assessment Model': {
            'recommended': 'qwen2.5:14b',
            'alternative': 'qwen3:14b (experimental)',
            'reason': 'Stable, consistent reasoning and security analysis'
        },
        'Code Review Model': {
            'recommended': 'qwen3-coder:latest',
            'alternative': 'codeqwen:7b-chat-v1.5-q8_0',
            'reason': 'Code-specialized model is better than general model for code tasks'
        }
    }
    
    for model_type, details in config.items():
        print(f"\n📦 {model_type}:")
        print(f"   🏆 Recommended: {details['recommended']}")
        print(f"   🔄 Alternative: {details['alternative']}")
        print(f"   💡 Reason: {details['reason']}")
    
    print(f"\n🧪 EXPERIMENTAL SETUP (if you want to try qwen3):")
    print("   📚 Documentation Model: qwen3:14b")
    print("   🔒 Risk Assessment Model: qwen3:14b")
    print("   💻 Code Review Model: qwen3-coder:latest")
    print("   ⚠️  Note: Monitor results carefully, may need prompt adjustments")

if __name__ == "__main__":
    qwen_models, qwen25, qwen3 = analyze_available_models()
    compare_qwen_versions()
    analyze_task_specific_suitability()
    analyze_production_considerations()
    provide_final_recommendation()
    show_optimal_configuration()
    
    print(f"\n🎯 ANSWER: Why not qwen3:14b?")
    print("=" * 35)
    print("✅ You COULD use qwen3:14b - it's available and may work well")
    print("🛡️ qwen2.5:14b is recommended for PRODUCTION STABILITY")
    print("🧪 qwen3:14b is good for EXPERIMENTATION")
    print()
    print("💡 SUGGESTION:")
    print("   1. Start with qwen2.5:14b for proven reliability")
    print("   2. Test qwen3:14b in parallel if you want to experiment")
    print("   3. Compare results and switch if qwen3 performs better")
    print("   4. For critical production use, stick with proven qwen2.5:14b")
    print()
    print("🚀 Either way, you'll get MUCH better results than the current smollm2:latest!")
