<!DOCTYPE html>
<html>
<head>
    <title>Clipboard Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Clipboard Copy Test</h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Test Content</h5>
                <button class="btn btn-sm btn-light" onclick="copyToClipboard()">
                    <i class="fas fa-copy"></i> Copy
                </button>
            </div>
            <div class="card-body">
                <pre id="rawContent">This is test content for clipboard copying.
It has multiple lines.
And some special characters: !@#$%^&*()
Unicode: 🎉 ✅ 🔧</pre>
            </div>
        </div>
        
        <div class="mt-3">
            <p><strong>Test Instructions:</strong></p>
            <ol>
                <li>Click the "Copy" button above</li>
                <li>The button should change to "Copied!" with green background</li>
                <li>Try pasting (Ctrl+V) in a text editor to verify it worked</li>
                <li>If it fails, you should see "Failed" with red background</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard() {
            const content = document.getElementById('rawContent').textContent;
            const btn = event.target.closest('button');
            const originalHTML = btn.innerHTML;
            
            // Function to show success feedback
            function showSuccess() {
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.classList.remove('btn-light', 'btn-outline-primary');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-light');
                }, 2000);
            }
            
            // Function to show error feedback
            function showError(message) {
                console.error('Copy failed:', message);
                btn.innerHTML = '<i class="fas fa-times"></i> Failed';
                btn.classList.remove('btn-light', 'btn-outline-primary');
                btn.classList.add('btn-danger');
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.classList.remove('btn-danger');
                    btn.classList.add('btn-light');
                }, 2000);
                
                // Also show a more user-friendly message
                alert('Failed to copy to clipboard. Please try selecting and copying the text manually.');
            }
            
            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(content)
                    .then(showSuccess)
                    .catch(err => {
                        console.warn('Modern clipboard API failed, trying fallback:', err);
                        fallbackCopyToClipboard(content, showSuccess, showError);
                    });
            } else {
                // Use fallback method
                fallbackCopyToClipboard(content, showSuccess, showError);
            }
        }

        function fallbackCopyToClipboard(text, onSuccess, onError) {
            // Create a temporary textarea element
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            
            try {
                textArea.focus();
                textArea.select();
                
                // Try to copy using execCommand
                const successful = document.execCommand('copy');
                if (successful) {
                    onSuccess();
                } else {
                    onError('execCommand copy failed');
                }
            } catch (err) {
                onError('execCommand not supported: ' + err.message);
            } finally {
                document.body.removeChild(textArea);
            }
        }
    </script>
</body>
</html>
