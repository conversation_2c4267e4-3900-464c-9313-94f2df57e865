<!DOCTYPE html>
<html>
<head>
    <title>PDF Download Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>PDF Download Test</h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Test Document</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="downloadDocument('markdown')">
                            <i class="fab fa-markdown me-2"></i>Markdown (.md)
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="downloadDocument('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>PDF (.pdf)
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <pre id="rawContent">This is test document content.

It has multiple paragraphs and demonstrates the PDF generation capability.

## Features Tested:
- Document content formatting
- Diff content inclusion
- PDF generation with ReportLab
- Clean markdown download

This content should appear properly formatted in both Markdown and PDF downloads.</pre>
                
                <!-- Hidden element with raw diff content -->
                <div id="rawDiffContent" style="display: none;">Index: test.py
===================================================================
--- test.py	(revision 122)
+++ test.py	(revision 123)
@@ -1,3 +1,4 @@
 def hello_world():
     print("Hello, World!")
+    print("This is a new line!")
 
 if __name__ == "__main__":
     hello_world()</div>
            </div>
        </div>
        
        <div class="mt-3">
            <p><strong>Test Instructions:</strong></p>
            <ol>
                <li>Click the "Download" dropdown above</li>
                <li>Try both "Markdown (.md)" and "PDF (.pdf)" options</li>
                <li>Verify both downloads work correctly</li>
                <li>Check that PDF includes formatted content and diff</li>
            </ol>
        </div>
    </div>

    <script>
        function downloadDocument(format = 'markdown') {
            if (format === 'pdf') {
                downloadAsPDF();
            } else {
                downloadAsMarkdown();
            }
        }

        function downloadAsMarkdown() {
            const filename = 'test-document.md';

            // Build complete markdown content with document information
            let content = '# Test Document - Revision 123\n\n';

            // Add revision summary document section
            content += '## Revision Summary Document\n\n';
            content += '**Repository:** test-repository\n\n';
            content += '**Revision:** 123\n\n';
            content += '**Author:** test-user\n\n';
            content += '**Date:** 2025-01-01 12:00:00 UTC\n\n';
            content += '**Filename:** test-document.md\n\n';
            content += '**Size:** 2.6 KB\n\n';
            content += '**Path:** repositories/test-repository/docs/test-document.md\n\n';
            content += '**Commit Message:** Test commit for PDF generation\n\n';
            content += '**Files Changed:**\n\n';
            content += '- `/test.py`\n';
            content += '- `/README.md`\n';
            content += '\n2 files changed in this commit\n\n';
            content += '---\n\n';

            // Add main document content
            content += '## Document Content\n\n';
            content += document.getElementById('rawContent').textContent;

            // Add diff content if available
            let diffElement = document.getElementById('rawDiffContent');
            if (diffElement && diffElement.textContent.trim()) {
                const cleanDiffContent = diffElement.textContent.trim();
                content += '\n\n---\n\n## Code Changes (Diff)\n\n```diff\n' + cleanDiffContent + '\n```';
            }

            const blob = new Blob([content], { type: 'text/markdown' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        function downloadAsPDF() {
            // Show loading indicator
            const originalButtons = document.querySelectorAll('.btn-group .dropdown-toggle');
            originalButtons.forEach(btn => {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
                btn.disabled = true;
            });

            // Prepare content for PDF generation
            let content = document.getElementById('rawContent').textContent;
            const documentInfo = {
                title: 'Test Document - Revision 123',
                repository: 'test-repository',
                revision: '123',
                author: 'test-user',
                date: '2025-01-01 12:00:00 UTC',
                commit_message: 'Test commit for PDF generation',
                filename: 'test-document.md',
                size: '2.6 KB',
                path: 'repositories/test-repository/docs/test-document.md',
                changed_files: ['/test.py', '/README.md'],
                files_changed_count: '2 files changed in this commit'
            };

            // Add diff content if available
            let diffContent = '';
            let diffElement = document.getElementById('rawDiffContent');
            if (diffElement && diffElement.textContent.trim()) {
                diffContent = diffElement.textContent.trim();
            }

            // Simulate API call (replace with actual endpoint)
            setTimeout(() => {
                alert('PDF generation would work with a real document ID and API endpoint!');
                
                // Restore buttons
                originalButtons.forEach(btn => {
                    btn.innerHTML = '<i class="fas fa-download"></i> Download';
                    btn.disabled = false;
                });
            }, 2000);
        }
    </script>
</body>
</html>
