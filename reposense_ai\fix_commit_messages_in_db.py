#!/usr/bin/env python3
"""
Fix commit messages in database by replacing AI-generated summaries with actual repository commit messages
"""

import sys
sys.path.append('/app')

from document_database import DocumentDatabase
from repository_backends import get_backend_manager
from config_manager import Config<PERSON><PERSON><PERSON>

def fix_commit_messages():
    """Fix commit messages in database"""
    print("🔧 Starting commit message fix...")
    
    # Load database
    db = DocumentDatabase('/app/data/documents.db')
    docs = db.get_documents(limit=100)
    
    # Load config and backend
    config_manager = ConfigManager('/app/data/config.json')
    config = config_manager.load_config()
    backend_manager = get_backend_manager()
    
    fixed_count = 0
    skipped_count = 0
    error_count = 0
    
    print(f"📊 Found {len(docs)} documents to check")
    print()
    
    for doc in sorted(docs, key=lambda x: x.revision):
        print(f"--- Processing Revision {doc.revision} ---")
        
        # Skip if already has correct commit message (not AI-generated)
        if doc.commit_message and not doc.commit_message.endswith("(AI-generated summary)"):
            print(f"✅ Already has original message: \"{doc.commit_message[:50]}...\"")
            skipped_count += 1
            print()
            continue
        
        # Find repository config
        repo = None
        for r in config.repositories:
            if r.name == doc.repository_name:
                repo = r
                break
        
        if not repo:
            print(f"❌ Repository {doc.repository_name} not found in config")
            error_count += 1
            print()
            continue
        
        # Get backend and commit info
        backend = backend_manager.get_backend_for_repository(repo, config)
        if not backend:
            print(f"❌ Could not get backend for repository {doc.repository_name}")
            error_count += 1
            print()
            continue
        
        commit_info = backend.get_commit_info(repo, str(doc.revision))
        if not commit_info:
            print(f"❌ Could not get commit info for revision {doc.revision}")
            error_count += 1
            print()
            continue
        
        # Check if repository has actual commit message
        if not commit_info.message or commit_info.message.strip() == "":
            print(f"⚠️  Repository has no commit message - keeping AI summary")
            skipped_count += 1
            print()
            continue
        
        # Update database with correct commit message
        print(f"🔄 Updating commit message:")
        print(f"   Old: \"{doc.commit_message[:80]}...\"")
        print(f"   New: \"{commit_info.message[:80]}...\"")
        
        try:
            # Create updated document record
            from document_database import DocumentRecord
            from datetime import datetime
            
            updated_doc = DocumentRecord(
                id=doc.id,
                repository_id=doc.repository_id,
                repository_name=doc.repository_name,
                revision=doc.revision,
                date=doc.date,
                filename=doc.filename,
                filepath=doc.filepath,
                size=doc.size,
                author=commit_info.author or doc.author,  # Also update author if available
                commit_message=commit_info.message,  # Use original commit message
                changed_paths=commit_info.changed_paths or doc.changed_paths,  # Update changed paths if available
                code_review_recommended=doc.code_review_recommended,
                code_review_priority=doc.code_review_priority,
                documentation_impact=doc.documentation_impact,
                risk_level=doc.risk_level,
                file_modified_time=doc.file_modified_time,
                processed_time=datetime.now(),  # Update processed time
                ai_model_used=doc.ai_model_used,
                heuristic_context=doc.heuristic_context
            )
            
            # Update in database
            success = db.upsert_document(updated_doc)
            if success:
                print(f"✅ Successfully updated revision {doc.revision}")
                fixed_count += 1
            else:
                print(f"❌ Failed to update revision {doc.revision}")
                error_count += 1
                
        except Exception as e:
            print(f"❌ Error updating revision {doc.revision}: {e}")
            error_count += 1
        
        print()
    
    print("=" * 50)
    print("🎉 COMMIT MESSAGE FIX COMPLETE!")
    print(f"✅ Fixed: {fixed_count} documents")
    print(f"⏭️  Skipped: {skipped_count} documents (already correct)")
    print(f"❌ Errors: {error_count} documents")
    print(f"📊 Total: {len(docs)} documents processed")
    
    if fixed_count > 0:
        print()
        print("🔄 Restart the application to see the changes in the web interface")

if __name__ == "__main__":
    fix_commit_messages()
