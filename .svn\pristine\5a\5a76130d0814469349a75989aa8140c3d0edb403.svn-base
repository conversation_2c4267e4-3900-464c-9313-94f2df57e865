#!/usr/bin/env python3
"""
Check database schema to verify heuristic_context column exists
"""

import sqlite3
import os

def check_database_schema():
    """Check the current database schema"""
    
    db_path = "/app/data/documents.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        print("🔍 Checking database schema...")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get table info
            cursor.execute("PRAGMA table_info(documents)")
            columns = cursor.fetchall()
            
            print(f"\n📋 Documents table has {len(columns)} columns:")
            for i, (cid, name, type_, notnull, default, pk) in enumerate(columns):
                print(f"  {i+1:2d}. {name:<30} {type_:<15} {'NOT NULL' if notnull else 'NULL':<8} {'PK' if pk else ''}")
            
            # Check specifically for heuristic_context
            column_names = [col[1] for col in columns]
            if 'heuristic_context' in column_names:
                print(f"\n✅ heuristic_context column found at position {column_names.index('heuristic_context') + 1}")
            else:
                print(f"\n❌ heuristic_context column NOT found")
                print(f"Available columns: {', '.join(column_names)}")
            
            return 'heuristic_context' in column_names
                
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        return False

if __name__ == "__main__":
    check_database_schema()
