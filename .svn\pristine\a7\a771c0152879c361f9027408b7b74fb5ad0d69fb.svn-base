#!/usr/bin/env python3
"""
Debug why revision 9 is not appearing
"""

import sqlite3
import os
import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def check_current_database_state():
    """Check what revisions are currently in the database"""
    print("🔍 Checking current database state...")
    
    try:
        db_path = "reposense_ai/data/documents.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all reposense_cpp_test documents
        cursor.execute("""
            SELECT repository_name, revision, date, commit_message, processed_time
            FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%' 
            ORDER BY revision DESC
        """)
        
        docs = cursor.fetchall()
        print(f"\n📋 Current reposense_cpp_test documents ({len(docs)} total):")
        
        revisions_found = []
        for doc in docs:
            repo_name, revision, date, commit_msg, processed_time = doc
            revisions_found.append(revision)
            print(f"  Revision {revision}: {commit_msg[:50]}{'...' if len(commit_msg) > 50 else ''}")
            print(f"    Processed: {processed_time}")
        
        if revisions_found:
            max_revision = max(revisions_found)
            print(f"\n📊 Summary:")
            print(f"  Revisions found: {sorted(revisions_found)}")
            print(f"  Highest revision: {max_revision}")
            
            if 9 in revisions_found:
                print(f"  ✅ Revision 9 IS in database")
            else:
                print(f"  ❌ Revision 9 is NOT in database")
                if max_revision < 9:
                    print(f"  ⏳ System has only processed up to revision {max_revision}")
                else:
                    print(f"  ❓ System has processed beyond revision 9 but skipped it")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

def check_monitoring_logs():
    """Check recent monitoring activity"""
    print(f"\n📋 Checking recent monitoring logs...")
    
    try:
        log_path = "reposense_ai/data/reposense_ai.log"
        
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Look for recent monitoring activity (last 100 lines)
        recent_lines = lines[-100:]
        
        monitoring_activity = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in [
                'checking for new commits',
                'found new commits',
                'monitoring started',
                'daemon',
                'revision',
                'process_commit'
            ]):
                monitoring_activity.append(line.strip())
        
        if monitoring_activity:
            print(f"🔍 Recent monitoring activity:")
            for line in monitoring_activity[-10:]:  # Show last 10
                print(f"  {line}")
        else:
            print(f"❌ No recent monitoring activity found")
            print(f"   Monitoring may not be running despite API success")
            
        # Check for any errors
        error_lines = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed']):
                error_lines.append(line.strip())
        
        if error_lines:
            print(f"\n⚠️ Recent errors:")
            for line in error_lines[-5:]:
                print(f"  {line}")
                
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

def check_repository_configuration():
    """Check repository configuration details"""
    print(f"\n🔧 Checking repository configuration...")
    
    try:
        # Try to get detailed repository info
        response = requests.get('http://localhost:5000/api/status', timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"📊 System Status:")
            print(f"  Monitoring running: {status.get('monitoring_running', 'Unknown')}")
            print(f"  Enabled repositories: {status.get('enabled_repositories', 0)}")
            print(f"  Last check time: {status.get('last_check_time', 'Never')}")
        
        # Check what the manual check finds
        print(f"\n🧪 Testing manual repository check...")
        check_response = requests.post('http://localhost:5000/api/check', timeout=60)
        
        if check_response.status_code == 200:
            check_data = check_response.json()
            repos_checked = check_data.get('repositories_checked', 0)
            print(f"✅ Manual check processed {repos_checked} repositories")
            
            if repos_checked > 0:
                print(f"✅ Repository scanning is working")
                print(f"💡 Check the logs immediately after this for new activity")
            else:
                print(f"❌ No repositories were processed")
        else:
            print(f"❌ Manual check failed: {check_response.text}")
            
    except Exception as e:
        print(f"❌ Error checking repository config: {e}")

def check_repository_last_revision():
    """Check what the system thinks is the last revision"""
    print(f"\n📊 Checking repository last_revision tracking...")
    
    try:
        config_path = "reposense_ai/data/config.json"
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            repositories = config.get('repositories', [])
            print(f"📋 Repository configuration:")
            
            for repo in repositories:
                name = repo.get('name', 'Unknown')
                last_revision = repo.get('last_revision', 0)
                enabled = repo.get('enabled', False)
                url = repo.get('url', 'Unknown')
                
                print(f"  - {name}")
                print(f"    Last revision: {last_revision}")
                print(f"    Enabled: {'✅' if enabled else '❌'}")
                print(f"    URL: {url}")
                
                if 'reposense_cpp_test' in name.lower():
                    print(f"    🎯 This is your target repository!")
                    if last_revision >= 9:
                        print(f"    ✅ System knows about revision {last_revision} (>= 9)")
                        print(f"    💡 Revision 9 should have been processed")
                    else:
                        print(f"    ⏳ System only knows about revision {last_revision} (< 9)")
                        print(f"    💡 System hasn't detected revision 9 yet")
        else:
            print(f"❌ Config file not found at {config_path}")
            
    except Exception as e:
        print(f"❌ Error checking config: {e}")

def force_repository_scan():
    """Force a repository scan to see what happens"""
    print(f"\n🔄 Forcing repository scan...")
    
    try:
        print("⏳ Running manual check to force scanning...")
        response = requests.post('http://localhost:5000/api/check', timeout=120)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Forced scan completed")
            print(f"  Repositories checked: {data.get('repositories_checked', 0)}")
            
            # Wait a moment for processing
            import time
            time.sleep(5)
            
            # Check database again
            print(f"\n📊 Checking database after forced scan...")
            check_current_database_state()
            
        else:
            print(f"❌ Forced scan failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error forcing scan: {e}")

if __name__ == "__main__":
    check_current_database_state()
    check_monitoring_logs()
    check_repository_configuration()
    check_repository_last_revision()
    force_repository_scan()
    
    print(f"\n💡 Troubleshooting Steps:")
    print(f"  1. If last_revision < 9: Repository hasn't detected revision 9 yet")
    print(f"  2. If no monitoring activity: Periodic scanning isn't working")
    print(f"  3. If errors in logs: Fix the underlying issue")
    print(f"  4. If revision 9 exists in repo but not detected: Check repository URL/access")
    print(f"  5. Try restarting the container to reset monitoring state")
