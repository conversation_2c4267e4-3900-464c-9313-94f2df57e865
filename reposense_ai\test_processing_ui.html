<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Feedback UI Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Processing Feedback UI Test</h1>
        <p>This page tests the processing feedback UI components.</p>
        
        <!-- Dashboard-style Processing Status Card -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Document Processing Status</h5>
                <div id="processing-status-indicator" class="float-end" style="display: none;">
                    <span class="badge bg-warning">
                        <i class="fas fa-spinner fa-spin"></i> Processing...
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-primary mb-1" id="processed-count">0</h4>
                            <small class="text-muted">Processed</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-warning mb-1" id="queue-size">0</h4>
                            <small class="text-muted">In Queue</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-danger mb-1" id="error-count">0</h4>
                        <small class="text-muted">Errors</small>
                    </div>
                </div>
                
                <!-- Processing Tasks List -->
                <div id="current-tasks" class="mt-3" style="display: none;">
                    <hr>
                    <h6 class="text-muted mb-2">Current Processing:</h6>
                    <div id="tasks-list"></div>
                </div>
            </div>
        </div>
        
        <!-- Documents-style Processing Alert -->
        <div id="documents-processing-status" class="d-flex align-items-center mt-4" style="display: none;">
            <div class="alert alert-info mb-0 me-3">
                <i class="fas fa-spinner fa-spin me-2"></i>
                <span id="processing-message">Processing documents...</span>
            </div>
        </div>
        
        <!-- API Status -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>API Status</h5>
            </div>
            <div class="card-body">
                <div id="api-status">Testing API...</div>
                <button class="btn btn-primary mt-2" onclick="testAPI()">Test API Now</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let processingStatusInterval = null;

        function updateProcessingStatus() {
            fetch('/api/processing-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const processing = data.processing;
                        
                        // Update counters
                        document.getElementById('processed-count').textContent = processing.processed_count || 0;
                        document.getElementById('queue-size').textContent = processing.queue_size || 0;
                        document.getElementById('error-count').textContent = processing.error_count || 0;
                        
                        // Show/hide processing indicator
                        const indicator = document.getElementById('processing-status-indicator');
                        const tasksDiv = document.getElementById('current-tasks');
                        const tasksList = document.getElementById('tasks-list');
                        const documentsStatus = document.getElementById('documents-processing-status');
                        const processingMessage = document.getElementById('processing-message');
                        
                        if (processing.queue_size > 0 || processing.current_tasks.length > 0) {
                            // Show processing indicators
                            indicator.style.display = 'block';
                            documentsStatus.style.display = 'flex';
                            
                            // Update processing message
                            let message = 'Processing documents...';
                            if (processing.queue_size > 0) {
                                message = `Processing ${processing.queue_size} documents...`;
                            } else if (processing.current_tasks.length > 0) {
                                const activeTask = processing.current_tasks.find(t => t.status === 'active');
                                if (activeTask) {
                                    message = activeTask.description;
                                }
                            }
                            processingMessage.textContent = message;
                            
                            // Show current tasks
                            if (processing.current_tasks.length > 0) {
                                tasksDiv.style.display = 'block';
                                tasksList.innerHTML = '';
                                
                                processing.current_tasks.forEach(task => {
                                    const taskElement = document.createElement('div');
                                    taskElement.className = 'small mb-1';
                                    
                                    let icon = '';
                                    let badgeClass = '';
                                    if (task.status === 'pending') {
                                        icon = '<i class="fas fa-clock text-warning"></i>';
                                        badgeClass = 'bg-warning';
                                    } else if (task.status === 'active') {
                                        icon = '<i class="fas fa-spinner fa-spin text-info"></i>';
                                        badgeClass = 'bg-info';
                                    }
                                    
                                    taskElement.innerHTML = `
                                        ${icon} ${task.description}
                                        <span class="badge ${badgeClass} ms-2">${task.status}</span>
                                    `;
                                    tasksList.appendChild(taskElement);
                                });
                            } else {
                                tasksDiv.style.display = 'none';
                            }
                        } else {
                            // Hide processing indicators
                            indicator.style.display = 'none';
                            tasksDiv.style.display = 'none';
                            documentsStatus.style.display = 'none';
                        }
                        
                        // Update API status
                        document.getElementById('api-status').innerHTML = `
                            <div class="text-success">✅ API Working</div>
                            <small>Last update: ${new Date().toLocaleTimeString()}</small>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error fetching processing status:', error);
                    document.getElementById('api-status').innerHTML = `
                        <div class="text-danger">❌ API Error: ${error.message}</div>
                        <small>Last attempt: ${new Date().toLocaleTimeString()}</small>
                    `;
                });
        }

        function testAPI() {
            updateProcessingStatus();
        }

        // Start processing status updates when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateProcessingStatus();
            processingStatusInterval = setInterval(updateProcessingStatus, 3000);
        });

        // Clean up interval when page unloads
        window.addEventListener('beforeunload', function() {
            if (processingStatusInterval) {
                clearInterval(processingStatusInterval);
            }
        });
    </script>
</body>
</html>
