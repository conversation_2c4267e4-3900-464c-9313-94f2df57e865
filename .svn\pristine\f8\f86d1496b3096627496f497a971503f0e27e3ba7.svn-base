{% extends "base.html" %}

{% block title %}Configuration - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Configuration</h1>
            <p class="page-subtitle">Configure global settings for repository intelligence and private AI integration</p>
        </div>
        <button type="button" class="btn btn-outline-secondary" onclick="testOllamaConnection()">
            <i class="fas fa-plug"></i> Test Ollama Connection
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i>Configuration</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('save_config') }}">
                    <!-- SVN Server Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-server"></i> SVN Server Configuration</h6>
                            <a href="{{ url_for('repository_discovery_page') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-search"></i> Discover Repositories
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="svn_server_url" class="form-label">SVN Server Base URL</label>
                                <input type="url" class="form-control" id="svn_server_url" name="svn_server_url"
                                       value="{{ config.svn_server_url or '' }}"
                                       placeholder="http://your-server:port/svn">
                                <div class="form-text">Base URL of your SVN server (used for repository discovery)</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="svn_server_username" class="form-label">Server Username</label>
                                        <input type="text" class="form-control" id="svn_server_username" name="svn_server_username"
                                               value="{{ config.svn_server_username or '' }}"
                                               placeholder="Optional">
                                        <div class="form-text">Default username for server access</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="svn_server_password" class="form-label">Server Password</label>
                                        <input type="password" class="form-control" id="svn_server_password" name="svn_server_password"
                                               value="{{ config.svn_server_password or '' }}"
                                               placeholder="Optional">
                                        <div class="form-text">Default password for server access</div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Repository Management:</strong> Individual repositories are configured in the
                                <a href="{{ url_for('repositories_page') }}" class="alert-link">Repositories</a> section.
                                Use the "Discover Repositories" button above to automatically find and import repositories from your SVN server.
                            </div>
                        </div>
                    </div>

                    <!-- Ollama Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-robot"></i> Ollama AI</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="ollama_host" class="form-label">Ollama Host</label>
                                        <input type="text" class="form-control" id="ollama_host" name="ollama_host" 
                                               value="{{ config.ollama_host }}" required
                                               placeholder="http://ollama:11434">
                                        <div class="form-text">URL of the Ollama server</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model" class="form-label">Model</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ollama_model" name="ollama_model">
                                                <option value="">
                                                    <i class="fas fa-spinner fa-spin"></i> Loading models...
                                                </option>
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="refresh-models" title="Refresh available models">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                        <div class="form-text" id="model-status">Available models from your Ollama server</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Specialized AI Models Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-brain"></i> Specialized AI Models</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Optional:</strong> Configure specialized models for different AI tasks. If not specified, the default model will be used for all tasks.
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model_documentation" class="form-label">Product Documentation Model</label>
                                        <select class="form-select specialized-model-select" id="ollama_model_documentation" name="ollama_model_documentation">
                                            <option value="">Use default model</option>
                                        </select>
                                        <div class="form-text">Model for analyzing user-facing documentation impact</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model_code_review" class="form-label">Code Review Model</label>
                                        <select class="form-select specialized-model-select" id="ollama_model_code_review" name="ollama_model_code_review">
                                            <option value="">Use default model</option>
                                        </select>
                                        <div class="form-text">Model for code review analysis and recommendations</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model_risk_assessment" class="form-label">Risk Assessment Model</label>
                                        <select class="form-select specialized-model-select" id="ollama_model_risk_assessment" name="ollama_model_risk_assessment">
                                            <option value="">Use default model</option>
                                        </select>
                                        <div class="form-text">Model for security and risk analysis</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <h6 class="mb-3"><i class="fas fa-cogs"></i> Enhanced Prompt Settings</h6>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="use_enhanced_prompts" name="use_enhanced_prompts"
                                                   {{ 'checked' if config.use_enhanced_prompts else '' }}>
                                            <label class="form-check-label" for="use_enhanced_prompts">
                                                <strong>Use Enhanced Contextual Prompts</strong>
                                            </label>
                                        </div>
                                        <div class="form-text">Enable advanced contextual prompts that analyze repository type, change patterns, file criticality, and risk context for more accurate AI recommendations</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enhanced_prompts_fallback" name="enhanced_prompts_fallback"
                                                   {{ 'checked' if config.enhanced_prompts_fallback else '' }}>
                                            <label class="form-check-label" for="enhanced_prompts_fallback">
                                                <strong>Enhanced Prompts Fallback</strong>
                                            </label>
                                        </div>
                                        <div class="form-text">Automatically fall back to basic prompts if enhanced contextual prompts fail, ensuring system reliability</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monitoring Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-clock"></i> Monitoring</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="check_interval" class="form-label">Check Interval (seconds)</label>
                                <input type="number" class="form-control" id="check_interval" name="check_interval" 
                                       value="{{ config.check_interval }}" min="60" max="86400" required>
                                <div class="form-text">How often to check for new commits (60-86400 seconds)</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="generate_docs" name="generate_docs" 
                                               {% if config.generate_docs %}checked{% endif %}>
                                        <label class="form-check-label" for="generate_docs">
                                            Generate Documentation
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_emails" name="send_emails" 
                                               {% if config.send_emails %}checked{% endif %}>
                                        <label class="form-check-label" for="send_emails">
                                            Send Email Notifications
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-envelope"></i> Email Settings</h6>
                        </div>
                        <div class="card-body" id="email-settings-body">

                            <!-- Email Provider Presets -->
                            <div class="alert alert-info mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Quick Setup Options:</strong>
                                </div>
                                <div class="btn-group" role="group" aria-label="Email provider presets">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setMailHogConfig()">
                                        <i class="fas fa-bug"></i> MailHog (Development)
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setGmailConfig()">
                                        <i class="fab fa-google"></i> Gmail
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setOutlookConfig()">
                                        <i class="fab fa-microsoft"></i> Outlook
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearEmailConfig()">
                                        <i class="fas fa-times"></i> Custom
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>MailHog:</strong> Development email testing (emails captured, not sent).
                                        <a href="http://localhost:8025" target="_blank" class="text-decoration-none">
                                            View MailHog Web UI <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                               value="{{ config.smtp_host }}"
                                               placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                               value="{{ config.smtp_port }}" min="1" max="65535">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                               value="{{ config.smtp_username or '' }}"
                                               placeholder="Optional">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                               value="{{ config.smtp_password or '' }}"
                                               placeholder="Optional">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="email_from" class="form-label">From Address</label>
                                <input type="email" class="form-control" id="email_from" name="email_from" 
                                       value="{{ config.email_from }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="email_recipients" class="form-label">Global Recipients</label>
                                <textarea class="form-control" id="email_recipients" name="email_recipients" rows="3"
                                          placeholder="<EMAIL>, <EMAIL>">{{ config.email_recipients | join(', ') }}</textarea>
                                <div class="form-text">Global email recipients who receive notifications for ALL repositories (comma-separated). Repository-specific recipients can be configured in the Repositories section.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Web Interface Configuration -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-globe"></i> Web Interface Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="web_log_entries" class="form-label">Log Entries Display Count</label>
                                        <input type="number" class="form-control" id="web_log_entries" name="web_log_entries"
                                               value="{{ config.web_log_entries }}" min="50" max="1000" required>
                                        <div class="form-text">Number of recent log entries to display on the logs page (50-1000)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Note:</strong> Higher values may slow down the logs page loading time.
                                        Recommended range: 100-500 entries.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Log Management Configuration -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-file-alt"></i> Log Management Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_cleanup_max_size_mb" class="form-label">Manual Cleanup Trigger Size (MB)</label>
                                        <input type="number" class="form-control" id="log_cleanup_max_size_mb" name="log_cleanup_max_size_mb"
                                               value="{{ config.log_cleanup_max_size_mb }}" min="10" max="500" required>
                                        <div class="form-text">Log file size that triggers manual cleanup (10-500 MB)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_cleanup_lines_to_keep" class="form-label">Lines to Keep After Cleanup</label>
                                        <input type="number" class="form-control" id="log_cleanup_lines_to_keep" name="log_cleanup_lines_to_keep"
                                               value="{{ config.log_cleanup_lines_to_keep }}" min="100" max="10000" required>
                                        <div class="form-text">Number of recent log lines to preserve (100-10,000)</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_rotation_max_size_mb" class="form-label">Automatic Rotation Size (MB)</label>
                                        <input type="number" class="form-control" id="log_rotation_max_size_mb" name="log_rotation_max_size_mb"
                                               value="{{ config.log_rotation_max_size_mb }}" min="5" max="100" required>
                                        <div class="form-text">Log file size that triggers automatic rotation (5-100 MB)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_rotation_backup_count" class="form-label">Backup Files to Keep</label>
                                        <input type="number" class="form-control" id="log_rotation_backup_count" name="log_rotation_backup_count"
                                               value="{{ config.log_rotation_backup_count }}" min="1" max="20" required>
                                        <div class="form-text">Number of rotated backup files to retain (1-20)</div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Log Management:</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>Manual Cleanup:</strong> Triggered by the "Cleanup" button on the logs page</li>
                                    <li><strong>Automatic Rotation:</strong> Happens automatically when the log file grows too large</li>
                                    <li><strong>Backup Files:</strong> Rotated files are saved as .log.1, .log.2, etc.</li>
                                    <li><strong>Restart Required:</strong> Log rotation settings require container restart to take effect</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced System Reset -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-undo-alt"></i> System Reset & Management</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Warning:</strong> Reset operations are irreversible. Comprehensive backups are created automatically before any reset.
                            </div>

                            <!-- Quick Reset Options -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="border rounded p-3 bg-light">
                                        <h6 class="text-primary"><i class="fas fa-database me-2"></i>Quick Database Reset</h6>
                                        <p class="text-muted small mb-3">
                                            Reset only the database and documents. All configuration, repositories, and users remain unchanged.
                                        </p>
                                        <button type="button" class="btn btn-warning" onclick="resetDatabase()">
                                            <i class="fas fa-database"></i> Reset Database Only
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="border rounded p-3 bg-light">
                                        <h6 class="text-danger"><i class="fas fa-broom me-2"></i>Complete System Reset</h6>
                                        <p class="text-muted small mb-3">
                                            Reset everything to factory defaults. Use when you want a completely fresh start.
                                        </p>
                                        <button type="button" class="btn btn-danger" onclick="showCompleteResetModal()">
                                            <i class="fas fa-undo-alt"></i> Complete Reset
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- System Information -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="border rounded p-3">
                                        <h6 class="text-muted"><i class="fas fa-info-circle me-2"></i>System Information</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <strong>Database:</strong> <code>/app/data/documents.db</code><br>
                                                    <strong>Configuration:</strong> <code>/app/data/config.json</code><br>
                                                    <strong>Documents:</strong> <code>/app/data/output/repositories/</code>
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <strong>Current Status:</strong><br>
                                                    • Repositories: {{ config.repositories|length }}<br>
                                                    • Users: {{ config.users|length }}<br>
                                                    • Email Recipients: {{ config.email_recipients|length }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Reset Modal -->
<div class="modal fade" id="enhancedResetModal" tabindex="-1" aria-labelledby="enhancedResetModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="enhancedResetModalLabel">
                    <i class="fas fa-undo-alt me-2"></i>Enhanced System Reset
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Choose what to reset:</strong> Select only the components you want to reset. Automatic backups will be created for critical data.
                </div>

                <!-- Backup Information -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>What Gets Backed Up:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>config.json</strong> - All configuration (repositories, users, email, AI settings)</li>
                        <li><strong>documents.db</strong> - Document database and analysis data</li>
                        <li><strong>Repository files</strong> - Local repository checkouts (if database reset selected)</li>
                        <li><strong>Generated documents</strong> - AI-generated revision documents (if database reset selected)</li>
                        <li><strong>System logs</strong> - Current system activity log</li>
                    </ul>
                    <small class="text-muted mt-2 d-block">
                        <i class="fas fa-clock"></i> Backups are timestamped: <code>filename.backup.{timestamp}</code>
                    </small>
                </div>

                <!-- Reset Options -->
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-cogs me-2"></i>Reset Options</h6>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_database" onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_database">
                                <i class="fas fa-database me-1"></i> Database and Documents
                            </label>
                            <small class="text-muted d-block">Clear all document analysis and scan history</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_repositories" onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_repositories">
                                <i class="fas fa-code-branch me-1"></i> Repository Configuration
                            </label>
                            <small class="text-muted d-block">Remove all repository settings and credentials</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_users" onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_users">
                                <i class="fas fa-users me-1"></i> User Accounts
                            </label>
                            <small class="text-muted d-block">Remove all user accounts and permissions</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_email_config" onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_email_config">
                                <i class="fas fa-envelope me-1"></i> Email Configuration
                            </label>
                            <small class="text-muted d-block">Reset email settings and recipient lists</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_svn_config" onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_svn_config">
                                <i class="fas fa-server me-1"></i> SVN/Git Server Settings
                            </label>
                            <small class="text-muted d-block">Reset server URLs and credentials</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_ai_config" onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_ai_config">
                                <i class="fas fa-robot me-1"></i> AI Model Configuration
                            </label>
                            <small class="text-muted d-block">Reset AI model settings and preferences</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h6><i class="fas fa-eye me-2"></i>Reset Preview</h6>
                        <div id="resetPreview" class="border rounded p-3 bg-light">
                            <p class="text-muted mb-0">Select reset options above to see what will be reset...</p>
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAllReset()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNoneReset()">
                                <i class="fas fa-square"></i> Select None
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Safety Validation -->
                <div class="mt-3">
                    <div class="alert alert-info" role="alert" id="safetyValidation" style="display: none;">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Safety Check:</strong> <span id="safetyMessage"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-danger" id="executeResetBtn" onclick="executeEnhancedReset()" disabled>
                    <i class="fas fa-undo-alt"></i> Execute Reset
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Load available Ollama models
function loadOllamaModels() {
    const modelSelect = document.getElementById('ollama_model');
    const refreshBtn = document.getElementById('refresh-models');
    const statusText = document.getElementById('model-status');
    const currentValue = '{{ config.ollama_model }}';

    // Show loading state
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    refreshBtn.disabled = true;
    statusText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading models from Ollama server...';

    fetch('/api/ollama/models')
        .then(response => response.json())
        .then(data => {
            // Clear existing options
            modelSelect.innerHTML = '';

            if (data.connected && data.models.length > 0) {
                // Add available models
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    if (model === currentValue) {
                        option.selected = true;
                    }
                    modelSelect.appendChild(option);
                });

                // If current model is not in the list, add it as the first option
                if (currentValue && !data.models.includes(currentValue)) {
                    const option = document.createElement('option');
                    option.value = currentValue;
                    option.textContent = currentValue + ' (not found)';
                    option.selected = true;
                    modelSelect.insertBefore(option, modelSelect.firstChild);
                }

                statusText.innerHTML = `<i class="fas fa-check text-success"></i> Found ${data.models.length} available models`;

                // Also populate specialized model dropdowns
                populateSpecializedModels(data.models);
            } else if (!data.connected) {
                // Ollama not connected
                const option = document.createElement('option');
                option.value = currentValue || 'qwen3';
                option.textContent = (currentValue || 'qwen3') + ' (Ollama not connected)';
                option.selected = true;
                modelSelect.appendChild(option);

                statusText.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Ollama server not connected';
            } else {
                // No models available
                const option = document.createElement('option');
                option.value = currentValue || '';
                option.textContent = 'No models available';
                modelSelect.appendChild(option);

                statusText.innerHTML = '<i class="fas fa-info-circle text-info"></i> No models found on Ollama server';
            }
        })
        .catch(error => {
            console.error('Error loading models:', error);
            // Fallback to server-side models or current model
            const serverModels = {{ available_models | tojson }};
            modelSelect.innerHTML = '';

            if (serverModels && serverModels.length > 0) {
                serverModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    if (model === currentValue) {
                        option.selected = true;
                    }
                    modelSelect.appendChild(option);
                });
                statusText.innerHTML = `<i class="fas fa-check text-success"></i> Using cached models (${serverModels.length} available)`;
            } else {
                // Final fallback to current model
                const option = document.createElement('option');
                option.value = currentValue || 'qwen3';
                option.textContent = (currentValue || 'qwen3') + ' (error loading models)';
                option.selected = true;
                modelSelect.appendChild(option);
                statusText.innerHTML = '<i class="fas fa-exclamation-circle text-danger"></i> Error loading models from server';
            }
        })
        .finally(() => {
            // Reset refresh button
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshBtn.disabled = false;
        });
}

// Load models on page load
document.addEventListener('DOMContentLoaded', loadOllamaModels);

// Refresh models button
document.getElementById('refresh-models').addEventListener('click', loadOllamaModels);

// Populate specialized model dropdowns
function populateSpecializedModels(models) {
    const specializedSelects = document.querySelectorAll('.specialized-model-select');
    const currentValues = {
        'ollama_model_documentation': '{{ config.ollama_model_documentation or "" }}',
        'ollama_model_code_review': '{{ config.ollama_model_code_review or "" }}',
        'ollama_model_risk_assessment': '{{ config.ollama_model_risk_assessment or "" }}'
    };

    specializedSelects.forEach(select => {
        const currentValue = currentValues[select.id];

        // Clear existing options except the first one (Use default model)
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add available models
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            if (model === currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });

        // If current model is not in the list but is set, add it
        if (currentValue && !models.includes(currentValue)) {
            const option = document.createElement('option');
            option.value = currentValue;
            option.textContent = currentValue + ' (not found)';
            option.selected = true;
            select.appendChild(option);
        }
    });
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const checkInterval = parseInt(document.getElementById('check_interval').value);

    if (checkInterval < 60 || checkInterval > 86400) {
        alert('Check interval must be between 60 and 86400 seconds');
        e.preventDefault();
        return;
    }
});

// Show/hide email settings based on checkbox
function toggleEmailSettings() {
    const sendEmailsCheckbox = document.getElementById('send_emails');
    const emailSettings = document.getElementById('email-settings-body');

    if (!sendEmailsCheckbox || !emailSettings) {
        console.error('Email settings elements not found');
        return;
    }

    try {
        if (sendEmailsCheckbox.checked) {
            emailSettings.style.opacity = '1';
            emailSettings.style.pointerEvents = 'auto';
            emailSettings.querySelectorAll('input, textarea').forEach(el => {
                el.disabled = false;
                el.style.cursor = 'auto';
            });
        } else {
            emailSettings.style.opacity = '0.5';
            emailSettings.style.pointerEvents = 'none';
            emailSettings.querySelectorAll('input, textarea').forEach(el => {
                el.disabled = true;
                el.style.cursor = 'not-allowed';
            });
        }
    } catch (error) {
        console.error('Error toggling email settings:', error);
    }
}

document.getElementById('send_emails').addEventListener('change', toggleEmailSettings);

// Test Ollama Connection function
async function testOllamaConnection() {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    button.disabled = true;

    try {
        const response = await fetch('/api/test_ollama');
        const result = await response.json();

        if (result.connected) {
            button.innerHTML = '<i class="fas fa-check text-success"></i> Connected!';
            button.className = 'btn btn-outline-success';

            // Show success message
            showAlert('Ollama connection successful!', 'success');
        } else {
            button.innerHTML = '<i class="fas fa-times text-danger"></i> Failed';
            button.className = 'btn btn-outline-danger';

            // Show error message
            showAlert('Failed to connect to Ollama server. Please check the host URL and ensure Ollama is running.', 'danger');
        }
    } catch (error) {
        console.error('Error testing Ollama connection:', error);
        button.innerHTML = '<i class="fas fa-times text-danger"></i> Error';
        button.className = 'btn btn-outline-danger';

        // Show error message
        showAlert('Error testing Ollama connection: ' + error.message, 'danger');
    }

    // Reset button after 3 seconds
    setTimeout(() => {
        button.innerHTML = originalText;
        button.className = 'btn btn-outline-secondary';
        button.disabled = false;
    }, 3000);
}

// Helper function to show alerts
function showAlert(message, type) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-ollama-test');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-ollama-test mt-3`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert after the header
    const header = document.querySelector('.page-header');
    header.parentNode.insertBefore(alertDiv, header.nextSibling);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Initialize email settings visibility on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleEmailSettings();
});

// Email Provider Preset Functions
function setMailHogConfig() {
    document.getElementById('smtp_host').value = 'mailhog';
    document.getElementById('smtp_port').value = '1025';
    document.getElementById('smtp_username').value = '';
    document.getElementById('smtp_password').value = '';
    document.getElementById('email_from').value = 'reposense-ai@localhost';

    // Auto-populate global recipients with admin and manager emails
    document.getElementById('email_recipients').value = '<EMAIL>, <EMAIL>';

    showAlert('info', 'MailHog configuration applied with admin and manager recipients. This is for development - emails will be captured but not sent. View them at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a>');

    // Create admin and manager user accounts
    createDefaultUsers();
}

function setGmailConfig() {
    document.getElementById('smtp_host').value = 'smtp.gmail.com';
    document.getElementById('smtp_port').value = '587';
    document.getElementById('smtp_username').value = '';
    document.getElementById('smtp_password').value = '';
    document.getElementById('email_from').value = '';

    showAlert('warning', 'Gmail configuration applied. You need to:<br>1. Enable 2FA on your Gmail account<br>2. Generate an App Password<br>3. Enter your Gmail address and App Password above');
}

function setOutlookConfig() {
    document.getElementById('smtp_host').value = 'smtp-mail.outlook.com';
    document.getElementById('smtp_port').value = '587';
    document.getElementById('smtp_username').value = '';
    document.getElementById('smtp_password').value = '';
    document.getElementById('email_from').value = '';

    showAlert('info', 'Outlook configuration applied. Enter your Outlook email and password above.');
}

function clearEmailConfig() {
    document.getElementById('smtp_host').value = '';
    document.getElementById('smtp_port').value = '587';
    document.getElementById('smtp_username').value = '';
    document.getElementById('smtp_password').value = '';
    document.getElementById('email_from').value = '';

    showAlert('info', 'Email configuration cleared. Enter your custom SMTP settings.');
}

// Create default admin and manager users for MailHog testing
function createDefaultUsers() {
    const defaultUsers = [
        {
            username: 'admin',
            email: '<EMAIL>',
            full_name: 'System Administrator',
            role: 'admin',
            receive_all_notifications: 'on'  // Form expects 'on' for checkboxes
        },
        {
            username: 'manager',
            email: '<EMAIL>',
            full_name: 'Project Manager',
            role: 'manager',
            receive_all_notifications: 'on'  // Form expects 'on' for checkboxes
        }
    ];

    defaultUsers.forEach(userData => {
        fetch('/users/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(userData)
        })
        .then(response => {
            if (response.ok) {
                console.log(`✅ Created user: ${userData.username} (${userData.email})`);
            } else {
                console.log(`ℹ️ User ${userData.username} may already exist`);
            }
        })
        .catch(error => {
            console.log(`⚠️ Error creating user ${userData.username}:`, error);
        });
    });
}

// Database Management Functions
function resetDatabase() {
    if (confirm('⚠️ WARNING: This will reset the entire database and delete all documents. A backup will be created automatically. This action cannot be undone. Are you sure?')) {
        if (confirm('This is your final confirmation. Reset the database and delete all documents?')) {
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
            button.disabled = true;

            fetch('/api/database/reset', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', `Success: ${data.message}`);
                    // Optionally redirect to documents page after a delay
                    setTimeout(() => {
                        window.location.href = '/documents';
                    }, 3000);
                } else {
                    showAlert('danger', 'Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'An error occurred while resetting the database.');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
    }
}

// Enhanced Reset Functions
function showEnhancedResetModal() {
    // Ensure Bootstrap is loaded and modal element exists
    const modalElement = document.getElementById('enhancedResetModal');
    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap not loaded');
        return;
    }

    // Reset modal state
    updateResetPreview();
    validateResetOptions();

    // Get or create modal instance
    let modal = bootstrap.Modal.getInstance(modalElement);
    if (!modal) {
        modal = new bootstrap.Modal(modalElement);
    }

    // Show the modal
    modal.show();
}

function showCompleteResetModal() {
    // Ensure Bootstrap is loaded and modal element exists
    const modalElement = document.getElementById('enhancedResetModal');
    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap not loaded');
        return;
    }

    // Get or create modal instance
    let modal = bootstrap.Modal.getInstance(modalElement);
    if (!modal) {
        modal = new bootstrap.Modal(modalElement);
    }

    // Show the modal first
    modal.show();

    // Pre-select all options after modal is shown (with a small delay to ensure DOM is ready)
    setTimeout(() => {
        selectAllReset();
        // Ensure validation runs after selection
        setTimeout(() => {
            validateResetOptions();
        }, 50);
    }, 200);
}

function updateResetPreview() {
    const options = getResetOptions();
    const preview = document.getElementById('resetPreview');

    if (!Object.values(options).some(v => v)) {
        preview.innerHTML = '<em class="text-muted">Select reset options above to see what will be reset...</em>';
        return;
    }

    let previewHtml = '<strong>The following will be reset:</strong><ul class="mt-2 mb-0">';

    if (options.reset_database) {
        previewHtml += '<li><i class="fas fa-database text-warning me-2"></i>Database and all revision documents</li>';
    }
    if (options.reset_repositories) {
        previewHtml += '<li><i class="fas fa-code-branch text-danger me-2"></i>All repository configurations and credentials</li>';
    }
    if (options.reset_users) {
        previewHtml += '<li><i class="fas fa-users text-danger me-2"></i>All user accounts and permissions</li>';
    }
    if (options.reset_email_config) {
        previewHtml += '<li><i class="fas fa-envelope text-warning me-2"></i>Email settings and recipient lists</li>';
    }
    if (options.reset_svn_config) {
        previewHtml += '<li><i class="fas fa-server text-warning me-2"></i>SVN/Git server settings and credentials</li>';
    }
    if (options.reset_ai_config) {
        previewHtml += '<li><i class="fas fa-robot text-warning me-2"></i>AI model configuration and settings</li>';
    }

    previewHtml += '</ul>';
    preview.innerHTML = previewHtml;
}

function validateResetOptions() {
    const options = getResetOptions();
    const safetyAlert = document.getElementById('safetyValidation');
    const safetyMessage = document.getElementById('safetyMessage');
    const executeBtn = document.getElementById('executeResetBtn');

    let warnings = [];
    let isValid = true;

    // Check for potentially problematic combinations
    if (options.reset_users && options.reset_email_config && !options.reset_repositories) {
        warnings.push('Resetting users and email config but keeping repositories may leave orphaned email references');
    }

    if (options.reset_svn_config && !options.reset_repositories) {
        warnings.push('Resetting SVN config but keeping repositories may break repository access');
    }

    if (options.reset_ai_config && !options.reset_database) {
        warnings.push('Resetting AI config without database reset may cause model compatibility issues');
    }

    // Check if at least one option is selected
    if (!Object.values(options).some(v => v)) {
        warnings.push('Please select at least one component to reset');
        isValid = false;
    }

    // Show warnings or hide safety alert
    if (warnings.length > 0) {
        safetyMessage.innerHTML = warnings.join('<br>• ');
        safetyAlert.style.display = 'block';
        safetyAlert.className = isValid ? 'alert alert-warning' : 'alert alert-danger';
    } else {
        safetyAlert.style.display = 'none';
    }

    // Enable/disable execute button
    executeBtn.disabled = !isValid;
}

function getResetOptions() {
    return {
        reset_database: document.getElementById('reset_database').checked,
        reset_repositories: document.getElementById('reset_repositories').checked,
        reset_users: document.getElementById('reset_users').checked,
        reset_email_config: document.getElementById('reset_email_config').checked,
        reset_svn_config: document.getElementById('reset_svn_config').checked,
        reset_ai_config: document.getElementById('reset_ai_config').checked
    };
}

function selectAllReset() {
    const checkboxes = ['reset_database', 'reset_repositories', 'reset_users', 'reset_email_config', 'reset_svn_config', 'reset_ai_config'];
    checkboxes.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.checked = true;
        } else {
            console.warn(`Checkbox with id '${id}' not found`);
        }
    });
    updateResetPreview();
    validateResetOptions();
}

function selectNoneReset() {
    const checkboxes = ['reset_database', 'reset_repositories', 'reset_users', 'reset_email_config', 'reset_svn_config', 'reset_ai_config'];
    checkboxes.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.checked = false;
        } else {
            console.warn(`Checkbox with id '${id}' not found`);
        }
    });
    updateResetPreview();
    validateResetOptions();
}

function executeEnhancedReset() {
    const options = getResetOptions();

    // Final confirmation
    const resetItems = [];
    if (options.reset_database) resetItems.push('Database & Documents');
    if (options.reset_repositories) resetItems.push('Repositories');
    if (options.reset_users) resetItems.push('Users');
    if (options.reset_email_config) resetItems.push('Email Settings');
    if (options.reset_svn_config) resetItems.push('SVN/Git Settings');
    if (options.reset_ai_config) resetItems.push('AI Settings');

    const confirmMessage = `⚠️ FINAL CONFIRMATION\n\nThis will reset: ${resetItems.join(', ')}\n\nBackups will be created automatically.\nThis action cannot be undone.\n\nProceed with reset?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    const button = document.getElementById('executeResetBtn');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
    button.disabled = true;

    // Execute the reset
    console.log('Executing reset with options:', options);
    fetch('/api/system/reset', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(options)
    })
    .then(response => {
        console.log('Reset response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Reset response data:', data);
        if (data.success) {
            showAlert('success', `✅ System Reset Complete: ${data.message}`);

            // Close modal
            const modalElement = document.getElementById('enhancedResetModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }

            // Redirect after delay
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        } else {
            showAlert('danger', `❌ Reset Failed: ${data.message}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '❌ An error occurred during system reset.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Add event listeners for reset option changes
document.addEventListener('DOMContentLoaded', function() {
    const resetCheckboxes = ['reset_database', 'reset_repositories', 'reset_users', 'reset_email_config', 'reset_svn_config', 'reset_ai_config'];
    resetCheckboxes.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.addEventListener('change', () => {
                updateResetPreview();
                validateResetOptions();
            });
        }
    });
});

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-database-reset');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-database-reset mt-3`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert after the page header
    const header = document.querySelector('.page-header');
    header.parentNode.insertBefore(alertDiv, header.nextSibling);

    // Auto-dismiss after 8 seconds for success, 10 seconds for errors
    const dismissTime = type === 'success' ? 8000 : 10000;
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, dismissTime);
}
</script>
{% endblock %}
