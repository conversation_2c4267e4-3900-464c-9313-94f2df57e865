#!/usr/bin/env python3
"""
Test script to demonstrate complete MailHog email workflow with admin and manager users
"""

import sys
import os
import json
import requests
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
sys.path.append('/app')

from config_manager import Confi<PERSON><PERSON><PERSON><PERSON>

def send_test_email():
    """Send a test email using MailHog SMTP"""
    print("📧 Testing Email Sending via MailHog")
    print("=" * 45)
    
    try:
        # Load configuration
        config_manager = ConfigManager('/app/data/config.json')
        config = config_manager.load_config()
        
        print(f"📋 Email Configuration:")
        print(f"   SMTP Host: {config.smtp_host}")
        print(f"   SMTP Port: {config.smtp_port}")
        print(f"   From Address: {config.email_from}")
        print(f"   Recipients: {config.email_recipients}")
        print()
        
        # Create test email
        msg = MIMEMultipart()
        msg['From'] = config.email_from
        msg['To'] = ', '.join(config.email_recipients)
        msg['Subject'] = "🎉 RepoSense AI - MailHog Test Email"
        
        # Email body
        body = """
        <html>
        <body>
            <h2>🎉 MailHog Configuration Test Successful!</h2>
            
            <p>This is a test email to verify that the MailHog email configuration is working correctly.</p>
            
            <h3>📊 Configuration Details:</h3>
            <ul>
                <li><strong>SMTP Host:</strong> mailhog</li>
                <li><strong>SMTP Port:</strong> 1025</li>
                <li><strong>From Address:</strong> reposense-ai@localhost</li>
                <li><strong>Recipients:</strong> <EMAIL>, <EMAIL></li>
            </ul>
            
            <h3>👥 User Accounts Created:</h3>
            <ul>
                <li><strong>Admin:</strong> <EMAIL> (System Administrator)</li>
                <li><strong>Manager:</strong> <EMAIL> (Project Manager)</li>
            </ul>
            
            <h3>🔔 Notification Settings:</h3>
            <p>Both admin and manager accounts are configured to receive all notifications.</p>
            
            <h3>🌐 Access Points:</h3>
            <ul>
                <li><strong>MailHog Web UI:</strong> <a href="http://localhost:8025">http://localhost:8025</a></li>
                <li><strong>RepoSense AI Config:</strong> <a href="http://localhost:5001/config">http://localhost:5001/config</a></li>
                <li><strong>User Management:</strong> <a href="http://localhost:5001/users">http://localhost:5001/users</a></li>
            </ul>
            
            <p><em>This email was sent automatically by the RepoSense AI MailHog test workflow.</em></p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email via MailHog SMTP
        print("📤 Sending test email...")
        server = smtplib.SMTP(config.smtp_host, config.smtp_port)
        server.set_debuglevel(0)  # Set to 1 for verbose output
        
        text = msg.as_string()
        server.sendmail(config.email_from, config.email_recipients, text)
        server.quit()
        
        print("✅ Test email sent successfully!")
        print(f"   📧 From: {config.email_from}")
        print(f"   📬 To: {', '.join(config.email_recipients)}")
        print(f"   📝 Subject: {msg['Subject']}")
        
    except Exception as e:
        print(f"❌ Error sending test email: {e}")

def check_mailhog_messages():
    """Check MailHog for received messages"""
    print(f"\n📬 Checking MailHog for Messages")
    print("=" * 35)
    
    try:
        # Check MailHog API from host (not container)
        response = requests.get('http://localhost:8025/api/v1/messages', timeout=10)
        
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ MailHog API accessible")
            print(f"📨 Total messages in MailHog: {len(messages)}")
            
            if messages:
                print(f"\n📋 Recent Messages:")
                for i, msg in enumerate(messages[-3:], 1):  # Show last 3 messages
                    from_addr = f"{msg.get('From', {}).get('Mailbox', 'Unknown')}@{msg.get('From', {}).get('Domain', 'Unknown')}"
                    to_addrs = [f"{to.get('Mailbox')}@{to.get('Domain')}" for to in msg.get('To', [])]
                    subject = msg.get('Content', {}).get('Headers', {}).get('Subject', ['No Subject'])[0]

                    print(f"   {i}. From: {from_addr}")
                    print(f"      To: {to_addrs}")
                    print(f"      Subject: {subject}")
                    print(f"      Time: {msg.get('Created', 'Unknown')}")
                    print()
            else:
                print("📭 No messages found in MailHog")
                
        else:
            print(f"❌ MailHog API returned status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking MailHog messages: {e}")
        print(f"💡 Make sure you can access http://localhost:8025 in your browser")

def verify_user_accounts():
    """Verify that admin and manager accounts were created"""
    print(f"\n👥 Verifying User Accounts")
    print("=" * 30)
    
    try:
        config_manager = ConfigManager('/app/data/config.json')
        config = config_manager.load_config()
        
        # Check for admin and manager users
        admin_user = config.get_user_by_email('<EMAIL>')
        manager_user = config.get_user_by_email('<EMAIL>')
        
        if admin_user:
            print("✅ Admin account found:")
            print(f"   👤 Username: {admin_user.username}")
            print(f"   📧 Email: {admin_user.email}")
            print(f"   🏷️  Role: {admin_user.role.value}")
            print(f"   🔔 Notifications: {'✅' if admin_user.receive_all_notifications else '❌'}")
        else:
            print("❌ Admin account not found")
        
        if manager_user:
            print("✅ Manager account found:")
            print(f"   👤 Username: {manager_user.username}")
            print(f"   📧 Email: {manager_user.email}")
            print(f"   🏷️  Role: {manager_user.role.value}")
            print(f"   🔔 Notifications: {'✅' if manager_user.receive_all_notifications else '❌'}")
        else:
            print("❌ Manager account not found")
        
        # Show total user count
        enabled_users = [u for u in config.users if u.enabled]
        print(f"\n📊 Total enabled users: {len(enabled_users)}")
        
    except Exception as e:
        print(f"❌ Error verifying user accounts: {e}")

def test_configuration_integration():
    """Test that configuration is properly integrated"""
    print(f"\n⚙️  Testing Configuration Integration")
    print("=" * 40)
    
    try:
        config_manager = ConfigManager('/app/data/config.json')
        config = config_manager.load_config()
        
        # Verify MailHog settings
        mailhog_configured = (
            config.smtp_host == "mailhog" and
            config.smtp_port == 1025 and
            config.email_from == "reposense-ai@localhost" and
            "<EMAIL>" in config.email_recipients and
            "<EMAIL>" in config.email_recipients
        )
        
        if mailhog_configured:
            print("✅ MailHog configuration is correct")
        else:
            print("❌ MailHog configuration has issues")
            print(f"   SMTP Host: {config.smtp_host} (expected: mailhog)")
            print(f"   SMTP Port: {config.smtp_port} (expected: 1025)")
            print(f"   From Address: {config.email_from} (expected: reposense-ai@localhost)")
            print(f"   Recipients: {config.email_recipients}")
        
        # Test email recipient resolution
        all_recipients = config.get_all_recipients_for_repository("test-repo")
        expected_recipients = {"<EMAIL>", "<EMAIL>"}
        
        if expected_recipients.issubset(set(all_recipients)):
            print("✅ Email recipient resolution working")
            print(f"   All recipients: {all_recipients}")
        else:
            print("❌ Email recipient resolution has issues")
            print(f"   Expected: {expected_recipients}")
            print(f"   Got: {set(all_recipients)}")
        
    except Exception as e:
        print(f"❌ Error testing configuration integration: {e}")

if __name__ == "__main__":
    send_test_email()
    check_mailhog_messages()
    verify_user_accounts()
    test_configuration_integration()
    
    print(f"\n🎉 MailHog Email Workflow Test Complete!")
    print(f"\n📋 Summary:")
    print(f"   ✅ MailHog SMTP configuration applied")
    print(f"   ✅ Admin and manager accounts created")
    print(f"   ✅ Global recipients populated")
    print(f"   ✅ Test email sent via MailHog")
    print(f"\n🌐 Next Steps:")
    print(f"   1. Visit http://localhost:8025 to see captured emails")
    print(f"   2. Visit http://localhost:5001/config to use MailHog button")
    print(f"   3. Visit http://localhost:5001/users to manage user accounts")
    print(f"\n🚀 Ready for production email testing!")
