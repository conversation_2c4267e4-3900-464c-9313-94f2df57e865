#!/bin/bash
# RepoSense AI Docker Image Build & Deploy Script
# Builds a production-ready Docker image for remote server deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="reposense-ai"
IMAGE_TAG="latest"
REGISTRY_PREFIX=""  # Set to your registry if using one (e.g., "your-registry.com/")
REMOTE_SERVER=""    # Set your remote server IP/hostname
REMOTE_USER="fvaneijk"
REMOTE_PATH="/home/<USER>/home-ai-system"

echo -e "${GREEN}🐳 RepoSense AI Docker Build & Deploy Script${NC}"
echo "=============================================="

# Function to log messages
log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Parse command line arguments
PUSH_TO_REGISTRY=false
DEPLOY_TO_REMOTE=false
SAVE_TO_FILE=false
OUTPUT_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        --deploy)
            DEPLOY_TO_REMOTE=true
            REMOTE_SERVER="$2"
            shift 2
            ;;
        --save)
            SAVE_TO_FILE=true
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --push                    Push image to registry"
            echo "  --deploy SERVER_IP        Deploy to remote server"
            echo "  --save FILENAME           Save image to tar file"
            echo "  --tag TAG                 Set image tag (default: latest)"
            echo "  --help                    Show this help"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Build image locally"
            echo "  $0 --save reposense-ai.tar          # Build and save to file"
            echo "  $0 --deploy *************           # Build and deploy to server"
            echo "  $0 --tag v1.0.0 --push              # Build, tag, and push to registry"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if we're in the right directory
if [ ! -d "reposense_ai" ]; then
    log_error "reposense_ai directory not found. Please run this script from the parent directory."
    exit 1
fi

if [ ! -f "reposense_ai/Dockerfile" ]; then
    log_error "Dockerfile not found in reposense_ai/. Please check the directory structure."
    exit 1
fi

log_step "1. Preparing build context..."

# Validate build context
log_info "Validating build context..."
if [ ! -f "reposense_ai/requirements.txt" ]; then
    log_error "requirements.txt not found"
    exit 1
fi

if [ ! -f "reposense_ai/entrypoint.sh" ]; then
    log_error "entrypoint.sh not found"
    exit 1
fi

if [ ! -f "reposense_ai/reposense_ai_app.py" ]; then
    log_error "reposense_ai_app.py not found"
    exit 1
fi

log_info "Build context validated"

log_step "2. Building Docker image..."

# Build the image
FULL_IMAGE_NAME="${REGISTRY_PREFIX}${IMAGE_NAME}:${IMAGE_TAG}"

log_info "Building image: $FULL_IMAGE_NAME"
log_info "Build context: ./reposense_ai"

if docker build -t "$FULL_IMAGE_NAME" ./reposense_ai; then
    log_info "Docker image built successfully!"
else
    log_error "Docker build failed!"
    exit 1
fi

# Show image info
IMAGE_SIZE=$(docker images "$FULL_IMAGE_NAME" --format "{{.Size}}")
log_info "Image size: $IMAGE_SIZE"

log_step "3. Testing built image..."

# Test the image
log_info "Running quick test of the built image..."
if docker run --rm -d --name reposense-ai-test -p 5001:5000 "$FULL_IMAGE_NAME" > /dev/null; then
    sleep 5
    if curl -f http://localhost:5001/health > /dev/null 2>&1; then
        log_info "Image test passed - health check successful"
    else
        log_warn "Image test warning - health check failed (may be normal if Ollama not available)"
    fi
    docker stop reposense-ai-test > /dev/null 2>&1 || true
else
    log_warn "Could not test image (port 5001 may be in use)"
fi

# Handle different deployment options
if [ "$SAVE_TO_FILE" = true ]; then
    log_step "4. Saving image to file..."
    
    if [ -z "$OUTPUT_FILE" ]; then
        OUTPUT_FILE="reposense-ai-${IMAGE_TAG}.tar"
    fi
    
    log_info "Saving image to: $OUTPUT_FILE"
    if docker save "$FULL_IMAGE_NAME" -o "$OUTPUT_FILE"; then
        FILE_SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
        log_info "Image saved successfully! Size: $FILE_SIZE"
        log_info "Transfer to remote server with: scp $OUTPUT_FILE user@server:/path/"
        log_info "Load on remote server with: docker load -i $OUTPUT_FILE"
    else
        log_error "Failed to save image to file"
        exit 1
    fi
fi

if [ "$PUSH_TO_REGISTRY" = true ]; then
    log_step "4. Pushing to registry..."
    
    if [ -z "$REGISTRY_PREFIX" ]; then
        log_error "REGISTRY_PREFIX not set. Please configure your registry in the script."
        exit 1
    fi
    
    log_info "Pushing image: $FULL_IMAGE_NAME"
    if docker push "$FULL_IMAGE_NAME"; then
        log_info "Image pushed successfully!"
    else
        log_error "Failed to push image"
        exit 1
    fi
fi

if [ "$DEPLOY_TO_REMOTE" = true ]; then
    log_step "4. Deploying to remote server..."
    
    if [ -z "$REMOTE_SERVER" ]; then
        log_error "Remote server not specified"
        exit 1
    fi
    
    # Save image to temporary file
    TEMP_FILE="reposense-ai-deploy-${IMAGE_TAG}.tar"
    log_info "Creating deployment package..."
    docker save "$FULL_IMAGE_NAME" -o "$TEMP_FILE"
    
    # Transfer and deploy
    log_info "Transferring to $REMOTE_SERVER..."
    if scp "$TEMP_FILE" "${REMOTE_USER}@${REMOTE_SERVER}:${REMOTE_PATH}/"; then
        log_info "Transfer completed"
        
        # Deploy on remote server
        log_info "Deploying on remote server..."
        ssh "${REMOTE_USER}@${REMOTE_SERVER}" << EOF
cd ${REMOTE_PATH}
echo "Loading Docker image..."
docker load -i ${TEMP_FILE}
rm ${TEMP_FILE}

echo "Updating docker-compose.yml to use pre-built image..."
# Update docker-compose.yml to use the image instead of building
sed -i 's/build:/# build:/' docker-compose.yml || true
sed -i 's/context: .*/# context: ./' docker-compose.yml || true
sed -i 's/dockerfile: .*/# dockerfile: ./' docker-compose.yml || true
sed -i '/reposense-ai:/a\\    image: ${FULL_IMAGE_NAME}' docker-compose.yml || true

echo "Starting RepoSense AI..."
docker-compose up -d reposense-ai

echo "Checking status..."
docker-compose ps reposense-ai
EOF
        
        if [ $? -eq 0 ]; then
            log_info "Deployment completed successfully!"
            log_info "Check status with: ssh ${REMOTE_USER}@${REMOTE_SERVER} 'cd ${REMOTE_PATH} && docker-compose logs reposense-ai'"
        else
            log_error "Deployment failed"
            exit 1
        fi
    else
        log_error "Failed to transfer image to remote server"
        exit 1
    fi
    
    # Clean up local temp file
    rm -f "$TEMP_FILE"
fi

echo
log_info "🎉 Build process completed successfully!"
echo
echo -e "${BLUE}Image Details:${NC}"
echo "  Name: $FULL_IMAGE_NAME"
echo "  Size: $IMAGE_SIZE"
echo
echo -e "${BLUE}Next Steps:${NC}"
if [ "$DEPLOY_TO_REMOTE" = false ] && [ "$SAVE_TO_FILE" = false ] && [ "$PUSH_TO_REGISTRY" = false ]; then
    echo "  1. Save to file: $0 --save reposense-ai.tar"
    echo "  2. Deploy to server: $0 --deploy YOUR_SERVER_IP"
    echo "  3. Push to registry: $0 --push"
    echo "  4. Run locally: docker run -p 5000:5000 $FULL_IMAGE_NAME"
fi

if [ "$DEPLOY_TO_REMOTE" = true ]; then
    echo "  • Access web interface: http://${REMOTE_SERVER}:5000"
    echo "  • Check logs: ssh ${REMOTE_USER}@${REMOTE_SERVER} 'cd ${REMOTE_PATH} && docker-compose logs reposense-ai'"
fi
