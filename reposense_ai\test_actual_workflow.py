#!/usr/bin/env python3
"""
Test the actual workflow that the application uses to verify specialized models work
"""

import logging
from monitor_service import MonitorService
from document_service import DocumentService
from ollama_client import OllamaClient

def test_monitor_service_workflow():
    """Test the actual MonitorService workflow"""
    print("=== Testing MonitorService Workflow ===")
    
    # Initialize MonitorService (this is how the app actually starts)
    monitor_service = MonitorService("data/config.json")
    
    print("🎯 MonitorService config:")
    print(f"  Default model: {monitor_service.config.ollama_model}")
    print(f"  Documentation model: {monitor_service.config.ollama_model_documentation}")
    print(f"  Code review model: {monitor_service.config.ollama_model_code_review}")
    print(f"  Risk assessment model: {monitor_service.config.ollama_model_risk_assessment}")
    
    print("\n🤖 OllamaClient config:")
    print(f"  Default model: {monitor_service.ollama_client.config.ollama_model}")
    print(f"  Documentation model: {monitor_service.ollama_client.config.ollama_model_documentation}")
    print(f"  Code review model: {monitor_service.ollama_client.config.ollama_model_code_review}")
    print(f"  Risk assessment model: {monitor_service.ollama_client.config.ollama_model_risk_assessment}")
    
    return monitor_service

def test_document_service_workflow(monitor_service):
    """Test DocumentService workflow"""
    print("\n=== Testing DocumentService Workflow ===")
    
    # Create DocumentService the same way WebInterface does
    document_service = DocumentService(
        monitor_service.config.output_dir,
        ollama_client=monitor_service.ollama_client,
        config_manager=monitor_service.config_manager
    )
    
    print("📄 DocumentService setup:")
    print(f"  Has OllamaClient: {document_service.ollama_client is not None}")
    print(f"  Has ConfigManager: {document_service.config_manager is not None}")
    
    if document_service.ollama_client:
        print(f"  OllamaClient config - Documentation model: {document_service.ollama_client.config.ollama_model_documentation}")
        print(f"  OllamaClient config - Code review model: {document_service.ollama_client.config.ollama_model_code_review}")
        print(f"  OllamaClient config - Risk assessment model: {document_service.ollama_client.config.ollama_model_risk_assessment}")
    
    # Test the config loading logic used in _extract_metadata_with_llm
    if document_service.ollama_client and hasattr(document_service.ollama_client, 'config'):
        config = document_service.ollama_client.config
    elif document_service.config_manager:
        config = document_service.config_manager.load_config()
    else:
        config = None
    
    print(f"\n🔍 Config used in _extract_metadata_with_llm:")
    if config:
        print(f"  Documentation model: {getattr(config, 'ollama_model_documentation', None)}")
        print(f"  Code review model: {getattr(config, 'ollama_model_code_review', None)}")
        print(f"  Risk assessment model: {getattr(config, 'ollama_model_risk_assessment', None)}")
        
        # Test model selection logic
        risk_model = getattr(config, 'ollama_model_risk_assessment', None)
        code_review_model = getattr(config, 'ollama_model_code_review', None)
        specialized_model = risk_model or code_review_model
        
        print(f"\n🎯 Model selection result:")
        print(f"  Selected specialized model: {specialized_model}")
        
        return specialized_model is not None
    else:
        print("  ❌ No config available!")
        return False

if __name__ == "__main__":
    print("🧪 Testing Actual Application Workflow")
    print("=" * 60)
    
    # Set up logging to see debug messages
    logging.basicConfig(level=logging.DEBUG)
    
    try:
        # Test MonitorService workflow
        monitor_service = test_monitor_service_workflow()
        
        # Test DocumentService workflow
        models_working = test_document_service_workflow(monitor_service)
        
        print("\n" + "=" * 60)
        if models_working:
            print("✅ SUCCESS: Specialized models are properly configured and accessible!")
        else:
            print("❌ ISSUE: Specialized models are not working in the actual workflow!")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
