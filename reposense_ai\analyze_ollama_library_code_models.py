#!/usr/bin/env python3
"""
Re-evaluate qwen3-coder:latest based on the actual Ollama library
"""

import sys
import os
import json
import requests
sys.path.append('/app')

def analyze_ollama_library_code_models():
    """Analyze code models from the actual Ollama library"""
    print("🌐 Analyzing Code Models from Ollama Library (2024/2025)")
    print("=" * 60)
    
    # Based on the actual Ollama library as of 2024/2025
    ollama_code_models = {
        'qwen2.5-coder': {
            'sizes': ['0.5b', '1.5b', '3b', '7b', '14b', '32b'],
            'pulls': '6.3M',
            'updated': '2 months ago',
            'description': 'The latest series of Code-Specific Qwen models, with significant improvements in code generation, code reasoning, and code fixing',
            'ranking': 9.5,
            'status': 'TOP TIER - Latest specialized code model'
        },
        'codellama': {
            'sizes': ['7b', '13b', '34b', '70b'],
            'pulls': '2.7M',
            'updated': '1 year ago',
            'description': 'A large language model that can use text prompts to generate and discuss code',
            'ranking': 9.0,
            'status': 'GOLD STANDARD - Meta\'s proven code model'
        },
        'deepseek-coder': {
            'sizes': ['1.3b', '6.7b', '33b'],
            'pulls': 'Not in top list but available',
            'updated': 'Available in library',
            'description': 'DeepSeek Coder is a capable coding model trained on two trillion code and natural language tokens',
            'ranking': 9.0,
            'status': 'EXCELLENT - Top benchmark performer'
        },
        'magicoder': {
            'sizes': ['7b'],
            'pulls': '41K',
            'updated': '1 year ago',
            'description': 'Family of 7B parameter models trained on 75K synthetic instruction data using OSS-Instruct',
            'ranking': 7.5,
            'status': 'GOOD - Specialized training approach'
        },
        'codebooga': {
            'sizes': ['34b'],
            'pulls': '39.8K',
            'updated': '1 year ago',
            'description': 'A high-performing code instruct model created by merging two existing code models',
            'ranking': 8.0,
            'status': 'GOOD - Large merged model'
        },
        'codeup': {
            'sizes': ['13b'],
            'pulls': '48.5K',
            'updated': '1 year ago',
            'description': 'Great code generation model based on Llama2',
            'ranking': 7.0,
            'status': 'DECENT - Older generation'
        }
    }
    
    print("🏆 TOP CODE MODELS IN OLLAMA LIBRARY:")
    for model, details in sorted(ollama_code_models.items(), key=lambda x: x[1]['ranking'], reverse=True):
        print(f"\n📦 {model} (Score: {details['ranking']}/10)")
        print(f"   📊 Status: {details['status']}")
        print(f"   📏 Sizes: {', '.join(details['sizes'])}")
        print(f"   📈 Pulls: {details['pulls']}")
        print(f"   🔄 Updated: {details['updated']}")
        print(f"   📝 Description: {details['description']}")
    
    return ollama_code_models

def compare_your_models_with_ollama_library():
    """Compare your available models with the Ollama library standards"""
    print(f"\n🔍 Your Models vs Ollama Library Standards")
    print("=" * 50)
    
    try:
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        ollama_host = config.get('ollama_host', 'http://localhost:11434')
        response = requests.get(f"{ollama_host}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            your_models = [m['name'] for m in data.get('models', [])]
            
            print("📊 COMPARISON ANALYSIS:")
            
            # Check for top-tier models
            top_tier_available = []
            top_tier_missing = []
            
            top_models = {
                'qwen2.5-coder:32b': 'Best large code model',
                'qwen2.5-coder:14b': 'Best medium code model', 
                'qwen2.5-coder:7b': 'Best small code model',
                'codellama:34b': 'Meta\'s large code model',
                'codellama:13b': 'Meta\'s medium code model',
                'codellama:7b': 'Meta\'s small code model',
                'deepseek-coder:33b': 'Top benchmark performer',
                'deepseek-coder:6.7b': 'Efficient code model'
            }
            
            for model, description in top_models.items():
                if model in your_models:
                    top_tier_available.append(f"✅ {model} - {description}")
                else:
                    top_tier_missing.append(f"❌ {model} - {description}")
            
            print(f"\n🎯 TOP-TIER CODE MODELS:")
            print(f"✅ AVAILABLE ON YOUR SERVER:")
            if top_tier_available:
                for model in top_tier_available:
                    print(f"   {model}")
            else:
                print("   ❌ None of the top-tier code models are available")
            
            print(f"\n❌ MISSING FROM YOUR SERVER:")
            for model in top_tier_missing[:5]:  # Show top 5 missing
                print(f"   {model}")
            
            # Analyze what you actually have
            your_code_models = []
            for model in your_models:
                if any(keyword in model.lower() for keyword in ['code', 'coder']):
                    your_code_models.append(model)
            
            print(f"\n📦 YOUR AVAILABLE CODE MODELS:")
            if your_code_models:
                for model in your_code_models:
                    print(f"   📦 {model}")
            else:
                print("   ❌ No dedicated code models found")
            
            return your_models, top_tier_available, top_tier_missing
            
        else:
            print("❌ Cannot connect to your Ollama server")
            return [], [], []
            
    except Exception as e:
        print(f"❌ Error analyzing your models: {e}")
        return [], [], []

def reevaluate_qwen3_coder():
    """Re-evaluate qwen3-coder:latest based on Ollama library standards"""
    print(f"\n🔄 Re-evaluating qwen3-coder:latest")
    print("=" * 40)
    
    print("🔍 REALITY CHECK:")
    print("   ❓ qwen3-coder:latest is NOT in the official Ollama library!")
    print("   📊 The official library shows:")
    print("      • qwen2.5-coder (latest official code model)")
    print("      • codellama (Meta's proven model)")
    print("      • deepseek-coder (benchmark leader)")
    print()
    
    print("🤔 WHAT IS qwen3-coder:latest?")
    analysis = [
        "🔍 Likely a custom/unofficial model on your server",
        "📦 Not part of the standard Ollama model library",
        "⚠️  May be experimental or community-built",
        "❓ Quality and performance not officially validated",
        "🧪 Could be good, but unproven compared to official models"
    ]
    
    for point in analysis:
        print(f"   {point}")
    
    print(f"\n🏆 OFFICIAL ALTERNATIVES (from Ollama library):")
    alternatives = {
        'qwen2.5-coder:32b': {
            'status': 'BEST CHOICE - Latest official code model',
            'size': '~32B parameters',
            'benefits': 'Latest improvements in code generation, reasoning, and fixing'
        },
        'qwen2.5-coder:14b': {
            'status': 'EXCELLENT CHOICE - Balanced performance',
            'size': '~14B parameters', 
            'benefits': 'Good balance of capability and resource usage'
        },
        'codellama:34b': {
            'status': 'PROVEN CHOICE - Meta\'s gold standard',
            'size': '~34B parameters',
            'benefits': 'Extensively tested, industry standard'
        },
        'deepseek-coder:33b': {
            'status': 'TOP PERFORMER - Benchmark leader',
            'size': '~33B parameters',
            'benefits': 'Top performance in coding benchmarks'
        }
    }
    
    for model, details in alternatives.items():
        print(f"\n📦 {model}:")
        print(f"   🎯 Status: {details['status']}")
        print(f"   📏 Size: {details['size']}")
        print(f"   ✅ Benefits: {details['benefits']}")

def provide_updated_recommendation():
    """Provide updated recommendation based on Ollama library analysis"""
    print(f"\n🎯 UPDATED RECOMMENDATION")
    print("=" * 30)
    
    print("🔄 REVISED ANALYSIS:")
    print("   ❌ qwen3-coder:latest is NOT an official Ollama model")
    print("   ✅ qwen2.5-coder is the LATEST official code model series")
    print("   🏆 Multiple proven alternatives available")
    print()
    
    print("🏆 NEW RECOMMENDATION HIERARCHY:")
    
    recommendations = {
        '1st Choice': {
            'model': 'qwen2.5-coder:32b',
            'reason': 'Latest official code model with best capabilities',
            'install': 'ollama pull qwen2.5-coder:32b'
        },
        '2nd Choice': {
            'model': 'qwen2.5-coder:14b', 
            'reason': 'Excellent balance of performance and efficiency',
            'install': 'ollama pull qwen2.5-coder:14b'
        },
        '3rd Choice': {
            'model': 'codellama:34b',
            'reason': 'Meta\'s proven industry standard',
            'install': 'ollama pull codellama:34b'
        },
        '4th Choice': {
            'model': 'deepseek-coder:33b',
            'reason': 'Top benchmark performance',
            'install': 'ollama pull deepseek-coder:33b'
        },
        'Current Choice': {
            'model': 'qwen3-coder:latest',
            'reason': 'Unofficial model - quality unknown but may work',
            'install': 'Already installed (custom/unofficial)'
        }
    }
    
    for rank, details in recommendations.items():
        print(f"\n🏆 {rank}: {details['model']}")
        print(f"   💡 Reason: {details['reason']}")
        print(f"   📥 Install: {details['install']}")
    
    print(f"\n🎯 FINAL VERDICT:")
    verdict = [
        "❓ qwen3-coder:latest may work but is not officially supported",
        "🏆 qwen2.5-coder:32b is the best official choice",
        "⚡ qwen2.5-coder:14b is the best balanced choice",
        "🛡️ codellama:34b is the most proven choice",
        "📊 All official alternatives are better validated than qwen3-coder"
    ]
    
    for point in verdict:
        print(f"   {point}")

def show_installation_commands():
    """Show commands to install better code models"""
    print(f"\n📥 Installation Commands for Better Models")
    print("=" * 45)
    
    print("🚀 RECOMMENDED INSTALLATIONS:")
    
    commands = [
        {
            'model': 'qwen2.5-coder:32b',
            'command': 'ollama pull qwen2.5-coder:32b',
            'note': 'Best overall - latest official code model'
        },
        {
            'model': 'qwen2.5-coder:14b',
            'command': 'ollama pull qwen2.5-coder:14b', 
            'note': 'Best balanced - good performance/size ratio'
        },
        {
            'model': 'codellama:34b',
            'command': 'ollama pull codellama:34b',
            'note': 'Most proven - Meta\'s industry standard'
        },
        {
            'model': 'qwen2.5-coder:7b',
            'command': 'ollama pull qwen2.5-coder:7b',
            'note': 'Most efficient - smaller but still capable'
        }
    ]
    
    for cmd in commands:
        print(f"\n📦 {cmd['model']}:")
        print(f"   💻 Command: {cmd['command']}")
        print(f"   💡 Note: {cmd['note']}")
    
    print(f"\n⚡ QUICK START:")
    print("   1. Choose a model from above")
    print("   2. Run the ollama pull command")
    print("   3. Update RepoSense config to use the new model")
    print("   4. Enjoy much better code analysis!")

def final_assessment():
    """Final assessment of qwen3-coder vs official alternatives"""
    print(f"\n🎯 FINAL ASSESSMENT: Should You Use qwen3-coder:latest?")
    print("=" * 60)
    
    print("🔍 HONEST EVALUATION:")
    
    assessment = {
        'qwen3-coder:latest (Your Current Choice)': {
            'pros': [
                'Already installed and working',
                'May have good performance (unverified)',
                'Large model size suggests capability'
            ],
            'cons': [
                'Not in official Ollama library',
                'Quality and reliability unverified',
                'No official support or documentation',
                'May have unknown issues or limitations'
            ],
            'verdict': 'RISKY - Works but unproven'
        },
        'qwen2.5-coder:32b (Recommended Alternative)': {
            'pros': [
                'Latest official code model',
                'Proven quality and reliability',
                'Official support and documentation',
                'Continuous updates and improvements'
            ],
            'cons': [
                'Need to download (~32GB)',
                'Slightly larger than your current model'
            ],
            'verdict': 'BEST CHOICE - Official and proven'
        }
    }
    
    for model, details in assessment.items():
        print(f"\n📊 {model}:")
        print(f"   ✅ Pros:")
        for pro in details['pros']:
            print(f"      • {pro}")
        print(f"   ❌ Cons:")
        for con in details['cons']:
            print(f"      • {con}")
        print(f"   🎯 Verdict: {details['verdict']}")
    
    print(f"\n💡 RECOMMENDATION:")
    print("   🔄 KEEP qwen3-coder:latest for now (it's working)")
    print("   📥 INSTALL qwen2.5-coder:32b as backup/alternative")
    print("   🧪 TEST both models side-by-side")
    print("   🏆 SWITCH to qwen2.5-coder if it performs better")
    print("   🛡️ USE official models for production reliability")

if __name__ == "__main__":
    ollama_models = analyze_ollama_library_code_models()
    your_models, available, missing = compare_your_models_with_ollama_library()
    reevaluate_qwen3_coder()
    provide_updated_recommendation()
    show_installation_commands()
    final_assessment()
    
    print(f"\n🎯 BOTTOM LINE:")
    print("=" * 15)
    print("✅ qwen3-coder:latest may be good but is UNOFFICIAL")
    print("🏆 qwen2.5-coder:32b is the BEST OFFICIAL choice")
    print("⚡ qwen2.5-coder:14b is the BEST BALANCED choice")
    print("🛡️ Official models are more reliable for production")
    print("🧪 Consider testing official alternatives")
    print()
    print("🚀 Either way, ANY specialized code model is 10x better than smollm2:latest!")
