#!/usr/bin/env python3
"""
Diff Service - Generate SCM diff content on-demand
"""

import logging
import subprocess
from typing import Optional
from document_database import DocumentRecord

class DiffService:
    """Service for generating SCM diff content on-demand"""

    def __init__(self, config_manager=None):
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
    
    def get_diff_for_document(self, document: DocumentRecord, format_type: str = 'unified') -> Optional[str]:
        """
        Generate diff content for a document based on its repository metadata

        Args:
            document: DocumentRecord containing repository metadata
            format_type: 'unified' or 'side-by-side'

        Returns:
            Diff content as string, or None if unable to generate
        """
        # Get repository metadata (from document or infer from configuration)
        repo_url = document.repository_url
        repo_type = document.repository_type

        if not repo_url or not repo_type:
            # Try to infer from configuration
            if self.config_manager is not None:
                try:
                    config = self.config_manager.load_config()
                    for repo in config.repositories:
                        if repo.id == document.repository_id:
                            repo_url = repo.url
                            repo_type = getattr(repo, 'type', 'svn')
                            self.logger.info(f"Inferred repository metadata for {document.id}: {repo_type} - {repo_url}")
                            break

                    if not repo_url or not repo_type:
                        self.logger.warning(f"Document {document.id} missing repository metadata and cannot infer from config")
                        return None
                except Exception as e:
                    self.logger.error(f"Error loading configuration: {e}")
                    return None
            else:
                self.logger.warning(f"Document {document.id} missing repository metadata and no config manager available")
                return None

        try:
            if repo_type.lower() == 'svn':
                # Create a temporary document with the inferred metadata for SVN operations
                temp_document = document
                if not document.repository_url:
                    # We need to pass the inferred URL to the SVN diff method
                    # For now, we'll modify the document temporarily
                    temp_document.repository_url = repo_url
                    temp_document.repository_type = repo_type
                unified_diff = self._get_svn_diff(temp_document)
            elif repo_type.lower() == 'git':
                # Create a temporary document with the inferred metadata for Git operations
                temp_document = document
                if not document.repository_url:
                    temp_document.repository_url = repo_url
                    temp_document.repository_type = repo_type
                unified_diff = self._get_git_diff(temp_document)
            else:
                self.logger.error(f"Unsupported repository type: {repo_type}")
                return None

            if not unified_diff:
                return None

            # Convert to requested format
            if format_type == 'side-by-side':
                return self._convert_to_side_by_side(unified_diff)
            else:
                return unified_diff

        except Exception as e:
            self.logger.error(f"Error generating diff for document {document.id}: {e}")
            return None
    


    def _get_svn_diff(self, document: DocumentRecord) -> Optional[str]:
        """Generate SVN diff for a specific revision using repository credentials"""
        try:
            # Check if repository URL is available
            if not document.repository_url:
                return "Error: Repository URL not available in document metadata."

            # Get repository configuration with credentials
            repo_config = self._get_repository_config(document.repository_url)
            if not repo_config:
                return "Error: Repository configuration not found. Cannot access SVN credentials."

            # For SVN, we need to get the diff between revision-1 and revision
            prev_revision = document.revision - 1
            current_revision = document.revision

            # Build SVN command with credentials
            if prev_revision < 1:
                # For first revision, show the commit as a diff
                cmd = [
                    'svn', 'diff',
                    '--change', str(current_revision),
                    '--non-interactive',
                    '--trust-server-cert'
                ]
                if repo_config.get('username'):
                    cmd.extend(['--username', repo_config['username']])
                if repo_config.get('password'):
                    cmd.extend(['--password', repo_config['password']])
                cmd.append(document.repository_url)
            else:
                # Normal diff between revisions using -r flag
                cmd = [
                    'svn', 'diff',
                    '-r', f'{prev_revision}:{current_revision}',
                    '--non-interactive',
                    '--trust-server-cert'
                ]
                if repo_config.get('username'):
                    cmd.extend(['--username', repo_config['username']])
                if repo_config.get('password'):
                    cmd.extend(['--password', repo_config['password']])
                cmd.append(document.repository_url)

            # Filter out None values and ensure all elements are strings
            cmd = [str(c) for c in cmd if c is not None]

            self.logger.debug(f"Running SVN diff command with credentials: {' '.join([c if c != repo_config.get('password', '') else '***' for c in cmd])}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,  # Get bytes to handle encoding ourselves
                timeout=30  # 30 second timeout
            )

            if result.returncode == 0:
                # Decode output with proper encoding handling
                diff_content = self._decode_svn_output(result.stdout)
                if diff_content.strip():
                    return diff_content
                else:
                    return "No differences found or binary files only"
            else:
                # Decode error message with proper encoding handling
                error_msg = self._decode_svn_output(result.stderr) if result.stderr else "Unknown SVN error"
                self.logger.error(f"SVN diff with credentials failed: {error_msg}")
                return f"Error generating SVN diff: {error_msg}"

        except subprocess.TimeoutExpired:
            self.logger.error(f"SVN diff timed out for document {document.id}")
            return "Error: SVN diff operation timed out"
        except Exception as e:
            self.logger.error(f"Error running SVN diff with credentials: {e}")
            return f"Error running SVN diff: {str(e)}"

    def _decode_svn_output(self, output_bytes: bytes) -> str:
        """
        Decode SVN output bytes with proper encoding handling and binary detection

        Args:
            output_bytes: Raw bytes from SVN command

        Returns:
            Decoded string, with fallback handling for encoding issues and binary content detection
        """
        if not output_bytes:
            return ""

        # Try different encodings in order of preference
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                decoded = output_bytes.decode(encoding)

                # Check if this is actually binary content by analyzing the decoded content
                if self._is_binary_content(decoded):
                    return "Binary file content detected - diff not available for binary files"

                return decoded
            except (UnicodeDecodeError, UnicodeError):
                continue

        # If all encodings fail, use error handling
        try:
            return output_bytes.decode('utf-8', errors='replace')
        except Exception:
            return "Error: Unable to decode SVN output - possibly binary content"

    def _is_binary_content(self, content: str) -> bool:
        """
        Determine if content is binary by analyzing the actual content characteristics

        This uses a more robust approach than file extensions, analyzing the actual
        content to determine if it's binary or text.

        Args:
            content: Decoded string content to analyze

        Returns:
            True if content appears to be binary, False if it's text
        """
        if not content:
            return False

        # Sample the content - for large files, we only need to check a portion
        sample_size = min(8192, len(content))  # Check first 8KB
        sample = content[:sample_size]

        # Method 1: Check for null bytes (most reliable binary indicator)
        if '\x00' in sample:
            return True

        # Method 2: Check ratio of printable characters
        printable_chars = 0
        control_chars = 0

        for char in sample:
            if char.isprintable() or char in '\n\r\t\f\v':
                printable_chars += 1
            elif ord(char) < 32:  # Control characters (except whitespace)
                control_chars += 1

        total_chars = len(sample)
        if total_chars == 0:
            return False

        printable_ratio = printable_chars / total_chars
        control_ratio = control_chars / total_chars

        # If less than 85% printable characters, likely binary
        if printable_ratio < 0.85:
            return True

        # If more than 5% control characters, likely binary
        if control_ratio > 0.05:
            return True

        # Method 3: Check for common binary file signatures in the content
        # These are patterns that commonly appear in binary files
        binary_patterns = [
            '\xFF\xFE',      # UTF-16 LE BOM
            '\xFE\xFF',      # UTF-16 BE BOM
            '\xFF\xFF',      # Common in some binary formats
            '\x89PNG',       # PNG signature
            'GIF8',          # GIF signature
            '\xFF\xD8\xFF',  # JPEG signature
            'PK\x03\x04',    # ZIP signature
            'PK\x05\x06',    # ZIP signature
            'PK\x07\x08',    # ZIP signature
            '\x7FELF',       # ELF binary signature
            'MZ',            # DOS/Windows executable signature
        ]

        for pattern in binary_patterns:
            if pattern in sample:
                return True

        # Method 4: Check for excessive high-bit characters (non-ASCII)
        high_bit_chars = sum(1 for char in sample if ord(char) > 127)
        high_bit_ratio = high_bit_chars / total_chars

        # If more than 30% high-bit characters, might be binary
        # (but allow for UTF-8 text files with international characters)
        if high_bit_ratio > 0.30:
            # Additional check: see if it looks like valid UTF-8
            try:
                sample.encode('utf-8')
                # If it encodes cleanly as UTF-8, it's probably text
                return False
            except UnicodeEncodeError:
                # If it doesn't encode as UTF-8, it's likely binary
                return True

        # If we get here, it's likely text
        return False



    def _get_repository_config(self, repository_url: str) -> Optional[dict]:
        """Get repository configuration including credentials"""
        try:
            if not self.config_manager:
                self.logger.warning("No config manager available for repository credentials")
                return None

            config = self.config_manager.load_config()
            repositories = config.repositories

            self.logger.debug(f"Looking for repository URL: {repository_url}")
            self.logger.debug(f"Available repositories: {[r.url for r in repositories]}")

            # Find repository by URL
            for repo in repositories:
                if repo.url == repository_url:
                    self.logger.debug(f"Found repository config with username: {repo.username or 'None'}")
                    # Convert RepositoryConfig to dict for compatibility
                    return {
                        'url': repo.url,
                        'username': repo.username,
                        'password': repo.password,
                        'name': repo.name,
                        'id': repo.id
                    }

            self.logger.warning(f"Repository configuration not found for URL: {repository_url}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting repository configuration: {e}")
            return None
    
    def _get_git_diff(self, document: DocumentRecord) -> Optional[str]:
        """Generate Git diff for a specific revision"""
        try:
            # For Git, we need to get the diff for the specific commit
            # This would require cloning/accessing the git repository
            # For now, return a placeholder
            self.logger.warning("Git diff generation not yet implemented")
            return "Git diff generation not yet implemented"
            
        except Exception as e:
            self.logger.error(f"Error running Git diff: {e}")
            return None
    
    def can_generate_diff(self, document: DocumentRecord) -> bool:
        """
        Check if we can generate diff for this document

        Args:
            document: DocumentRecord to check

        Returns:
            True if diff can be generated, False otherwise
        """
        # If repository metadata is available, use it
        if (document.repository_url is not None and
            document.repository_type is not None and
            document.repository_type.lower() in ['svn', 'git']):
            return True

        # If metadata is missing, try to infer from configuration
        if self.config_manager is not None:
            try:
                config = self.config_manager.load_config()
                for repo in config.repositories:
                    if repo.id == document.repository_id:
                        # Found matching repository in config
                        repo_type = getattr(repo, 'type', 'svn')  # Default to SVN
                        return repo_type.lower() in ['svn', 'git']

                # If repository not found in config but we have a repository_id,
                # assume it's SVN (most common case)
                if document.repository_id:
                    self.logger.info(f"Repository metadata missing for {document.repository_id}, assuming SVN")
                    return True

            except Exception as e:
                self.logger.debug(f"Error checking repository configuration: {e}")
        else:
            # If repository not found in config but we have a repository_id,
            # assume it's SVN (most common case) when no config manager is available
            if document.repository_id:
                self.logger.info(f"Repository metadata missing for {document.repository_id}, assuming SVN (no config manager)")
                return True

        return False

    def _convert_to_side_by_side(self, unified_diff: str) -> str:
        """Convert unified diff to side-by-side HTML format"""
        try:
            import re

            lines = unified_diff.split('\n')
            html_parts = []

            # Start HTML structure
            html_parts.append('''
<div class="side-by-side-diff">
    <style>
        .side-by-side-diff {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .diff-header {
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .diff-table {
            width: 100%;
            border-collapse: collapse;
        }
        .diff-table td {
            padding: 2px 8px;
            vertical-align: top;
            border: none;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .line-number {
            background-color: #f8f9fa;
            color: #666;
            text-align: right;
            width: 60px;
            min-width: 60px;
            border-right: 1px solid #ddd;
            user-select: none;
            white-space: nowrap;
        }
        .line-added {
            background-color: #d4edda;
            color: #155724;
        }
        .line-removed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .line-context {
            background-color: #fff;
            color: #333;
        }
        .line-info {
            background-color: #d1ecf1;
            color: #0c5460;
            font-weight: bold;
        }
        .diff-highlight-removed {
            background-color: #ff6b6b;
            color: #fff;
            padding: 1px 2px;
            border-radius: 2px;
        }
        .diff-highlight-added {
            background-color: #51cf66;
            color: #fff;
            padding: 1px 2px;
            border-radius: 2px;
        }
    </style>
    <table class="diff-table">
''')

            current_file = None
            left_line_num = 0
            right_line_num = 0

            # Process lines and group consecutive changes
            i = 0
            while i < len(lines):
                line = lines[i]
                self.logger.debug(f"Processing line {i}: '{line[:50]}...' (starts with: {line[:1] if line else 'EMPTY'})")

                if line.startswith('Index: ') or line.startswith('==='):
                    if line.startswith('Index: '):
                        current_file = line[7:]  # Remove "Index: "
                        html_parts.append(f'<tr><td colspan="4" class="diff-header">📄 {current_file}</td></tr>')
                elif line.startswith('@@'):
                    # Parse hunk header like "@@ -1,4 +1,6 @@"
                    match = re.match(r'@@ -(\d+),?\d* \+(\d+),?\d* @@', line)
                    if match:
                        left_line_num = int(match.group(1))
                        right_line_num = int(match.group(2))
                    html_parts.append(f'<tr><td colspan="4" class="line-info">{line}</td></tr>')
                elif line.startswith('---') or line.startswith('+++'):
                    # Skip these lines as they're redundant with Index
                    pass
                elif line.startswith('-') or line.startswith('+'):
                    # Start of a change block - collect all consecutive changes
                    change_block = []

                    # Collect all consecutive - and + lines
                    while i < len(lines) and (lines[i].startswith('-') or lines[i].startswith('+')):
                        change_block.append(lines[i])
                        i += 1

                    # Separate removed and added lines
                    removed_lines = []
                    added_lines = []

                    for change_line in change_block:
                        if change_line.startswith('-'):
                            removed_lines.append((left_line_num, change_line[1:]))  # Remove '-' prefix
                            left_line_num += 1
                        elif change_line.startswith('+'):
                            added_lines.append((right_line_num, change_line[1:]))  # Remove '+' prefix
                            right_line_num += 1

                    # Debug logging
                    self.logger.debug(f"Processing change block: {len(removed_lines)} removed, {len(added_lines)} added")
                    if removed_lines:
                        self.logger.debug(f"First removed line: {removed_lines[0][1][:50]}...")
                    if added_lines:
                        self.logger.debug(f"First added line: {added_lines[0][1][:50]}...")

                    # Create side-by-side rows for changes
                    max_lines = max(len(removed_lines), len(added_lines))
                    for j in range(max_lines):
                        left_num = removed_lines[j][0] if j < len(removed_lines) else ""
                        left_content = removed_lines[j][1] if j < len(removed_lines) else ""
                        left_class = "line-removed" if j < len(removed_lines) else "line-context"

                        right_num = added_lines[j][0] if j < len(added_lines) else ""
                        right_content = added_lines[j][1] if j < len(added_lines) else ""
                        right_class = "line-added" if j < len(added_lines) else "line-context"

                        # Apply inline diff highlighting if both sides have content
                        if j < len(removed_lines) and j < len(added_lines):
                            left_highlighted, right_highlighted = self._compute_inline_diff(left_content, right_content)
                        else:
                            left_highlighted = self._escape_html(left_content)
                            right_highlighted = self._escape_html(right_content)

                        html_parts.append(f'''
<tr>
    <td class="line-number">{left_num}</td>
    <td class="{left_class}">{left_highlighted}</td>
    <td class="line-number">{right_num}</td>
    <td class="{right_class}">{right_highlighted}</td>
</tr>''')

                    # Don't increment i here as it was already incremented in the while loop
                    continue


                elif line.startswith(' ') or (line and not line.startswith(('Index:', '===', '---', '+++', '@@'))):
                    # Context line
                    content = line[1:] if line.startswith(' ') else line
                    html_parts.append(f'''
<tr>
    <td class="line-number">{left_line_num}</td>
    <td class="line-context">{self._escape_html(content)}</td>
    <td class="line-number">{right_line_num}</td>
    <td class="line-context">{self._escape_html(content)}</td>
</tr>''')
                    left_line_num += 1
                    right_line_num += 1

                i += 1

            # Close HTML structure
            html_parts.append('    </table>\n</div>')

            return ''.join(html_parts)

        except Exception as e:
            self.logger.error(f"Error converting diff to side-by-side: {e}")
            return f"Error converting diff format: {str(e)}"

    def test_side_by_side_conversion(self):
        """Test method to verify side-by-side conversion works correctly"""
        test_diff = """Index: test.txt
===================================================================
--- test.txt	(revision 1)
+++ test.txt	(working copy)
@@ -15,4 +15,4 @@
-String friendlyName = "Garage";
-#define HOST_NAME "Garage-esp"
-const char* serialNumber = "221517K0101768";
-const char* uuid = "904bfa3c-1de2-11v2-8728-fd8eebaf492d";
+String friendlyName = "Porch";
+#define HOST_NAME "Porch-esp"
+const char* serialNumber = "221517K0101769";
+const char* uuid = "904bfa3c-1de2-11v2-8728-fd8eebaf492e";
"""
        result = self._convert_to_side_by_side(test_diff)
        self.logger.info(f"Test conversion result: {result[:200]}...")
        return result

    def _escape_html(self, text: str) -> str:
        """Escape HTML characters in text"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))

    def _compute_inline_diff(self, old_text: str, new_text: str) -> tuple:
        """Compute character-level differences and return highlighted versions"""
        import difflib

        # Strip trailing whitespace for comparison to avoid highlighting line ending differences
        old_stripped = old_text.rstrip()
        new_stripped = new_text.rstrip()

        # If the only difference is trailing whitespace, don't highlight anything
        if old_stripped == new_stripped:
            return self._escape_html(old_text), self._escape_html(new_text)

        # Use difflib to get character-level differences on stripped content
        matcher = difflib.SequenceMatcher(None, old_stripped, new_stripped)

        old_highlighted = []
        new_highlighted = []

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                # Same content in both
                old_highlighted.append(self._escape_html(old_stripped[i1:i2]))
                new_highlighted.append(self._escape_html(new_stripped[j1:j2]))
            elif tag == 'delete':
                # Content only in old (removed)
                deleted_text = old_stripped[i1:i2]
                # Only highlight if it's not just whitespace
                if deleted_text.strip():
                    old_highlighted.append(f'<span class="diff-highlight-removed">{self._escape_html(deleted_text)}</span>')
                else:
                    old_highlighted.append(self._escape_html(deleted_text))
            elif tag == 'insert':
                # Content only in new (added)
                inserted_text = new_stripped[j1:j2]
                # Only highlight if it's not just whitespace
                if inserted_text.strip():
                    new_highlighted.append(f'<span class="diff-highlight-added">{self._escape_html(inserted_text)}</span>')
                else:
                    new_highlighted.append(self._escape_html(inserted_text))
            elif tag == 'replace':
                # Content changed
                old_part = old_stripped[i1:i2]
                new_part = new_stripped[j1:j2]

                # Only highlight if the changes aren't just whitespace
                if old_part.strip():
                    old_highlighted.append(f'<span class="diff-highlight-removed">{self._escape_html(old_part)}</span>')
                else:
                    old_highlighted.append(self._escape_html(old_part))

                if new_part.strip():
                    new_highlighted.append(f'<span class="diff-highlight-added">{self._escape_html(new_part)}</span>')
                else:
                    new_highlighted.append(self._escape_html(new_part))

        # Add back any trailing whitespace without highlighting
        old_trailing = old_text[len(old_stripped):]
        new_trailing = new_text[len(new_stripped):]

        return ''.join(old_highlighted) + self._escape_html(old_trailing), ''.join(new_highlighted) + self._escape_html(new_trailing)
