#!/usr/bin/env python3
"""
Test the UnifiedDocumentProcessor to ensure it works correctly
"""

import logging
import tempfile
from datetime import datetime
from pathlib import Path
from unified_document_processor import UnifiedDocumentProcessor, ProcessingSource
from document_database import DocumentRecord
from models import CommitInfo, RepositoryConfig
from monitor_service import MonitorService

def setup_logging():
    """Setup logging to see processing details"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_unified_processor():
    """Test the unified processor with different processing sources"""
    print("🧪 Testing UnifiedDocumentProcessor")
    print("=" * 50)
    
    setup_logging()
    
    # Initialize monitor service for Ollama client
    monitor_service = MonitorService("data/config.json")
    
    print(f"📋 Configuration:")
    print(f"  Default model: {monitor_service.config.ollama_model}")
    print(f"  Risk assessment model: {monitor_service.config.ollama_model_risk_assessment}")
    print(f"  Code review model: {monitor_service.config.ollama_model_code_review}")
    
    # Create unified processor
    processor = UnifiedDocumentProcessor(
        output_dir=monitor_service.config.output_dir,
        db_path="/app/data/documents.db",
        ollama_client=monitor_service.ollama_client,
        config_manager=monitor_service.config_manager,
        max_concurrent_tasks=2
    )
    
    print(f"\n🔧 Processor setup:")
    print(f"  Has Ollama client: {processor.ollama_client is not None}")
    print(f"  MetadataExtractor has Ollama client: {processor.metadata_extractor.ollama_client is not None}")
    print(f"  Max concurrent tasks: {processor.max_concurrent_tasks}")
    
    # Start the processor
    processor.start()
    
    try:
        # Test 1: Historical scan processing
        print(f"\n🔍 Test 1: Historical Scan Processing")
        test_commit_info = CommitInfo(
            revision="999",
            author="test_user",
            date=datetime.now().isoformat(),
            message="Implement OAuth2 authentication with JWT tokens",
            changed_paths=["src/auth/oauth.py", "config/auth.json"],
            diff="@@ -0,0 +1,10 @@\n+# OAuth2 Implementation\n+class OAuth2Handler:\n+    def authenticate(self, token):\n+        return validate_jwt(token)",
            repository_id="test-repo",
            repository_name="reposense_ai"
        )
        
        test_repo_config = RepositoryConfig(
            name="reposense_ai",
            url="https://github.com/test/reposense_ai",
            enabled=True
        )
        
        test_documentation = """
# Revision 999 - OAuth2 Security Enhancement

## Summary
This revision introduces OAuth2 authentication with JWT tokens for enhanced security.

## Code Review Recommendation
**RECOMMENDED** - HIGH PRIORITY - Authentication changes require thorough security review.

## Risk Level
**HIGH RISK** - Authentication system changes require extensive testing.

## Documentation Impact
**YES** - API documentation requires updates for new OAuth2 flow.

## Changes Made
- Implemented OAuth2 authorization server
- Added JWT token validation middleware
- Enhanced security logging and audit trails
"""
        
        success = processor.process_commit(
            test_commit_info, 
            test_repo_config, 
            test_documentation, 
            priority=8
        )
        
        if success:
            print(f"  ✅ Historical scan task queued successfully")
        else:
            print(f"  ❌ Failed to queue historical scan task")
        
        # Test 2: Content processing (web/API)
        print(f"\n🔍 Test 2: Content Processing (Web/API)")
        test_doc_record = DocumentRecord(
            id="test-content-processing",
            repository_id="test-repo",
            repository_name="reposense_ai",
            revision=1000,
            date=datetime.now(),
            filename="test_content.md",
            filepath="/tmp/test_content.md",
            size=1500,
            author="api_user",
            commit_message="Test content processing via API",
            changed_paths=["src/api/endpoints.py"],
            repository_type="git"
        )
        
        test_content = """
## Code Review Recommendation
MEDIUM PRIORITY - API endpoint changes should be reviewed for security.

## Risk Level
MEDIUM RISK - New API endpoints require validation testing.

## Documentation Impact
YES - API documentation needs updates for new endpoints.
"""
        
        success = processor.process_content(
            test_content,
            test_doc_record,
            priority=6
        )
        
        if success:
            print(f"  ✅ Content processing task queued successfully")
        else:
            print(f"  ❌ Failed to queue content processing task")
        
        # Wait a bit for processing
        import time
        print(f"\n⏳ Waiting for processing to complete...")
        time.sleep(10)
        
        # Check statistics
        stats = processor.get_stats()
        print(f"\n📊 Processing Statistics:")
        print(f"  Processed count: {stats['processed_count']}")
        print(f"  Error count: {stats['error_count']}")
        print(f"  Queue size: {stats['queue_size']}")
        print(f"  Active threads: {stats['active_threads']}")
        print(f"  Total processing time: {stats['processing_time_total']:.3f}s")
        
        # Verify documents were stored with AI model information
        from document_database import DocumentDatabase
        db = DocumentDatabase("/app/data/documents.db")
        docs = db.get_documents(limit=3)
        
        print(f"\n🔍 Verification - Latest documents:")
        for i, doc in enumerate(docs):
            ai_model = doc.ai_model_used or 'Not set'
            print(f"  {i+1}. {doc.filename}")
            print(f"     AI Model Used: {ai_model}")
            print(f"     Risk Level: {doc.risk_level}")
            print(f"     Code Review: {doc.code_review_recommended}")
        
        # Check if any documents have the specialized model
        specialized_docs = [doc for doc in docs if doc.ai_model_used and doc.ai_model_used != monitor_service.config.ollama_model]
        
        if specialized_docs:
            print(f"\n🎉 SUCCESS: Found {len(specialized_docs)} documents using specialized models!")
            for doc in specialized_docs:
                print(f"  📋 {doc.filename}: {doc.ai_model_used}")
            return True
        else:
            print(f"\n⚠️  WARNING: No documents found using specialized models")
            print(f"  Expected specialized model: {monitor_service.config.ollama_model_risk_assessment}")
            print(f"  Default model: {monitor_service.config.ollama_model}")
            return False
            
    finally:
        # Stop the processor
        processor.stop()

if __name__ == "__main__":
    success = test_unified_processor()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 UnifiedDocumentProcessor is working correctly!")
        print("🎯 Specialized models are being used and tracked.")
        print("🔄 Ready for integration into the main application.")
    else:
        print("❌ UnifiedDocumentProcessor needs debugging.")
        print("🔍 Check the logs above for specific issues.")
