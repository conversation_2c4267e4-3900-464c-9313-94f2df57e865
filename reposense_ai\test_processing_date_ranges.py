#!/usr/bin/env python3
"""
Test Processing Date Ranges

This test checks what dates the JavaScript would generate and tests the database queries.
"""

import sqlite3
from datetime import datetime, timedelta

def test_processing_date_ranges():
    """Test the processing date range calculations"""
    print("🔄 Testing Processing Date Range Logic")
    print("=" * 50)
    
    # Simulate the JavaScript date calculation
    today = datetime.now()
    print(f"Today: {today}")
    print(f"Today formatted: {today.strftime('%Y-%m-%d')}")
    
    # Test Today calculation
    print("\n1️⃣ Testing 'Today' calculation:")
    today_from = datetime(today.year, today.month, today.day)
    today_to = datetime(today.year, today.month, today.day)
    print(f"  From: {today_from.strftime('%Y-%m-%d')}")
    print(f"  To: {today_to.strftime('%Y-%m-%d')}")
    
    # Test Last 7 days calculation
    print("\n2️⃣ Testing 'Last 7 Days' calculation:")
    from_date_7 = datetime(today.year, today.month, today.day) - timedelta(days=7)
    to_date_7 = datetime(today.year, today.month, today.day)
    print(f"  From: {from_date_7.strftime('%Y-%m-%d')}")
    print(f"  To: {to_date_7.strftime('%Y-%m-%d')}")
    
    # Test Last 30 days calculation
    print("\n3️⃣ Testing 'Last 30 Days' calculation:")
    from_date_30 = datetime(today.year, today.month, today.day) - timedelta(days=30)
    to_date_30 = datetime(today.year, today.month, today.day)
    print(f"  From: {from_date_30.strftime('%Y-%m-%d')}")
    print(f"  To: {to_date_30.strftime('%Y-%m-%d')}")
    
    # Test database queries
    print("\n🗄️ Testing Database Queries")
    print("=" * 50)
    
    conn = sqlite3.connect('/app/data/documents.db')
    cursor = conn.cursor()
    
    # Get sample data
    cursor.execute('SELECT processed_time FROM documents ORDER BY processed_time DESC LIMIT 3')
    results = cursor.fetchall()
    print("Sample processing dates in database:")
    for i, row in enumerate(results, 1):
        print(f"  {i}. {row[0]}")
    
    # Test Today query
    print(f"\n1️⃣ Testing Today query:")
    from_param_today = f"{today_from.strftime('%Y-%m-%d')}T00:00:00"
    end_date_today = today_to + timedelta(days=1)
    to_param_today = end_date_today.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"  Query: processed_time >= '{from_param_today}' AND processed_time < '{to_param_today}'")
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', 
                   (from_param_today, to_param_today))
    count_today = cursor.fetchone()[0]
    print(f"  Result: {count_today} documents")
    
    # Test Last 7 days query
    print(f"\n2️⃣ Testing Last 7 Days query:")
    from_param_7 = f"{from_date_7.strftime('%Y-%m-%d')}T00:00:00"
    end_date_7 = to_date_7 + timedelta(days=1)
    to_param_7 = end_date_7.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"  Query: processed_time >= '{from_param_7}' AND processed_time < '{to_param_7}'")
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', 
                   (from_param_7, to_param_7))
    count_7 = cursor.fetchone()[0]
    print(f"  Result: {count_7} documents")
    
    # Test Last 30 days query
    print(f"\n3️⃣ Testing Last 30 Days query:")
    from_param_30 = f"{from_date_30.strftime('%Y-%m-%d')}T00:00:00"
    end_date_30 = to_date_30 + timedelta(days=1)
    to_param_30 = end_date_30.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"  Query: processed_time >= '{from_param_30}' AND processed_time < '{to_param_30}'")
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', 
                   (from_param_30, to_param_30))
    count_30 = cursor.fetchone()[0]
    print(f"  Result: {count_30} documents")
    
    # Analysis
    print(f"\n📊 Analysis:")
    print(f"  Today: {count_today} documents")
    print(f"  Last 7 days: {count_7} documents")
    print(f"  Last 30 days: {count_30} documents")
    
    if count_today > 0 and count_7 == 0:
        print("\n❌ Issue found: Today has documents but Last 7 days doesn't!")
        print("   This suggests the date range calculation is wrong.")
        
        # Check if today's date is included in the 7-day range
        today_str = today.strftime('%Y-%m-%d')
        from_7_str = from_date_7.strftime('%Y-%m-%d')
        to_7_str = to_date_7.strftime('%Y-%m-%d')
        
        print(f"\n🔍 Date range check:")
        print(f"   Today: {today_str}")
        print(f"   7-day range: {from_7_str} to {to_7_str}")
        print(f"   Is today in range? {from_7_str <= today_str <= to_7_str}")
        
    elif count_today == count_7 == count_30:
        print("\n✅ All ranges return the same count - this is expected if all documents are from today")
    
    conn.close()
    
    return count_today, count_7, count_30

if __name__ == "__main__":
    test_processing_date_ranges()
