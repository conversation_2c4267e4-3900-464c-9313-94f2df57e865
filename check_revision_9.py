#!/usr/bin/env python3
"""
Check if revision 9 for reposense_cpp_test was scanned and processed
"""

import sys
import os
import sqlite3
from datetime import datetime

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from document_database import DocumentDatabase
from config_manager import ConfigManager

def check_revision_9():
    """Check if revision 9 for reposense_cpp_test was processed"""
    print("🔍 Checking if revision 9 for reposense_cpp_test was scanned...")
    
    try:
        # Try different database paths
        db_paths = [
            '/app/data/documents.db',
            '/app/data/reposense_ai.db', 
            'reposense_ai/data/documents.db',
            'reposense_ai/data/reposense_ai.db',
            'data/documents.db',
            'data/reposense_ai.db'
        ]
        
        db_path = None
        for path in db_paths:
            if os.path.exists(path):
                db_path = path
                print(f"✅ Found database at: {path}")
                break
        
        if not db_path:
            print("❌ No database file found")
            return
            
        # Connect to database directly
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check for reposense_cpp_test documents
        print(f"\n📊 Searching for reposense_cpp_test documents...")
        
        # First, see what repository names exist
        cursor.execute("SELECT DISTINCT repository_name FROM documents ORDER BY repository_name")
        repo_names = cursor.fetchall()
        print(f"Available repositories in database:")
        for name in repo_names:
            print(f"  - '{name[0]}'")
        
        # Search for reposense_cpp_test specifically
        cursor.execute("""
            SELECT id, repository_name, revision, date, author, commit_message, processed_time
            FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%' 
            ORDER BY revision DESC
        """)
        
        docs = cursor.fetchall()
        print(f"\n📋 Found {len(docs)} documents for reposense_cpp_test:")
        
        revision_9_found = False
        for doc in docs:
            doc_id, repo_name, revision, date, author, commit_msg, processed_time = doc
            print(f"  Revision {revision}: {commit_msg[:50]}{'...' if len(commit_msg) > 50 else ''}")
            print(f"    Date: {date}, Author: {author}")
            print(f"    Processed: {processed_time}")
            print(f"    ID: {doc_id}")
            
            if revision == 9:
                revision_9_found = True
                print(f"    ✅ REVISION 9 FOUND!")
            print()
        
        # Check specifically for revision 9
        cursor.execute("""
            SELECT id, repository_name, revision, date, author, commit_message, processed_time
            FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%' AND revision = 9
        """)
        
        rev_9_docs = cursor.fetchall()
        
        if rev_9_docs:
            print(f"🎯 Revision 9 Details:")
            for doc in rev_9_docs:
                doc_id, repo_name, revision, date, author, commit_msg, processed_time = doc
                print(f"  ✅ Found revision 9!")
                print(f"  Repository: {repo_name}")
                print(f"  Date: {date}")
                print(f"  Author: {author}")
                print(f"  Commit Message: {commit_msg}")
                print(f"  Processed Time: {processed_time}")
                print(f"  Document ID: {doc_id}")
                
                # Check if there's a document file
                doc_file_path = f"reposense_ai/data/documents/{doc_id}.md"
                if os.path.exists(doc_file_path):
                    print(f"  ✅ Document file exists: {doc_file_path}")
                    # Get file size
                    file_size = os.path.getsize(doc_file_path)
                    print(f"  File size: {file_size} bytes")
                else:
                    print(f"  ❌ Document file not found: {doc_file_path}")
        else:
            print(f"❌ Revision 9 NOT found in database")
            print(f"   This means it hasn't been scanned yet or there was an issue")
        
        # Check the latest revision to see what's been processed
        cursor.execute("""
            SELECT MAX(revision) FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%'
        """)
        max_revision = cursor.fetchone()[0]
        
        if max_revision:
            print(f"\n📈 Latest revision processed: {max_revision}")
            if max_revision >= 9:
                print(f"  ✅ System has processed up to revision {max_revision}")
            else:
                print(f"  ⏳ System has only processed up to revision {max_revision}, revision 9 not yet scanned")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking revision 9: {e}")
        import traceback
        traceback.print_exc()

def check_recent_logs():
    """Check recent logs for scanning activity"""
    print(f"\n📋 Checking recent logs for scanning activity...")
    
    try:
        log_paths = [
            'reposense_ai/data/reposense_ai.log',
            '/app/data/logs/reposense_ai.log',
            'data/reposense_ai.log'
        ]
        
        log_path = None
        for path in log_paths:
            if os.path.exists(path):
                log_path = path
                break
        
        if not log_path:
            print("❌ No log file found")
            return
            
        print(f"📄 Reading logs from: {log_path}")
        
        # Look for recent scanning activity
        with open(log_path, 'r') as f:
            lines = f.readlines()
        
        # Get last 100 lines and look for relevant activity
        recent_lines = lines[-100:]
        
        scanning_activity = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in ['checking for new commits', 'revision', 'reposense_cpp_test', 'queued', 'processed']):
                scanning_activity.append(line.strip())
        
        if scanning_activity:
            print(f"🔍 Recent scanning activity:")
            for line in scanning_activity[-10:]:  # Show last 10 relevant lines
                print(f"  {line}")
        else:
            print(f"❌ No recent scanning activity found in logs")
            
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

if __name__ == "__main__":
    check_revision_9()
    check_recent_logs()
    
    print(f"\n💡 Summary:")
    print(f"  - If revision 9 is found: The system successfully scanned it")
    print(f"  - If revision 9 is not found: Either not scanned yet or scanning failed")
    print(f"  - Check the logs for any error messages during scanning")
    print(f"  - The system scans every 5 minutes, so it should appear soon if recently committed")
