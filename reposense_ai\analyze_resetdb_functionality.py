#!/usr/bin/env python3
"""
Analyze what the current ResetDB functionality actually resets vs what it should reset
"""

import sys
import os
import json
import requests
sys.path.append('/app')

def analyze_current_resetdb():
    """Analyze what the current ResetDB functionality actually does"""
    print("🔍 Analyzing Current ResetDB Functionality")
    print("=" * 50)
    
    print("📋 What Current ResetDB DOES Reset:")
    current_resets = [
        "✅ Database file (/app/data/documents.db) - Completely deleted and recreated",
        "✅ Revision documents (/app/data/output/repositories/*.md) - All deleted",
        "✅ Document service cache - Cleared via force_rescan()",
        "✅ Database backup - Creates timestamped backup before reset"
    ]
    
    for item in current_resets:
        print(f"   {item}")
    
    print(f"\n❌ What Current ResetDB DOES NOT Reset:")
    not_reset = [
        "❌ config.json settings - All configuration remains unchanged",
        "❌ Repository definitions - All repositories remain configured", 
        "❌ User accounts - <PERSON><PERSON>, manager, and other users remain",
        "❌ Email settings - SMTP configuration and recipients remain",
        "❌ SVN server settings - Server URL, credentials remain",
        "❌ Ollama settings - Model selection and host remain",
        "❌ Web interface settings - Port, host, secret key remain",
        "❌ Log settings - Log rotation and cleanup settings remain",
        "❌ Repository credentials - SVN/Git usernames and passwords remain",
        "❌ Email recipient lists - Global and per-repository recipients remain",
        "❌ Risk assessment settings - Aggressiveness levels remain",
        "❌ Product documentation files - File lists remain configured",
        "❌ Historical scan settings - Scan configurations remain",
        "❌ Branch monitoring settings - Branch paths and monitoring flags remain"
    ]
    
    for item in not_reset:
        print(f"   {item}")

def analyze_config_components():
    """Analyze all components stored in config.json that should be resetable"""
    print(f"\n📊 Analyzing config.json Components")
    print("=" * 40)
    
    try:
        with open('/app/data/config.json', 'r') as f:
            config = json.load(f)
        
        components = {
            'Users': len(config.get('users', [])),
            'Repositories': len(config.get('repositories', [])),
            'Email Recipients': len(config.get('email_recipients', [])),
            'SMTP Settings': 'smtp_host' in config,
            'SVN Settings': 'svn_server_url' in config,
            'Ollama Settings': 'ollama_host' in config,
            'Web Settings': 'web_port' in config
        }
        
        print("📋 Current config.json contains:")
        for component, value in components.items():
            if isinstance(value, bool):
                status = "✅" if value else "❌"
                print(f"   {status} {component}: {'Configured' if value else 'Not configured'}")
            else:
                print(f"   📊 {component}: {value}")
        
        # Show sample repository configuration
        if config.get('repositories'):
            repo = config['repositories'][0]
            print(f"\n📝 Sample Repository Configuration:")
            repo_settings = [
                f"Name: {repo.get('name')}",
                f"URL: {repo.get('url')}",
                f"Type: {repo.get('type')}",
                f"Enabled: {repo.get('enabled')}",
                f"Email Recipients: {len(repo.get('email_recipients', []))}",
                f"Risk Level: {repo.get('risk_aggressiveness')}",
                f"Branch Path: {repo.get('branch_path')}",
                f"Monitor All Branches: {repo.get('monitor_all_branches')}"
            ]
            for setting in repo_settings:
                print(f"      • {setting}")
        
        # Show sample user configuration
        if config.get('users'):
            user = config['users'][0]
            print(f"\n👤 Sample User Configuration:")
            user_settings = [
                f"Username: {user.get('username')}",
                f"Email: {user.get('email')}",
                f"Role: {user.get('role')}",
                f"Enabled: {user.get('enabled')}",
                f"Receive All Notifications: {user.get('receive_all_notifications')}"
            ]
            for setting in user_settings:
                print(f"      • {setting}")
                
    except Exception as e:
        print(f"❌ Error analyzing config.json: {e}")

def propose_complete_reset():
    """Propose what a complete RepoSense reset should include"""
    print(f"\n🎯 Proposed Complete RepoSense Reset")
    print("=" * 40)
    
    print("🔄 A complete reset should include:")
    
    complete_reset_items = {
        "Database & Documents": [
            "✅ Delete documents.db (already implemented)",
            "✅ Delete all revision documents (already implemented)",
            "✅ Clear document service cache (already implemented)"
        ],
        "Configuration Reset": [
            "❌ Reset config.json to default values (NOT implemented)",
            "❌ Clear all repository definitions (NOT implemented)",
            "❌ Reset email settings to defaults (NOT implemented)",
            "❌ Reset SVN server settings (NOT implemented)",
            "❌ Reset Ollama settings to defaults (NOT implemented)"
        ],
        "User Management": [
            "❌ Remove all user accounts (NOT implemented)",
            "❌ Clear email recipient lists (NOT implemented)",
            "❌ Reset user roles and permissions (NOT implemented)"
        ],
        "Repository Settings": [
            "❌ Remove all repository configurations (NOT implemented)",
            "❌ Clear repository credentials (NOT implemented)",
            "❌ Reset risk assessment settings (NOT implemented)",
            "❌ Clear product documentation file lists (NOT implemented)",
            "❌ Reset historical scan configurations (NOT implemented)"
        ],
        "System Settings": [
            "❌ Reset web interface settings (NOT implemented)",
            "❌ Reset log settings (NOT implemented)",
            "❌ Generate new web secret key (NOT implemented)"
        ]
    }
    
    for category, items in complete_reset_items.items():
        print(f"\n📂 {category}:")
        for item in items:
            print(f"   {item}")

def suggest_implementation_options():
    """Suggest implementation options for complete reset"""
    print(f"\n💡 Implementation Options")
    print("=" * 30)
    
    options = {
        "Option 1: Enhanced ResetDB": {
            "description": "Extend current ResetDB to also reset config.json",
            "pros": [
                "Single button for complete reset",
                "Consistent with current UI",
                "Simple implementation"
            ],
            "cons": [
                "Destructive - loses all configuration",
                "No granular control",
                "May be too aggressive for some users"
            ]
        },
        "Option 2: Separate Reset Options": {
            "description": "Add separate buttons for different reset types",
            "pros": [
                "Granular control over what to reset",
                "Users can choose what to keep",
                "Less risk of accidental data loss"
            ],
            "cons": [
                "More complex UI",
                "Multiple buttons to maintain",
                "Potential user confusion"
            ]
        },
        "Option 3: Reset Wizard": {
            "description": "Multi-step wizard to choose what to reset",
            "pros": [
                "Very clear about what will be reset",
                "Prevents accidental resets",
                "Educational for users"
            ],
            "cons": [
                "More complex to implement",
                "Slower for experienced users",
                "Additional UI complexity"
            ]
        }
    }
    
    for option_name, details in options.items():
        print(f"\n🔧 {option_name}:")
        print(f"   📝 {details['description']}")
        print(f"   ✅ Pros:")
        for pro in details['pros']:
            print(f"      • {pro}")
        print(f"   ❌ Cons:")
        for con in details['cons']:
            print(f"      • {con}")

def recommend_solution():
    """Recommend the best solution"""
    print(f"\n🎯 Recommended Solution")
    print("=" * 25)
    
    print("🏆 RECOMMENDATION: Enhanced ResetDB with Options")
    print()
    print("📋 Implementation Plan:")
    
    plan = [
        "1. Keep current 'Reset Database' button for documents/database only",
        "2. Add new 'Complete System Reset' button for everything",
        "3. Add checkbox options for granular control:",
        "   □ Reset database and documents (current functionality)",
        "   □ Reset all repositories and settings", 
        "   □ Reset user accounts",
        "   □ Reset email configuration",
        "   □ Reset SVN/Git server settings",
        "   □ Reset AI model settings",
        "4. Default to 'Reset database and documents' only",
        "5. Require explicit confirmation for each additional option",
        "6. Show clear preview of what will be reset before confirmation"
    ]
    
    for step in plan:
        print(f"   {step}")
    
    print(f"\n🎯 Benefits of This Approach:")
    benefits = [
        "Backward compatible - current ResetDB behavior unchanged",
        "Granular control - users choose what to reset",
        "Safe defaults - only resets documents unless explicitly requested",
        "Clear communication - users know exactly what will happen",
        "Flexible - supports both quick resets and complete clean slate"
    ]
    
    for benefit in benefits:
        print(f"   ✅ {benefit}")

if __name__ == "__main__":
    analyze_current_resetdb()
    analyze_config_components()
    propose_complete_reset()
    suggest_implementation_options()
    recommend_solution()
    
    print(f"\n📋 Summary:")
    print(f"   🔍 Current ResetDB only resets database and documents")
    print(f"   ⚙️  config.json settings (repositories, users, email, etc.) are NOT reset")
    print(f"   💡 Recommendation: Add enhanced reset options with granular control")
    print(f"   🎯 This would provide both quick document reset and complete system reset")
    print(f"\n❓ Would you like me to implement the enhanced reset functionality?")
