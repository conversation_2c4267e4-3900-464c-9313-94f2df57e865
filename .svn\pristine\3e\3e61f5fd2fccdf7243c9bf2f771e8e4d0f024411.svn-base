# RepoSense AI Integration Guide

## Overview

RepoSense AI has been restructured to integrate seamlessly into larger Docker Compose setups. The application files are now contained in the `reposense_ai/` subdirectory, making it easy to add to existing multi-service environments.

## Directory Structure

```
your-project/
├── docker-compose.yml          # Your main docker-compose file
├── reposense_ai/              # RepoSense AI application directory
│   ├── Dockerfile             # RepoSense AI Dockerfile
│   ├── data/                  # Persistent data (databases, config)
│   │   ├── config.json        # Application configuration
│   │   ├── documents.db       # SQLite database
│   │   ├── output/            # Generated documents
│   │   └── cache/             # Processing cache
│   ├── logs/                  # Application logs
│   ├── static/                # Web interface assets
│   ├── templates/             # Web interface templates
│   ├── requirements.txt       # Python dependencies
│   ├── entrypoint.sh          # Container startup script
│   └── *.py                   # Application source code
├── setup-integration.sh       # Integration setup script
└── docker-compose-full-example.yml  # Complete integration example
```

## Quick Integration Steps

### 1. Run the Setup Script

```bash
./setup-integration.sh
```

This script will:
- Verify directory structure
- Update configuration for Docker integration
- Create integration documentation
- Set proper permissions

### 2. Add RepoSense AI to Your Docker Compose

Copy this service definition to your existing `docker-compose.yml`:

```yaml
services:
  reposense-ai:
    build:
      context: ./reposense_ai
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
      - ./reposense_ai:/app  # Development mode (optional)
      - /app/node_modules
      - /app/.git
    environment:
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      - OLLAMA_BASE_URL=http://ollama:11434
      - REPOSENSE_AI_LOG_LEVEL=${REPOSENSE_AI_LOG_LEVEL:-INFO}
    networks:
      - ollama-network
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

### 3. Ensure Network Configuration

Make sure your `docker-compose.yml` includes the shared network:

```yaml
networks:
  ollama-network:
    driver: bridge
```

### 4. Start the Services

```bash
docker-compose up -d
```

## Configuration

### Environment Variables

Create a `.env` file (copy from `.env.template`) to customize:

```env
# Logging
REPOSENSE_AI_LOG_LEVEL=INFO
REPOSENSE_AI_DB_DEBUG=false

# AI Model Configuration
REPOSENSE_AI_MODEL=llama3.2
OLLAMA_BASE_URL=http://ollama:11434
```

### Application Configuration

The main configuration is stored in `reposense_ai/data/config.json`. Key settings:

- `ollama_host`: Should be `http://ollama:11434` for Docker integration
- `web_host`: Should be `0.0.0.0` for container access
- `web_port`: Should be `5000` (default)

## Service Integration

### Ollama Integration

RepoSense AI automatically connects to your Ollama service using:
- Service name: `ollama`
- Port: `11434`
- Network: `ollama-network`

### Port Mapping

- **RepoSense AI Web Interface**: `http://localhost:5000`
- **Ollama API**: `http://localhost:11434`
- **Open WebUI**: `http://localhost:3000`

## Data Persistence

All persistent data is stored in bind mounts:

- **Database**: `./reposense_ai/data/documents.db`
- **Configuration**: `./reposense_ai/data/config.json`
- **Generated Documents**: `./reposense_ai/data/output/`
- **Cache**: `./reposense_ai/data/cache/`
- **Logs**: `./reposense_ai/logs/`

## Development Mode

For development, the source code is mounted as a volume:

```yaml
volumes:
  - ./reposense_ai:/app  # Live code reloading
```

Remove this line for production deployments.

## Troubleshooting

### Connection Issues

1. **Cannot connect to Ollama**:
   - Verify both services are on `ollama-network`
   - Check Ollama is running: `docker-compose ps ollama`
   - Test connection: `docker-compose exec reposense-ai curl http://ollama:11434/api/tags`

2. **Web interface not accessible**:
   - Check port mapping: `docker-compose ps reposense-ai`
   - Verify health check: `docker-compose exec reposense-ai python -c "import requests; print(requests.get('http://localhost:5000/health').text)"`

### Permission Issues

**Most Common Issue**: Docker volume permissions

1. **Quick Fix - Use the automated script**:
   ```bash
   ./fix-docker-permissions.sh
   ```

2. **Manual Fix - Database and log errors**:
   ```bash
   # Stop container
   docker-compose stop reposense-ai

   # Fix ownership (container runs as uid 1000)
   sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs
   sudo chmod -R 755 reposense_ai/data reposense_ai/logs

   # Restart container
   docker-compose up -d reposense-ai
   ```

3. **Alternative - Fix from inside running container**:
   ```bash
   docker-compose exec --user root reposense-ai chown -R appuser:appuser /app/data /app/logs
   ```

### Configuration Issues

1. **Check configuration**:
   ```bash
   docker-compose exec reposense-ai python -c "
   import json
   with open('/app/data/config.json') as f:
       config = json.load(f)
   print('Ollama Host:', config.get('ollama_host'))
   print('Web Host:', config.get('web_host'))
   print('Web Port:', config.get('web_port'))
   "
   ```

2. **Reset configuration**:
   - Stop services: `docker-compose down`
   - Backup: `cp reposense_ai/data/config.json reposense_ai/data/config.json.backup`
   - Delete: `rm reposense_ai/data/config.json`
   - Restart: `docker-compose up -d`

## Health Checks

RepoSense AI includes built-in health checks:

```bash
# Check service health
docker-compose ps reposense-ai

# Manual health check
curl http://localhost:5000/health

# View logs
docker-compose logs reposense-ai
```

## Complete Example

See `docker-compose-full-example.yml` for a complete working example that includes:
- Ollama (GPU-enabled)
- Open WebUI
- Stable Diffusion
- RepoSense AI
- Portainer
- Qdrant
- Whisper
- TTS
- JupyterLab

## Support

For issues specific to the integration:
1. Check the logs: `docker-compose logs reposense-ai`
2. Verify network connectivity: `docker network ls` and `docker network inspect ollama-network`
3. Test Ollama connectivity from within the container
4. Check file permissions in the `reposense_ai/` directory
