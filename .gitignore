# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Python Type Checking
.mypy_cache/
.dmypy.json
dmypy.json

# Python Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# C/C++ Build directories
cmake-build-*/

# Compiled Object files
*.o
*.obj

# Compiled Dynamic libraries
*.dylib
*.dll

# Compiled Static libraries
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs (but keep structure)
*.log
*.log.*

# Temporary files
*.tmp
*.temp

# Database backups (keep only a few recent ones)
*.db.backup.*
