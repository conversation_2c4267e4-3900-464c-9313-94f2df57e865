"""
Document Service for RepoSense AI

High-performance document management with database storage, caching, and background processing.
Provides fast web interface access with pagination and filtering support.
"""

import os
import re
import time
import logging
import requests
from pathlib import Path
from typing import List, Dict, Optional, Tu<PERSON>, Any
from dataclasses import dataclass
from datetime import datetime

from document_database import DocumentDatabase, DocumentRecord
# Removed: from document_processor import DocumentProcessor  # Now using UnifiedDocumentProcessor
from cache_manager import CacheManager
from ollama_client import OllamaClient


@dataclass
class Document:
    """Represents a generated document"""
    id: str
    repository_id: str
    repository_name: str
    revision: int
    date: datetime
    filename: str
    filepath: str
    size: int
    author: str
    commit_message: str
    changed_paths: Optional[List[str]] = None  # List of files changed in this commit
    code_review_recommended: Optional[bool] = None
    code_review_priority: Optional[str] = None  # "HIGH", "MEDIUM", "LOW"
    documentation_impact: Optional[bool] = None
    risk_level: Optional[str] = None  # "HIGH", "MEDIUM", "LOW"
    file_modified_time: Optional[float] = None
    processed_time: Optional[datetime] = None
    ai_model_used: Optional[str] = None  # Track which AI model was actually used for analysis
    # Metadata for recreating diff on-demand
    repository_url: Optional[str] = None
    repository_type: Optional[str] = None
    # Optional diff content (populated on-demand)
    diff: Optional[str] = None
    # User feedback fields
    user_code_review_status: Optional[str] = None  # 'approved', 'rejected', 'needs_changes', 'in_progress'
    user_code_review_comments: Optional[str] = None
    user_code_review_reviewer: Optional[str] = None
    user_code_review_date: Optional[datetime] = None
    user_documentation_rating: Optional[int] = None  # 1-5 rating
    user_documentation_comments: Optional[str] = None
    user_documentation_updated_by: Optional[str] = None
    user_documentation_updated_date: Optional[datetime] = None
    user_risk_assessment_override: Optional[str] = None  # User can override AI risk assessment
    user_risk_assessment_comments: Optional[str] = None
    user_risk_assessment_updated_by: Optional[str] = None
    user_risk_assessment_updated_date: Optional[datetime] = None
    # User feedback fields for documentation input/augmentation
    user_documentation_input: Optional[str] = None  # User's additional documentation content
    user_documentation_suggestions: Optional[str] = None  # User's suggestions for improving AI documentation
    user_documentation_input_by: Optional[str] = None  # Who provided the documentation input
    user_documentation_input_date: Optional[datetime] = None  # When documentation input was provided

    @property
    def display_name(self) -> str:
        """Get a user-friendly display name"""
        return f"Revision {self.revision} - {self.date.strftime('%Y-%m-%d %H:%M')}"
    
    @property
    def relative_path(self) -> str:
        """Get relative path from output directory"""
        return str(Path(self.filepath).relative_to(Path("/app/data/output")))

    @classmethod
    def from_record(cls, record: DocumentRecord) -> 'Document':
        """Convert DocumentRecord to legacy Document"""
        return cls(
            id=record.id,
            repository_id=record.repository_id,
            repository_name=record.repository_name,
            revision=record.revision,
            date=record.date,
            filename=record.filename,
            filepath=record.filepath,
            size=record.size,
            author=record.author,
            commit_message=record.commit_message,
            changed_paths=record.changed_paths,
            code_review_recommended=record.code_review_recommended,
            code_review_priority=record.code_review_priority,
            documentation_impact=record.documentation_impact,
            risk_level=record.risk_level,
            file_modified_time=record.file_modified_time,
            processed_time=record.processed_time,
            ai_model_used=record.ai_model_used,
            repository_url=record.repository_url,
            repository_type=record.repository_type,
            # User feedback fields
            user_code_review_status=record.user_code_review_status,
            user_code_review_comments=record.user_code_review_comments,
            user_code_review_reviewer=record.user_code_review_reviewer,
            user_code_review_date=record.user_code_review_date,
            user_documentation_rating=record.user_documentation_rating,
            user_documentation_comments=record.user_documentation_comments,
            user_documentation_updated_by=record.user_documentation_updated_by,
            user_documentation_updated_date=record.user_documentation_updated_date,
            user_risk_assessment_override=record.user_risk_assessment_override,
            user_risk_assessment_comments=record.user_risk_assessment_comments,
            user_risk_assessment_updated_by=record.user_risk_assessment_updated_by,
            user_risk_assessment_updated_date=record.user_risk_assessment_updated_date,
            user_documentation_input=record.user_documentation_input,
            user_documentation_suggestions=record.user_documentation_suggestions,
            user_documentation_input_by=record.user_documentation_input_by,
            user_documentation_input_date=record.user_documentation_input_date
        )


class DocumentService:
    """High-performance document service with database storage and caching"""

    def __init__(self, output_dir: str = "/app/data/output", db_path: str = "/app/data/documents.db",
                 ollama_client: Optional[OllamaClient] = None, config_manager=None, unified_processor=None):
        self.output_dir = Path(output_dir)

        # Check configuration for cleanup behavior
        cleanup_orphaned = True
        if config_manager and hasattr(config_manager, 'load_config'):
            try:
                config = config_manager.load_config()
                cleanup_orphaned = getattr(config, 'cleanup_orphaned_documents', True)
            except Exception:
                pass

        self.db = DocumentDatabase(db_path, cleanup_orphaned=cleanup_orphaned)

        # Use provided unified processor or create a new one
        if unified_processor:
            self.unified_processor = unified_processor
        else:
            from unified_document_processor import UnifiedDocumentProcessor
            self.unified_processor = UnifiedDocumentProcessor(
                output_dir=output_dir,
                db_path=db_path,
                ollama_client=ollama_client,
                config_manager=config_manager,
                max_concurrent_tasks=2
            )
            self.unified_processor.start()

        self.ollama_client = ollama_client
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # Enhanced multi-level caching
        self.cache_manager = CacheManager(
            cache_dir=str(Path(output_dir).parent / "cache"),
            memory_ttl=300,  # 5 minutes
            file_ttl=3600,   # 1 hour
            max_memory_items=1000
        )

        # Legacy cache for backward compatibility (will be phased out)
        self._cache: Dict[str, List[DocumentRecord]] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl = 300  # 5 minutes

        # Background processor is already started in __init__ if we created it
        # If we received an existing processor, it should already be started

        # Optional initial scan (can be disabled to reduce startup time)
        skip_scan = False
        if config_manager and hasattr(config_manager, 'load_config'):
            try:
                config = config_manager.load_config()
                skip_scan = getattr(config, 'skip_initial_scan', False)
            except Exception:
                pass

        if not skip_scan:
            self._trigger_background_scan(force_scan=True)
        else:
            self.logger.info("⏭️ Skipping initial document scan (configured to reduce startup time)")

    def __del__(self):
        """Cleanup on destruction"""
        try:
            self.unified_processor.stop()
            self.cache_manager.stop()
        except Exception:
            pass

    def _trigger_background_scan(self, force_scan: bool = True):
        """Trigger background document scanning"""
        try:
            # Only scan if force_scan is True or if this is the first startup
            if force_scan:
                self.unified_processor.scan_file_system(force_rescan=False)
                self.logger.info("🔍 Background document scan initiated")
            else:
                self.logger.debug("Background scan skipped to reduce startup time")
        except Exception as e:
            self.logger.error(f"Error triggering background scan: {e}")

    def scan_documents(self) -> List[Document]:
        """Get documents with high-performance database queries (legacy interface)"""
        records = self.get_documents()
        return [Document.from_record(record) for record in records]

    def get_documents(self, limit: int = 50, offset: int = 0,
                     repository_id: Optional[str] = None,
                     code_review_filter: Optional[bool] = None,
                     doc_impact_filter: Optional[bool] = None,
                     risk_level_filter: Optional[str] = None,
                     author_filter: Optional[str] = None,
                     date_from: Optional[str] = None,
                     date_to: Optional[str] = None,
                     search_query: Optional[str] = None,
                     sort_by: str = 'date',
                     sort_order: str = 'desc') -> List[DocumentRecord]:
        """Get documents with enhanced filtering, sorting, and search capabilities"""
        # Check for cache invalidation signal from historical scanner
        self._check_cache_invalidation_signal()

        # Create cache key with all parameters
        cache_key = f"docs_{limit}_{offset}_{repository_id}_{code_review_filter}_{doc_impact_filter}_{risk_level_filter}_{author_filter}_{date_from}_{date_to}_{search_query}_{sort_by}_{sort_order}"

        # Check enhanced cache first
        cached_result = self.cache_manager.get(cache_key, "documents")
        if cached_result is not None:
            return cached_result

        # Query database with enhanced filtering
        documents = self.db.get_documents(
            limit=limit,
            offset=offset,
            repository_id=repository_id,
            code_review_filter=code_review_filter,
            doc_impact_filter=doc_impact_filter,
            risk_level_filter=risk_level_filter,
            author_filter=author_filter,
            date_from=date_from,
            date_to=date_to,
            search_query=search_query,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # Cache result in enhanced cache
        self.cache_manager.set(cache_key, documents, "documents")

        return documents

    def get_available_authors(self, repository_id: Optional[str] = None) -> List[str]:
        """Get list of available authors for filtering"""
        return self.db.get_available_authors(repository_id)

    def get_available_repositories(self) -> List[dict]:
        """Get list of available repositories with document counts"""
        return self.db.get_available_repositories()

    def get_document_count(self, repository_id: Optional[str] = None,
                          code_review_filter: Optional[bool] = None,
                          doc_impact_filter: Optional[bool] = None,
                          risk_level_filter: Optional[str] = None,
                          author_filter: Optional[str] = None,
                          date_from: Optional[str] = None,
                          date_to: Optional[str] = None,
                          search_query: Optional[str] = None) -> int:
        """Get total document count with enhanced filtering"""
        return self.db.get_document_count(
            repository_id=repository_id,
            code_review_filter=code_review_filter,
            doc_impact_filter=doc_impact_filter,
            risk_level_filter=risk_level_filter,
            author_filter=author_filter,
            date_from=date_from,
            date_to=date_to,
            search_query=search_query
        )

    def get_document_by_id(self, doc_id: str) -> Optional[Document]:
        """Get single document by ID (legacy interface)"""
        record = self.db.get_document_by_id(doc_id)
        return Document.from_record(record) if record else None

    def get_document_record_by_id(self, doc_id: str) -> Optional[DocumentRecord]:
        """Get single document record by ID"""
        return self.db.get_document_by_id(doc_id)

    def delete_document(self, doc_id: str) -> bool:
        """Delete document by ID (both database record and physical file)"""
        # Get document record first to get file path
        document = self.db.get_document_by_id(doc_id)
        if not document:
            return False

        # Delete from database
        db_success = self.db.delete_document(doc_id)

        # Delete physical file
        file_success = True
        try:
            if document.filepath and os.path.exists(document.filepath):
                os.remove(document.filepath)
                self.logger.info(f"Deleted document file: {document.filepath}")
        except Exception as e:
            self.logger.error(f"Error deleting document file {document.filepath}: {e}")
            file_success = False

        if db_success:
            self._invalidate_cache()

        return db_success and file_success

    def force_rescan(self):
        """Force complete rescan of all documents"""
        self.unified_processor.scan_file_system(force_rescan=True)
        self._invalidate_cache()

    def delete_all_documents(self) -> Tuple[int, int]:
        """Delete ALL documents (database records and physical files)
        Returns: (deleted_count, total_count)
        """
        # Stop background processor temporarily to prevent re-scanning
        self.unified_processor.stop()

        try:
            # Get all documents
            all_documents = self.get_documents(limit=1000)
            total_count = len(all_documents)
            deleted_count = 0

            # Delete each document (database + file)
            for doc in all_documents:
                success = self.delete_document(doc.id)
                if success:
                    deleted_count += 1
                    self.logger.info(f"Deleted document: {doc.id}")
                else:
                    self.logger.warning(f"Failed to delete document: {doc.id}")

            # Clear cache
            self._invalidate_cache()

            return deleted_count, total_count

        finally:
            # Restart background processor
            self.unified_processor.start()

    def delete_repository_documents(self, repository_id: str) -> Tuple[int, int]:
        """Delete all documents for a specific repository (database records and physical files)
        Returns: (deleted_count, total_count)
        """
        # Get documents for this repository
        repo_documents = self.get_documents(limit=1000, repository_id=repository_id)
        total_count = len(repo_documents)
        deleted_count = 0

        # Delete each document (database + file)
        for doc in repo_documents:
            success = self.delete_document(doc.id)
            if success:
                deleted_count += 1
                self.logger.info(f"Deleted repository document: {doc.id}")
            else:
                self.logger.warning(f"Failed to delete repository document: {doc.id}")

        # Also try to delete the repository directory if it exists
        try:
            repo_dir = self.output_dir / "repositories" / repository_id
            if repo_dir.exists():
                import shutil
                shutil.rmtree(repo_dir)
                self.logger.info(f"Deleted repository directory: {repo_dir}")
        except Exception as e:
            self.logger.error(f"Error deleting repository directory: {e}")

        # Clear cache
        self._invalidate_cache()

        return deleted_count, total_count

    def clear_all_documents(self) -> int:
        """Clear ALL documents from database (for when files are already deleted)
        Returns: number of documents cleared
        """
        # Get count before clearing
        count = self.db.get_document_count()

        # Clear all documents from database
        success = self.db.clear_all_documents()

        if success:
            # Clear cache
            self._invalidate_cache()
            self.logger.info(f"Cleared {count} documents from database")
            return count
        else:
            self.logger.error("Failed to clear documents from database")
            return 0

    def get_processing_stats(self) -> dict:
        """Get background processing statistics"""
        return self.unified_processor.get_stats()

    def get_cache_stats(self) -> dict:
        """Get cache performance statistics"""
        return self.cache_manager.get_stats()

    def get_migration_status(self) -> dict:
        """Get database migration status"""
        return self.db.get_migration_status()

    def _get_cached(self, key: str) -> Optional[List[DocumentRecord]]:
        """Get cached result if still valid"""
        if key not in self._cache:
            return None

        # Check if cache is expired
        if time.time() - self._cache_timestamps.get(key, 0) > self._cache_ttl:
            del self._cache[key]
            del self._cache_timestamps[key]
            return None

        return self._cache[key]

    def _set_cached(self, key: str, value: List[DocumentRecord]):
        """Cache result with timestamp"""
        self._cache[key] = value
        self._cache_timestamps[key] = time.time()

    def _check_cache_invalidation_signal(self):
        """Check for cache invalidation signal from historical scanner"""
        try:
            signal_file = Path("/app/data/cache_invalidate_signal")
            if signal_file.exists():
                # Signal found - invalidate cache and remove signal
                self._invalidate_cache()
                signal_file.unlink()
                self.logger.debug("Cache invalidated due to external signal")
        except Exception as e:
            self.logger.debug(f"Error checking cache invalidation signal: {e}")

    def _invalidate_cache(self):
        """Clear all cached data"""
        # Clear enhanced cache
        self.cache_manager.invalidate_namespace("documents")

        # Also clear ALL cache as a fallback
        self.cache_manager.clear_all()

        # Clear legacy cache
        self._cache.clear()
        self._cache_timestamps.clear()

    # Legacy methods removed - now handled by DocumentProcessor and DocumentDatabase
    # All document parsing and scanning is done in the background

    def get_repository_stats(self) -> Dict[str, int]:
        """Get document count statistics by repository"""
        stats = {}
        documents = self.get_documents(limit=1000)  # Get a large sample

        for doc in documents:
            if doc.repository_id not in stats:
                stats[doc.repository_id] = 0
            stats[doc.repository_id] += 1

        return stats
    
    def get_document_content(self, doc_id: str) -> Optional[str]:
        """Get the content of a specific document"""
        document = self.get_document_record_by_id(doc_id)
        if not document:
            return None

        try:
            with open(document.filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"Error reading document content {document.filepath}: {e}")
            return None

    # REMOVED: _extract_code_review_recommendation - now handled by UnifiedDocumentProcessor

    # REMOVED: _extract_code_review_priority - now handled by UnifiedDocumentProcessor

    # REMOVED: _extract_documentation_impact - now handled by UnifiedDocumentProcessor

    # REMOVED: _extract_risk_level - now handled by UnifiedDocumentProcessor

    def _extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            # Look for markdown section headers
            pattern = rf'##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)'
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None

    # REMOVED: _extract_metadata_with_llm - now handled by UnifiedDocumentProcessor
    def _removed_extract_metadata_with_llm(self, documentation: str, document_record: Optional[DocumentRecord] = None) -> tuple[Dict[str, Any], Optional[str]]:
        """Extract metadata using LLM with enhanced contextual prompts

        Returns:
            tuple: (metadata_dict, model_used)
        """
        if not self.ollama_client:
            self.logger.warning("No Ollama client available for LLM metadata extraction")
            return {}, None

        try:
            # Use enhanced prompts if enabled and we have document context
            # Prefer OllamaClient's config (which has the latest specialized models) over loading fresh config
            config = None
            if self.ollama_client and hasattr(self.ollama_client, 'config'):
                config = self.ollama_client.config
            elif self.config_manager:
                config = self.config_manager.load_config()

            use_enhanced = config and getattr(config, 'use_enhanced_prompts', True)

            if document_record and use_enhanced:
                try:
                    from prompt_templates import PromptTemplateManager, ContextAnalyzer

                    if not config:
                        raise Exception("No config available")

                    # Initialize enhanced prompt system
                    prompt_manager = PromptTemplateManager(config)
                    context_analyzer = ContextAnalyzer(config)

                    # Analyze the change context
                    context = context_analyzer.analyze_change_context(
                        document=document_record,
                        commit_message=getattr(document_record, 'commit_message', None)
                    )

                    # Get enhanced prompts
                    system_prompt, user_prompt = prompt_manager.get_metadata_extraction_prompt(
                        documentation, context
                    )

                    self.logger.debug(f"Using enhanced prompts for document {document_record.id}")

                except Exception as e:
                    self.logger.warning(f"Failed to use enhanced prompts, falling back to basic: {e}")
                    # Fall back to basic prompts
                    system_prompt, user_prompt = self._get_basic_metadata_prompts(documentation)
            else:
                # Use basic prompts when no document context available
                system_prompt, user_prompt = self._get_basic_metadata_prompts(documentation)

            # Use specialized model based on analysis type
            specialized_model = None
            if config:
                # For metadata extraction that includes risk assessment, prefer risk assessment model
                # but fall back to code review model if risk model not available
                risk_model = getattr(config, 'ollama_model_risk_assessment', None)
                code_review_model = getattr(config, 'ollama_model_code_review', None)
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(f"🎯 Selected specialized model for metadata extraction: {specialized_model}")
                else:
                    self.logger.debug(f"No specialized model configured, using default model")
                self.logger.debug(f"Model selection details: risk_model={risk_model}, code_review_model={code_review_model}, selected={specialized_model}")

            response = self.ollama_client.call_ollama(user_prompt, system_prompt, model=specialized_model)

            # Determine the actual model used (specialized model or default)
            actual_model_used = specialized_model or (config.ollama_model if config else None)

            self.logger.debug(f"Raw LLM response: '{response}' (type: {type(response)})")

            if response and response.strip():
                # Try to parse JSON response
                import json
                try:
                    # Extract JSON from response (LLM might wrap it in explanatory text)
                    json_str = response.strip()

                    # Look for JSON object in the response
                    start_idx = json_str.find('{')
                    end_idx = json_str.rfind('}')

                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_str = json_str[start_idx:end_idx+1]

                    metadata = json.loads(json_str)

                    # Validate and normalize the response
                    normalized = {}

                    # Code review recommendation
                    if 'code_review_recommended' in metadata:
                        val = metadata['code_review_recommended']
                        if isinstance(val, bool):
                            normalized['code_review_recommended'] = val
                        elif isinstance(val, str):
                            normalized['code_review_recommended'] = val.lower() in ['true', 'yes', '1']

                    # Code review priority
                    if 'code_review_priority' in metadata:
                        val = metadata['code_review_priority']
                        if val and isinstance(val, str) and val.upper() in ['HIGH', 'MEDIUM', 'LOW']:
                            normalized['code_review_priority'] = val.upper()

                    # Risk level
                    if 'risk_level' in metadata:
                        val = metadata['risk_level']
                        if val and isinstance(val, str) and val.upper() in ['HIGH', 'MEDIUM', 'LOW']:
                            normalized['risk_level'] = val.upper()

                    # Documentation impact
                    if 'documentation_impact' in metadata:
                        val = metadata['documentation_impact']
                        if isinstance(val, bool):
                            normalized['documentation_impact'] = val
                        elif isinstance(val, str):
                            normalized['documentation_impact'] = val.lower() in ['true', 'yes', '1']

                    self.logger.debug(f"LLM extracted metadata: {normalized}")
                    return normalized, actual_model_used

                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse LLM JSON response: {e}")
                    self.logger.warning(f"Raw LLM response: '{response}'")
            else:
                self.logger.warning(f"Empty or None LLM response: '{response}'")

            return {}, actual_model_used

        except Exception as e:
            self.logger.error(f"Error extracting metadata with LLM: {e}")
            return {}, None

    def _get_basic_metadata_prompts(self, documentation: str) -> Tuple[str, str]:
        """Get basic metadata extraction prompts (fallback when enhanced prompts fail)"""

        system_prompt = """You are a metadata extraction assistant. Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- risk_level: "HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- documentation_impact: true/false (whether documentation needs updates)

Be precise and consistent. If you cannot determine a value, use null."""

        user_prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

        return system_prompt, user_prompt

    def update_code_review_feedback(self, doc_id: str, status: str, comments: Optional[str] = None, reviewer: Optional[str] = None) -> bool:
        """Update code review feedback for a document"""
        try:
            document_record = self.db.get_document_by_id(doc_id)
            if not document_record:
                return False

            # Update user feedback fields
            document_record.user_code_review_status = status
            document_record.user_code_review_comments = comments
            document_record.user_code_review_reviewer = reviewer
            document_record.user_code_review_date = datetime.now()

            return self.db.upsert_document(document_record)
        except Exception as e:
            self.logger.error(f"Error updating code review feedback for {doc_id}: {e}")
            return False

    def update_documentation_feedback(self, doc_id: str, rating: Optional[int] = None, comments: Optional[str] = None, updated_by: Optional[str] = None) -> bool:
        """Update documentation quality feedback for a document"""
        try:
            document_record = self.db.get_document_by_id(doc_id)
            if not document_record:
                return False

            # Update user feedback fields
            if rating is not None:
                document_record.user_documentation_rating = rating
            if comments is not None:
                document_record.user_documentation_comments = comments
            if updated_by is not None:
                document_record.user_documentation_updated_by = updated_by
            document_record.user_documentation_updated_date = datetime.now()

            return self.db.upsert_document(document_record)
        except Exception as e:
            self.logger.error(f"Error updating documentation feedback for {doc_id}: {e}")
            return False

    def update_risk_assessment_override(self, doc_id: str, risk_override: str, comments: Optional[str] = None, updated_by: Optional[str] = None) -> bool:
        """Update risk assessment override for a document"""
        try:
            document_record = self.db.get_document_by_id(doc_id)
            if not document_record:
                return False

            # Update user feedback fields
            document_record.user_risk_assessment_override = risk_override
            document_record.user_risk_assessment_comments = comments
            document_record.user_risk_assessment_updated_by = updated_by
            document_record.user_risk_assessment_updated_date = datetime.now()

            return self.db.upsert_document(document_record)
        except Exception as e:
            self.logger.error(f"Error updating risk assessment override for {doc_id}: {e}")
            return False

    def update_documentation_input(self, doc_id: str, documentation_input: Optional[str] = None,
                                 suggestions: Optional[str] = None, input_by: Optional[str] = None) -> bool:
        """Update user documentation input/augmentation for a document"""
        try:
            document_record = self.db.get_document_by_id(doc_id)
            if not document_record:
                return False

            # Update user documentation input fields
            document_record.user_documentation_input = documentation_input
            document_record.user_documentation_suggestions = suggestions
            document_record.user_documentation_input_by = input_by
            document_record.user_documentation_input_date = datetime.now()

            return self.db.upsert_document(document_record)
        except Exception as e:
            self.logger.error(f"Error updating documentation input for {doc_id}: {e}")
            return False

    def get_ai_documentation_suggestions(self, doc_id: str) -> Optional[str]:
        """Generate specialized product documentation suggestions using AI analysis"""
        try:
            # Get the document record for context
            document = self.get_document_record_by_id(doc_id)
            if not document:
                return None

            # Get the technical analysis content
            content = self.get_document_content(doc_id)
            if not content:
                return None

            # If we have an Ollama client, generate specialized product documentation suggestions
            if self.ollama_client:
                return self._generate_product_documentation_suggestions(document, content)

            # Fallback: extract user-facing impact from existing analysis
            return self._extract_product_impact_fallback(content)

        except Exception as e:
            self.logger.error(f"Error generating AI documentation suggestions for {doc_id}: {e}")
            return None

    def _generate_product_documentation_suggestions(self, document: DocumentRecord, technical_content: str) -> Optional[str]:
        """Generate product documentation suggestions using specialized AI analysis"""
        try:
            # Try to find existing product documentation for context
            existing_docs = self._find_product_documentation(document)

            system_prompt = """You are an expert product documentation specialist and technical writer. Generate clean, well-formatted markdown content for product documentation.

CORE RESPONSIBILITIES:
1. Analyze technical changes for user-facing impact
2. Generate clean, readable markdown content
3. Provide specific, actionable documentation updates
4. Create ready-to-use content that requires minimal editing

MARKDOWN FORMATTING REQUIREMENTS:
- Use proper markdown headers (##, ###, ####)
- Format code blocks with ```language syntax
- Use bullet points (-) and numbered lists (1.)
- Bold important terms with **text**
- Use inline code with `backticks` for parameters
- Keep paragraphs concise and well-spaced
- Include practical examples in code blocks

CONTENT FOCUS:
- New features or capabilities users can access
- Changes to user interfaces, workflows, or user experience
- Configuration options and settings users control
- API changes that affect integrations
- Breaking changes requiring user action
- Performance improvements users will notice

WHEN EXISTING DOCUMENTATION IS PROVIDED:
- Study the current structure and style
- Match the established tone and formatting
- Reference specific sections that need updates
- Suggest content that fits the existing structure

OUTPUT STANDARDS:
- Write in clear, user-friendly language
- Focus on practical benefits, not technical implementation
- Provide step-by-step instructions with exact commands
- Include specific parameter names and values
- Use consistent markdown formatting throughout
- Ensure content is immediately copy-pasteable"""

            prompt_parts = [
                f"""Analyze this technical change and generate product documentation content:

**Change Details:**
- Repository: {document.repository_name}
- Revision: {document.revision}
- Author: {document.author}
- Commit Message: {document.commit_message}
- Files Changed: {', '.join(document.changed_paths) if document.changed_paths else 'Not specified'}"""
            ]

            if existing_docs:
                prompt_parts.append(f"\n{existing_docs}")

            prompt_parts.append(f"""
**Technical Analysis:**
{technical_content}

Generate clean, well-formatted markdown content for product documentation. Focus on user-facing changes and provide ready-to-use content.

If this change has user-facing impact, provide clean markdown in this format:

## What's New

Describe new features or capabilities users can access. Be specific about functionality and benefits.

## User Impact

Explain what users will notice or experience differently. Include practical examples where helpful.

## Action Required

List any steps users need to take:
- Configuration changes with exact parameter names
- Workflow modifications with clear instructions
- Migration steps for breaking changes
- Write "No action required" if none needed

## Documentation Updates

For each file that needs updates:

### README.md
- **Section**: [specific section name]
- **Change**: [what to add/update]
- **Priority**: High/Medium/Low

### Other Files
- **Section**: [specific section name]
- **Change**: [what to add/update]
- **Priority**: High/Medium/Low

## Suggested Content

Provide actual markdown content that can be copied directly:

```markdown
### New Feature Name

Description of the feature and how to use it.

#### Usage Example:
```bash
command example here
```

Benefits and additional notes.
```

If NO user-facing impact, respond with:
"No user-facing changes - this is an internal technical improvement."

Use clean markdown formatting with proper headers, lists, and code blocks. Keep content concise and actionable.""")

            prompt = "".join(prompt_parts)

            if self.ollama_client:
                # Use specialized documentation model if configured
                doc_model = getattr(self.ollama_client.config, 'ollama_model_documentation', None)
                if doc_model:
                    # Get context info for logging
                    context_limit = self._get_model_context_limit()
                    prompt_tokens = self._estimate_token_count(prompt + system_prompt)
                    self.logger.info(f"🎯 Using specialized documentation model: {doc_model}")
                    self.logger.info(f"📊 Context usage: {prompt_tokens:,} tokens (limit: {context_limit:,})")
                else:
                    self.logger.debug("No specialized documentation model configured, using default")
                response = self.ollama_client.call_ollama(prompt, system_prompt, model=doc_model)

                if response and len(response.strip()) > 50:  # Ensure we got a meaningful response
                    return response.strip()

            return None

        except Exception as e:
            self.logger.error(f"Error generating product documentation with AI: {e}")
            return None

    def _extract_product_impact_fallback(self, content: str) -> Optional[str]:
        """Fallback method to extract user-facing impact from technical analysis"""
        try:
            suggestions = []

            # Look for user-facing impact indicators
            impact_section = self._extract_section(content, "Impact Assessment")
            if impact_section:
                # Filter for user-facing content
                impact_lower = impact_section.lower()
                if any(keyword in impact_lower for keyword in ['user', 'interface', 'feature', 'workflow', 'experience', 'api', 'configuration']):
                    suggestions.append(f"**User Impact:**\n{impact_section}")

            # Look for documentation impact
            doc_impact = self._extract_section(content, "Documentation Impact")
            if doc_impact and 'yes' in doc_impact.lower():
                suggestions.append(f"**Documentation Updates Needed:**\n{doc_impact}")

            # Look for recommendations that affect users
            recommendations = self._extract_section(content, "Recommendations")
            if recommendations:
                rec_lower = recommendations.lower()
                if any(keyword in rec_lower for keyword in ['user', 'documentation', 'guide', 'update', 'configure']):
                    suggestions.append(f"**Recommendations:**\n{recommendations}")

            if suggestions:
                return "\n\n".join(suggestions)

            # Final fallback: provide a template
            return """**Product Documentation Update Needed**

This change may impact user-facing functionality. Please review the technical changes and update relevant user documentation sections such as:

- User guides and tutorials
- API documentation
- Configuration instructions
- Feature descriptions
- Release notes

Consider whether users need to be notified of any changes to their workflows or integrations."""

        except Exception as e:
            self.logger.error(f"Error in product impact fallback: {e}")
            return None

    def _find_product_documentation(self, document: DocumentRecord) -> Optional[str]:
        """Find and read relevant product documentation files"""
        try:
            # Get repository-specific product documentation files if configured
            doc_patterns = self._get_product_documentation_patterns(document.repository_id)

            if not doc_patterns:
                # Fallback to common product documentation file patterns
                doc_patterns = [
                    'README.md', 'readme.md', 'README.rst',
                    'CHANGELOG.md', 'changelog.md', 'CHANGES.md',
                    'USER_GUIDE.md', 'user-guide.md', 'user_guide.md',
                    'API.md', 'api.md', 'API_REFERENCE.md',
                    'FEATURES.md', 'features.md',
                    'DOCUMENTATION.md', 'documentation.md',
                    'docs/README.md', 'docs/index.md', 'docs/user-guide.md',
                    'docs/api.md', 'docs/features.md'
                ]

            # Get repository root from document filepath
            doc_path = Path(document.filepath)
            repo_root = None

            # Try to find repository root by looking for common indicators
            current_path = doc_path.parent
            while current_path != current_path.parent:
                # Look for common repository indicators
                if any((current_path / indicator).exists() for indicator in ['.git', '.svn', 'package.json', 'requirements.txt', 'Cargo.toml', 'pom.xml']):
                    repo_root = current_path
                    break
                current_path = current_path.parent

            if not repo_root:
                # Fallback: assume repository root is a few levels up from the document
                repo_root = doc_path.parent.parent.parent

            # Search for product documentation files
            found_docs = []
            for pattern in doc_patterns:
                doc_file = repo_root / pattern
                if doc_file.exists() and doc_file.is_file():
                    try:
                        content = self._read_document_file(doc_file)
                        if content and len(content.strip()) > 20:  # Include files with minimal content
                            # Use intelligent content extraction for large documents
                            relevant_content = self._extract_relevant_content(content, document)
                            found_docs.append({
                                'file': str(doc_file.relative_to(repo_root)),
                                'content': relevant_content
                            })
                            if len(found_docs) >= 3:  # Limit to 3 files to avoid token limits
                                break
                    except Exception as e:
                        self.logger.debug(f"Could not read {doc_file}: {e}")
                        continue

            if found_docs:
                # Format the documentation for AI analysis
                doc_content = "## Existing Product Documentation\n\n"
                for doc in found_docs:
                    doc_content += f"### {doc['file']}\n```\n{doc['content']}\n```\n\n"
                return doc_content

            return None

        except Exception as e:
            self.logger.error(f"Error finding product documentation: {e}")
            return None

    def _get_product_documentation_patterns(self, repository_id: str) -> List[str]:
        """Get repository-specific product documentation file patterns"""
        try:
            # Get repository configuration from config manager if available
            if hasattr(self, 'config_manager') and self.config_manager:
                config = self.config_manager.load_config()
                repo_config = config.get_repository_by_id(repository_id)
                if repo_config and repo_config.product_documentation_files:
                    self.logger.debug(f"Using repository-specific product documentation files for {repository_id}: {repo_config.product_documentation_files}")
                    return repo_config.product_documentation_files

            # If no config manager or no specific files configured, return empty list to use fallback
            return []

        except Exception as e:
            self.logger.debug(f"Error getting product documentation patterns for {repository_id}: {e}")
            return []

    def _read_document_file(self, file_path: Path) -> Optional[str]:
        """Read content from various document file formats"""
        try:
            file_ext = file_path.suffix.lower()

            # Handle text-based files
            if file_ext in ['.md', '.txt', '.rst', '.html', '.htm']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()

            # Handle Microsoft Word documents
            elif file_ext in ['.doc', '.docx']:
                return self._read_word_document(file_path)

            # Handle RTF files
            elif file_ext == '.rtf':
                return self._read_rtf_document(file_path)

            # Handle OpenDocument Text
            elif file_ext == '.odt':
                return self._read_odt_document(file_path)

            # For other file types, try reading as text with error handling
            else:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return f.read()
                except UnicodeDecodeError:
                    # Try with different encoding
                    with open(file_path, 'r', encoding='latin-1') as f:
                        return f.read()

        except Exception as e:
            self.logger.debug(f"Error reading document file {file_path}: {e}")
            return None

    def _read_word_document(self, file_path: Path) -> Optional[str]:
        """Extract text content from Microsoft Word documents"""
        try:
            # Try using python-docx for .docx files
            if file_path.suffix.lower() == '.docx':
                try:
                    import docx
                    doc = docx.Document(str(file_path))
                    paragraphs = []
                    for paragraph in doc.paragraphs:
                        if paragraph.text.strip():
                            paragraphs.append(paragraph.text.strip())
                    return '\n\n'.join(paragraphs)
                except ImportError:
                    self.logger.debug("python-docx not available for .docx files")
                except Exception as e:
                    self.logger.debug(f"Error reading .docx file with python-docx: {e}")

            # Try using textract as fallback for both .doc and .docx
            try:
                import textract  # type: ignore
                content = textract.process(str(file_path))
                return content.decode('utf-8')
            except ImportError:
                self.logger.debug("textract not available for Word document extraction")
            except Exception as e:
                self.logger.debug(f"Error reading Word file with textract: {e}")

            # Try using python-docx2txt as another fallback for .docx
            if file_path.suffix.lower() == '.docx':
                try:
                    import docx2txt  # type: ignore
                    return docx2txt.process(str(file_path))
                except ImportError:
                    self.logger.debug("docx2txt not available")
                except Exception as e:
                    self.logger.debug(f"Error reading .docx file with docx2txt: {e}")

            # If all else fails, indicate that the file exists but content couldn't be extracted
            self.logger.warning(f"Could not extract content from Word document {file_path.name}. Install python-docx, textract, or docx2txt for Word document support.")
            return f"[Microsoft Word Document: {file_path.name} - Content extraction requires python-docx, textract, or docx2txt library. Install with: pip install python-docx]"

        except Exception as e:
            self.logger.debug(f"Error reading Word document {file_path}: {e}")
            return None

    def _read_rtf_document(self, file_path: Path) -> Optional[str]:
        """Extract text content from RTF documents"""
        try:
            # Try using striprtf
            try:
                from striprtf.striprtf import rtf_to_text  # type: ignore
                with open(file_path, 'r', encoding='utf-8') as f:
                    rtf_content = f.read()
                return rtf_to_text(rtf_content)
            except ImportError:
                self.logger.debug("striprtf not available for RTF files")
            except Exception as e:
                self.logger.debug(f"Error reading RTF file: {e}")

            # Fallback: indicate file exists but needs library
            return f"[RTF Document: {file_path.name} - Content extraction requires striprtf library]"

        except Exception as e:
            self.logger.debug(f"Error reading RTF document {file_path}: {e}")
            return None

    def _read_odt_document(self, file_path: Path) -> Optional[str]:
        """Extract text content from OpenDocument Text files"""
        try:
            # Try using odfpy
            try:
                from odf import text, teletype  # type: ignore
                from odf.opendocument import load  # type: ignore

                doc = load(str(file_path))
                paragraphs = []
                for paragraph in doc.getElementsByType(text.P):
                    content = teletype.extractText(paragraph)
                    if content.strip():
                        paragraphs.append(content.strip())
                return '\n\n'.join(paragraphs)
            except ImportError:
                self.logger.debug("odfpy not available for ODT files")
            except Exception as e:
                self.logger.debug(f"Error reading ODT file: {e}")

            # Fallback: indicate file exists but needs library
            return f"[OpenDocument Text: {file_path.name} - Content extraction requires odfpy library]"

        except Exception as e:
            self.logger.debug(f"Error reading ODT document {file_path}: {e}")
            return None

    def _extract_relevant_content(self, content: str, document: DocumentRecord) -> str:
        """Extract relevant content from large documents based on commit context"""
        try:
            # Get the usable context limit for document content (already accounts for system overhead)
            max_doc_tokens = self._get_model_context_limit()

            # If content is small enough, return as-is
            estimated_tokens = self._estimate_token_count(content)
            if estimated_tokens <= max_doc_tokens:
                self.logger.debug(f"Document content fits within limit ({estimated_tokens} tokens)")
                return content

            self.logger.debug(f"Document content is large ({estimated_tokens} tokens > {max_doc_tokens} limit), extracting relevant sections")

            # Get keywords from the commit/document context
            keywords = self._extract_context_keywords(document)

            # Split content into sections
            sections = self._split_into_sections(content)

            # Score and rank sections by relevance
            scored_sections = self._score_sections_by_relevance(sections, keywords)

            # Select top sections that fit within token limit
            selected_content = self._select_top_sections(scored_sections, max_doc_tokens)

            # If very little content was selected, be more inclusive
            if self._estimate_token_count(selected_content) < max_doc_tokens // 4:
                self.logger.debug("Very little content selected, being more inclusive")
                selected_content = self._select_top_sections_inclusive(scored_sections, max_doc_tokens)

            # Add summary header explaining the selection
            final_content = self._create_content_summary(content, selected_content, len(sections), len([s for s in scored_sections if s['relevance_score'] > 0]))

            return final_content

        except Exception as e:
            self.logger.error(f"Error extracting relevant content: {e}")
            # Fallback: return truncated content
            fallback_limit = self._get_model_context_limit()
            return content[:fallback_limit * 4]

    def _extract_context_keywords(self, document: DocumentRecord) -> List[str]:
        """Extract keywords from document context to guide content selection"""
        keywords = []

        try:
            # Extract keywords from commit message
            if document.commit_message:
                commit_words = document.commit_message.lower().split()
                keywords.extend([word.strip('.,!?;:') for word in commit_words if len(word) > 3])

            # Extract keywords from filename
            if document.filename:
                filename_parts = document.filename.lower().replace('.', ' ').replace('_', ' ').replace('-', ' ').split()
                keywords.extend([part for part in filename_parts if len(part) > 2])

            # Extract keywords from changed file paths
            if document.changed_paths:
                for path in document.changed_paths:
                    path_parts = path.lower().replace('/', ' ').replace('.', ' ').replace('_', ' ').replace('-', ' ').split()
                    keywords.extend([part for part in path_parts if len(part) > 2])

            # Look for technical terms in commit message
            if document.commit_message:
                import re

                # Extract words that might be important (capitalized words, technical terms)
                tech_terms = re.findall(r'\b[A-Z][a-z]+(?:[A-Z][a-z]*)*\b', document.commit_message)
                keywords.extend([term.lower() for term in tech_terms])

                # Extract quoted terms (often feature names or API endpoints)
                quoted_terms = re.findall(r'["\']([^"\']+)["\']', document.commit_message)
                keywords.extend([term.lower() for term in quoted_terms if len(term) > 2])

                # Extract API-like terms
                api_terms = re.findall(r'\b(?:api|endpoint|route|method|function|class|service)\b', document.commit_message.lower())
                keywords.extend(api_terms)

            # Add common documentation section keywords
            doc_keywords = [
                'authentication', 'auth', 'login', 'api', 'endpoint', 'configuration',
                'setup', 'installation', 'getting started', 'tutorial', 'guide',
                'features', 'changelog', 'release', 'update', 'migration',
                'troubleshooting', 'faq', 'reference', 'documentation'
            ]
            keywords.extend(doc_keywords)

            # Remove duplicates and very short words
            keywords = list(set([kw for kw in keywords if len(kw) > 2]))

            self.logger.debug(f"Extracted {len(keywords)} context keywords: {keywords[:10]}...")
            return keywords

        except Exception as e:
            self.logger.debug(f"Error extracting context keywords: {e}")
            return ['api', 'authentication', 'configuration', 'guide', 'documentation']

    def _split_into_sections(self, content: str) -> List[Dict[str, Any]]:
        """Split document content into logical sections"""
        sections = []

        try:
            import re
            lines = content.split('\n')
            current_section: Dict[str, Any] = {'title': '', 'content': '', 'start_line': 0}

            for i, line in enumerate(lines):
                line_stripped = line.strip()

                # Detect section headers (various formats)
                is_header = (
                    # Markdown headers
                    line_stripped.startswith('#') or
                    # Underlined headers
                    (i + 1 < len(lines) and lines[i + 1].strip() and
                     all(c in '=-~^' for c in lines[i + 1].strip())) or
                    # All caps headers
                    (line_stripped.isupper() and len(line_stripped) > 3 and len(line_stripped) < 50) or
                    # Numbered sections
                    re.match(r'^\d+\.?\s+[A-Z]', line_stripped)
                )

                current_content = str(current_section['content'])
                if is_header and len(current_content.strip()) > 0:
                    # Save previous section
                    sections.append(current_section.copy())
                    # Start new section
                    current_section = {
                        'title': line_stripped,
                        'content': line + '\n',
                        'start_line': i
                    }
                else:
                    current_section['content'] = current_content + line + '\n'
                    if not current_section['title'] and line_stripped:
                        # Use first non-empty line as title if no header found
                        current_section['title'] = line_stripped[:50]

            # Add the last section
            final_content = str(current_section['content'])
            if len(final_content.strip()) > 0:
                sections.append(current_section)

            self.logger.debug(f"Split document into {len(sections)} sections")
            return sections

        except Exception as e:
            self.logger.error(f"Error splitting content into sections: {e}")
            # Fallback: treat entire content as one section
            return [{'title': 'Document Content', 'content': content, 'start_line': 0}]

    def _score_sections_by_relevance(self, sections: List[Dict[str, Any]], keywords: List[str]) -> List[Dict[str, Any]]:
        """Score sections by relevance to the context keywords"""
        try:
            for section in sections:
                score = 0
                content_lower = section['content'].lower()
                title_lower = section['title'].lower()

                # Score based on keyword matches
                for keyword in keywords:
                    # Title matches are more important
                    if keyword in title_lower:
                        score += 10
                    # Content matches
                    if keyword in content_lower:
                        score += content_lower.count(keyword)

                # Boost score for important section types
                important_sections = [
                    'api', 'authentication', 'auth', 'configuration', 'config',
                    'getting started', 'installation', 'setup', 'features',
                    'changelog', 'release notes', 'migration', 'upgrade'
                ]

                for important in important_sections:
                    if important in title_lower:
                        score += 20

                # Boost score for sections with code examples or technical content
                if any(indicator in content_lower for indicator in ['```', 'http://', 'https://', 'api/', 'endpoint']):
                    score += 5

                section['relevance_score'] = score

            # Sort by relevance score (highest first)
            sections.sort(key=lambda x: x['relevance_score'], reverse=True)

            self.logger.debug(f"Section relevance scores: {[(s['title'][:30], s['relevance_score']) for s in sections[:5]]}")
            return sections

        except Exception as e:
            self.logger.error(f"Error scoring sections: {e}")
            return sections

    def _select_top_sections(self, scored_sections: List[Dict[str, Any]], max_tokens: int) -> str:
        """Select top-scoring sections that fit within token limit"""
        try:
            selected_content = []
            total_chars = 0
            max_chars = max_tokens * 4  # Rough estimate: 4 chars per token

            # Always include a document overview if available
            overview_added = False
            for section in scored_sections:
                title_lower = section['title'].lower()
                if any(overview_term in title_lower for overview_term in ['overview', 'introduction', 'summary', 'abstract']):
                    if total_chars + len(section['content']) < max_chars * 0.3:  # Use max 30% for overview
                        selected_content.append(f"## {section['title']}\n{section['content']}")
                        total_chars += len(section['content'])
                        overview_added = True
                        break

            # Add highest-scoring sections
            for section in scored_sections:
                if total_chars + len(section['content']) > max_chars:
                    break

                # Skip if already added as overview
                if overview_added and any(overview_term in section['title'].lower()
                                        for overview_term in ['overview', 'introduction', 'summary', 'abstract']):
                    continue

                selected_content.append(f"## {section['title']}\n{section['content']}")
                total_chars += len(section['content'])

            # If no sections were selected (all too large), take the first section and truncate
            if not selected_content and scored_sections:
                first_section = scored_sections[0]
                truncated_content = first_section['content'][:max_chars - 100]
                selected_content.append(f"## {first_section['title']}\n{truncated_content}\n\n[Content truncated - document is very large]")

            result = '\n\n'.join(selected_content)

            self.logger.debug(f"Selected {len(selected_content)} sections, total chars: {len(result)}")
            return result

        except Exception as e:
            self.logger.error(f"Error selecting top sections: {e}")
            # Fallback: return truncated original content
            return scored_sections[0]['content'][:max_tokens * 4] if scored_sections else ""

    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count for text (rough approximation)"""
        # Rough estimation: 1 token ≈ 4 characters for English text
        # This is conservative to ensure we don't exceed limits
        return len(text) // 4

    def _get_model_context_limit(self) -> int:
        """Get the usable context limit for document processing

        Returns the maximum tokens available for document content, accounting for:
        - System prompts and instructions (~500-1000 tokens)
        - Response generation space (~1000-2000 tokens)
        - Safety buffer for tokenization variance (~5-10%)

        This is the limit that calling code can use directly without further reduction.
        """
        try:
            if self.ollama_client and hasattr(self.ollama_client, 'config'):
                model_name = getattr(self.ollama_client.config, 'ollama_model', 'qwen3')

                # First try to get actual context limit from Ollama API
                raw_limit = self._get_dynamic_context_limit(model_name)
                if raw_limit:
                    usable_limit = self._calculate_usable_context(raw_limit)
                    self.logger.debug(f"Using dynamic usable context {usable_limit} (raw: {raw_limit}) for model {model_name}")
                    return usable_limit

                # Get consolidated raw context limits
                model_raw_limits = self._get_model_raw_limits_dict()

                # Get consolidated model specialties for logging
                model_specialties = self._get_model_specialties_dict()

                # Find matching model (partial match) and calculate usable context
                for model_key, raw_limit in model_raw_limits.items():
                    if model_key in model_name.lower():
                        usable_limit = self._calculate_usable_context(raw_limit)
                        specialties = model_specialties.get(model_key, ['general'])
                        self.logger.debug(f"Using static usable context limit {usable_limit} (raw: {raw_limit}) for model {model_name}")
                        self.logger.info(f"Model {model_name} specialties: {', '.join(specialties)}")
                        return usable_limit

                self.logger.warning(f"Unknown model {model_name}, using default usable context limit")
                return 2500  # Safe default

            return 2500  # Safe default when no client available

        except Exception as e:
            self.logger.error(f"Error getting model context limit: {e}")
            return 2500  # Safe fallback

    def _calculate_usable_context(self, raw_limit: int) -> int:
        """Calculate maximum usable context for document content from raw model limit

        Reserves appropriate space for system components while maximizing document capacity:
        - System prompts and instructions: ~500-1000 tokens
        - Response generation: ~1000-2000 tokens
        - Safety buffer: ~5-10% for tokenization variance

        Returns the maximum tokens that can be used directly for document content.
        """

        if raw_limit <= 4000:
            # Small models: Reserve ~1500 tokens (system + response + buffer)
            # Usable: ~2500 tokens (62% of capacity)
            return max(1000, raw_limit - 1500)
        elif raw_limit <= 16000:
            # Medium models: Reserve ~2500 tokens
            # Usable: ~5500-13500 tokens (69-84% of capacity)
            return max(2000, raw_limit - 2500)
        elif raw_limit <= 32000:
            # Large models: Reserve ~3500 tokens
            # Usable: ~12500-28500 tokens (78-89% of capacity)
            return max(4000, raw_limit - 3500)
        else:
            # Very large models: Reserve ~5000 tokens
            # Usable: ~123k+ tokens (85-95% of capacity)
            return max(8000, raw_limit - 5000)

        # This approach maximizes usable context while ensuring safety:
        # 1. Fixed overhead for system prompts and response generation
        # 2. Scales overhead appropriately with model size
        # 3. Provides 62-95% context utilization (much better than previous 37-45%)
        # 4. Calling code can use returned value directly without further reduction

    def _get_dynamic_context_limit(self, model_name: str) -> Optional[int]:
        """Get context limit directly from Ollama API using /api/show endpoint"""
        try:
            if not self.ollama_client:
                return None

            url = f"{self.ollama_client.config.ollama_host}/api/show"
            payload = {"name": model_name}

            self.logger.debug(f"Querying Ollama for model info: {model_name}")
            timeout = getattr(self.ollama_client.config, 'ollama_timeout_connection', 30)
            response = requests.post(url, json=payload, timeout=timeout)
            response.raise_for_status()

            model_info = response.json()

            # Look for context length in the response
            # The API returns model details including context length
            if 'details' in model_info:
                details = model_info['details']

                # Check various possible field names
                context_fields = ['context_length', 'context_size', 'max_context_length', 'num_ctx']
                for field in context_fields:
                    if field in details and details[field]:
                        context_limit = int(details[field])
                        self.logger.info(f"Found dynamic context limit {context_limit} for model {model_name}")
                        return context_limit

            # Also check in model parameters if available
            if 'parameters' in model_info:
                params = model_info['parameters']
                if 'num_ctx' in params:
                    context_limit = int(params['num_ctx'])
                    self.logger.info(f"Found context limit in parameters: {context_limit} for model {model_name}")
                    return context_limit

            # Check in modelfile content for PARAMETER num_ctx
            if 'modelfile' in model_info:
                modelfile = model_info['modelfile']
                import re
                ctx_match = re.search(r'PARAMETER\s+num_ctx\s+(\d+)', modelfile, re.IGNORECASE)
                if ctx_match:
                    context_limit = int(ctx_match.group(1))
                    self.logger.info(f"Found context limit in modelfile: {context_limit} for model {model_name}")
                    return context_limit

            self.logger.debug(f"No context limit found in API response for model {model_name}")
            return None

        except Exception as e:
            self.logger.debug(f"Failed to get dynamic context limit for {model_name}: {e}")
            return None

    def _get_model_raw_limits_dict(self) -> Dict[str, int]:
        """Get the consolidated model raw context limits dictionary

        Returns:
            Dictionary mapping model names to their raw context limits in tokens
        """
        return {
            # === Meta Llama Family ===
            'llama2': 4096,      # 4K context window
            'llama3': 8192,      # 8K context window
            'llama3.1': 128000,  # 128K context window
            'llama3.2': 131072,  # 131K context window
            'llama3.3': 128000,  # 128K context window (latest)
            'codellama': 16384,  # 16K context window

            # === Mistral Family ===
            'mistral': 8192,     # 8K context window
            'mistral-nemo': 128000, # 128K context window
            'mistral-large': 128000, # 128K context window
            'mixtral': 32768,    # 32K context window

            # === Qwen Family ===
            'qwen': 32768,       # 32K context window
            'qwen2': 32768,      # 32K context window
            'qwen2.5': 32768,    # 32K context window
            'qwen2.5-coder': 65536, # 64K context window (code-specific)
            'qwen3': 131072,     # 128K context window (latest generation)
            'qwen3-coder': 131072, # 128K context window (code-specific, latest generation)
            'codeqwen': 65536,   # 64K context window
            'qwq': 32768,        # 32K context window (reasoning model)

            # === Google Gemma Family ===
            'gemma': 8192,       # 8K context window
            'gemma2': 8192,      # 8K context window
            'codegemma': 8192,   # 8K context window

            # === Microsoft Phi Family ===
            'phi': 4096,         # 4K context window
            'phi3': 4096,        # 4K context window
            'phi3.5': 128000,    # 128K context window
            'phi-4': 128000,     # 128K context window (latest)

            # === DeepSeek Family ===
            'deepseek-coder': 8192,  # 8K context window
            'deepseek-r1': 65536,    # 64K context window (reasoning)

            # === IBM Granite Family ===
            'granite': 128000,   # 128K context window
            'granite-code': 128000, # 128K context window
            'granite3-dense': 128000, # 128K context window
            'granite3-moe': 128000,   # 128K context window

            # === 01.AI Yi Family ===
            'yi': 4096,          # 4K context window
            'yi-coder': 128000,  # 128K context window

            # === Upstage Solar Family ===
            'solar': 4096,       # 4K context window
            'solar-10.7b': 4096, # 4K context window

            # === Chat/Assistant Models ===
            'dolphin-mistral': 8192,    # 8K context window
            'dolphin-mixtral': 32768,   # 32K context window
            'dolphin-tinyllama': 2048,  # 2K context window
            'neural-chat': 4096,        # 4K context window
            'starling-lm': 8192,        # 8K context window
            'openchat': 8192,           # 8K context window
            'zephyr': 8192,             # 8K context window
            'stablelm-zephyr': 4096,    # 4K context window
            'vicuna': 16384,            # 16K context window
            'wizard-vicuna': 16384,     # 16K context window
            'orca': 4096,               # 4K context window
            'orca-mini': 4096,          # 4K context window

            # === Small/Efficient Models ===
            'tinyllama': 2048,   # 2K context window
            'smollm': 2048,      # 2K context window (original SmolLM)
            'smollm2': 8192,     # 8K context window (SmolLM2)

            # === Specialized Models ===
            'aya': 8192,         # 8K context window (multilingual)
            'falcon': 2048,      # 2K context window
            'falcon3': 32768,    # 32K context window (latest)
            'marco-o1': 32768,   # 32K context window (reasoning)

            # === Embedding Models ===
            'nomic-embed': 2048, # 2K context window
            'mxbai-embed': 512,  # 512 token context window
            'all-minilm': 512,   # 512 token context window
            'bge-large': 512,    # 512 token context window
            'bge-base': 512,     # 512 token context window
            'bge-small': 512,    # 512 token context window
            'e5-large': 512,     # 512 token context window
            'e5-base': 512,      # 512 token context window
            'e5-small': 512,     # 512 token context window
            'snowflake-arctic-embed': 512, # 512 token context window

            # === Code-Specific Models ===
            'starcoder': 8192,   # 8K context window
            'starcoder2': 16384, # 16K context window
            'codegeex4': 128000, # 128K context window
            'magicoder': 16384,  # 16K context window
            'wizardcoder': 16384, # 16K context window
        }

    def _get_model_specialties_dict(self) -> Dict[str, List[str]]:
        """Get the consolidated model specialties dictionary

        Returns:
            Dictionary mapping model names to their specialty lists
        """
        return {
            # === GENERAL PURPOSE & CHAT ===
            'llama2': ['general', 'chat', 'instruction-following', 'document-processing'],
            'llama3': ['general', 'chat', 'instruction-following', 'reasoning', 'document-processing'],
            'llama3.1': ['general', 'chat', 'instruction-following', 'reasoning', 'long-context', 'document-processing'],
            'llama3.2': ['general', 'chat', 'instruction-following', 'reasoning', 'long-context', 'vision', 'document-processing'],
            'llama3.3': ['general', 'chat', 'instruction-following', 'reasoning', 'long-context', 'document-processing'],

            # === CODE SPECIALISTS ===
            'codellama': ['code', 'programming', 'debugging', 'code-completion', 'document-processing'],
            'codeqwen': ['code', 'programming', 'debugging', 'code-completion', 'multilingual-code', 'document-processing'],
            'qwen2.5-coder': ['code', 'programming', 'debugging', 'code-completion', 'code-reasoning', 'document-processing'],
            'deepseek-coder': ['code', 'programming', 'debugging', 'code-completion', 'architecture', 'document-processing'],
            'granite-code': ['code', 'programming', 'enterprise', 'debugging', 'code-completion', 'document-processing'],
            'yi-coder': ['code', 'programming', 'debugging', 'code-completion', 'document-processing'],
            'codegemma': ['code', 'programming', 'debugging', 'lightweight'],
            'starcoder': ['code', 'programming', 'code-completion', 'github-trained'],
            'starcoder2': ['code', 'programming', 'code-completion', 'improved-performance'],
            'codegeex4': ['code', 'programming', 'multilingual-code', 'long-context', 'document-processing'],
            'magicoder': ['code', 'programming', 'instruction-tuned', 'oss-friendly'],
            'wizardcoder': ['code', 'programming', 'instruction-following', 'complex-tasks'],

            # === REASONING & ANALYSIS ===
            'deepseek-r1': ['reasoning', 'analysis', 'problem-solving', 'chain-of-thought', 'document-processing'],
            'qwq': ['reasoning', 'analysis', 'problem-solving', 'question-answering', 'document-processing'],
            'marco-o1': ['reasoning', 'analysis', 'problem-solving', 'step-by-step', 'document-processing'],

            # === MULTILINGUAL & SPECIALIZED ===
            'qwen': ['multilingual', 'chinese', 'general', 'chat', 'document-processing'],
            'qwen2': ['multilingual', 'chinese', 'general', 'chat', 'improved', 'document-processing'],
            'qwen2.5': ['multilingual', 'chinese', 'general', 'chat', 'latest', 'document-processing'],
            'qwen3': ['multilingual', 'chinese', 'general', 'chat', 'latest', 'long-context', 'reasoning', 'document-processing'],
            'aya': ['multilingual', 'global-languages', 'chat', 'cultural-awareness', 'document-processing'],
            'yi': ['multilingual', 'chinese', 'general', 'document-processing'],

            # === EFFICIENT & LIGHTWEIGHT ===
            'phi': ['efficient', 'lightweight', 'mobile', 'edge-computing'],
            'phi3': ['efficient', 'lightweight', 'mobile', 'improved-performance'],
            'phi3.5': ['efficient', 'lightweight', 'long-context', 'vision', 'document-processing'],
            'phi-4': ['efficient', 'lightweight', 'latest', 'improved-reasoning', 'document-processing'],
            'gemma': ['efficient', 'lightweight', 'google', 'responsible-ai'],
            'gemma2': ['efficient', 'lightweight', 'google', 'improved-performance', 'document-processing'],
            'tinyllama': ['ultra-lightweight', 'edge-computing', 'iot', 'fast-inference'],
            'smollm': ['ultra-lightweight', 'edge-computing', 'fast-inference', 'resource-constrained'],
            'smollm2': ['lightweight', 'edge-computing', 'fast-inference', 'improved-context'],

            # === ENTERPRISE & PRODUCTION ===
            'granite': ['enterprise', 'business', 'production', 'ibm', 'compliance', 'document-processing'],
            'granite3-dense': ['enterprise', 'business', 'production', 'dense-architecture', 'document-processing'],
            'granite3-moe': ['enterprise', 'business', 'production', 'mixture-of-experts', 'document-processing'],
            'mistral': ['production', 'european', 'efficient', 'commercial', 'document-processing'],
            'mistral-nemo': ['production', 'long-context', 'efficient', 'latest', 'document-processing'],
            'mistral-large': ['production', 'high-performance', 'enterprise', 'complex-tasks', 'document-processing'],
            'mixtral': ['production', 'mixture-of-experts', 'high-performance', 'efficient', 'document-processing'],

            # === CONVERSATIONAL & ASSISTANT ===
            'dolphin-mistral': ['chat', 'uncensored', 'creative', 'roleplay'],
            'dolphin-mixtral': ['chat', 'uncensored', 'creative', 'high-performance', 'document-processing'],
            'dolphin-tinyllama': ['chat', 'lightweight', 'uncensored', 'edge-computing'],
            'neural-chat': ['chat', 'conversation', 'intel-optimized', 'helpful', 'document-processing'],
            'starling-lm': ['chat', 'conversation', 'helpful', 'harmless', 'document-processing'],
            'openchat': ['chat', 'conversation', 'open-source', 'community', 'document-processing'],
            'zephyr': ['chat', 'instruction-following', 'helpful', 'lightweight'],
            'stablelm-zephyr': ['chat', 'instruction-following', 'stability-ai'],
            'vicuna': ['chat', 'conversation', 'gpt-style', 'research', 'document-processing'],
            'wizard-vicuna': ['chat', 'conversation', 'enhanced', 'instruction-following', 'document-processing'],
            'orca': ['chat', 'reasoning', 'microsoft-style', 'explanation', 'document-processing'],
            'orca-mini': ['chat', 'reasoning', 'lightweight', 'explanation'],

            # === SPECIALIZED DOMAINS ===
            'solar': ['korean', 'multilingual', 'upstage', 'general', 'document-processing'],
            'solar-10.7b': ['korean', 'multilingual', 'upstage', 'improved', 'document-processing'],
            'falcon': ['research', 'multilingual', 'uae', 'general'],
            'falcon3': ['research', 'multilingual', 'improved', 'latest', 'document-processing'],

            # === EMBEDDING & RETRIEVAL ===
            'nomic-embed': ['embedding', 'retrieval', 'semantic-search', 'long-context'],
            'mxbai-embed': ['embedding', 'retrieval', 'semantic-search', 'mixedbread'],
            'all-minilm': ['embedding', 'retrieval', 'lightweight', 'sentence-transformers'],
            'bge-large': ['embedding', 'retrieval', 'chinese', 'high-performance'],
            'bge-base': ['embedding', 'retrieval', 'chinese', 'balanced'],
            'bge-small': ['embedding', 'retrieval', 'chinese', 'lightweight'],
            'e5-large': ['embedding', 'retrieval', 'multilingual', 'microsoft'],
            'e5-base': ['embedding', 'retrieval', 'multilingual', 'balanced'],
            'e5-small': ['embedding', 'retrieval', 'multilingual', 'lightweight'],
            'snowflake-arctic-embed': ['embedding', 'retrieval', 'enterprise', 'snowflake'],
        }

    def get_model_specialties(self, model_name: Optional[str] = None) -> List[str]:
        """Get the specialties/capabilities of a specific model or the current model

        Args:
            model_name: Optional model name. If None, uses current Ollama model.

        Returns:
            List of specialty tags describing what the model is good at.
        """
        try:
            if not model_name:
                if self.ollama_client and hasattr(self.ollama_client, 'config'):
                    model_name = getattr(self.ollama_client.config, 'ollama_model', 'qwen3')
                else:
                    return ['general']

            # Get consolidated model specialties
            model_specialties = self._get_model_specialties_dict()

            # Find matching model (partial match)
            if not model_name:
                return ['general']

            model_name_lower = model_name.lower()
            for model_key, specialties in model_specialties.items():
                if model_key in model_name_lower:
                    return specialties

            # Default if no match found
            return ['general']

        except Exception as e:
            self.logger.debug(f"Error getting model specialties: {e}")
            return ['general']

    def is_model_good_at_document_processing(self, model_name: Optional[str] = None) -> bool:
        """Check if a model is specifically good at document processing tasks

        Args:
            model_name: Optional model name. If None, uses current Ollama model.

        Returns:
            True if the model has document processing capabilities, False otherwise.
        """
        try:
            specialties = self.get_model_specialties(model_name)
            return 'document-processing' in specialties
        except Exception as e:
            self.logger.debug(f"Error checking document processing capability: {e}")
            return False

    def get_document_processing_score(self, model_name: Optional[str] = None) -> int:
        """Get a score (0-100) indicating how good a model is at document processing

        Args:
            model_name: Optional model name. If None, uses current Ollama model.

        Returns:
            Score from 0-100 where higher means better at document processing.
        """
        try:
            specialties = self.get_model_specialties(model_name)

            if not specialties:
                return 0

            # Base score for document processing capability
            score = 85 if 'document-processing' in specialties else 30

            # Bonus points for complementary capabilities
            if 'long-context' in specialties:
                score += 10  # Better for large documents
            if 'reasoning' in specialties:
                score += 8   # Better analysis and understanding
            if 'instruction-following' in specialties:
                score += 5   # Better at following processing instructions
            if 'analysis' in specialties:
                score += 7   # Better at extracting insights
            if 'enterprise' in specialties:
                score += 5   # Better reliability for business docs

            # Penalty for limitations
            if 'ultra-lightweight' in specialties:
                score -= 15  # May struggle with complex documents
            if 'edge-computing' in specialties and 'document-processing' not in specialties:
                score -= 10  # Optimized for speed over comprehension
            if 'embedding' in specialties:
                score -= 40  # Embedding models aren't for text generation

            return min(100, max(0, score))

        except Exception as e:
            self.logger.debug(f"Error calculating document processing score: {e}")
            return 50  # Default moderate score

    def _create_content_summary(self, full_content: str, selected_content: str, total_sections: int, selected_sections: int) -> str:
        """Create a summary header explaining content selection"""
        summary = f"""[DOCUMENT CONTENT SUMMARY]
Original document: {len(full_content)} characters, {total_sections} sections
Selected content: {len(selected_content)} characters, {selected_sections} sections
Selection criteria: Relevance to commit context and documentation keywords

The following sections were selected as most relevant:

"""
        return summary + selected_content

    def _handle_extremely_large_document(self, content: str, document: DocumentRecord) -> str:
        """Handle documents that are too large even after section selection - Hybrid AI + Keyword Approach"""
        try:
            max_chars = self._get_model_context_limit() * 4  # Convert usable tokens to chars

            if len(content) <= max_chars:
                return content

            self.logger.warning(f"Document is extremely large ({len(content)} chars), using hybrid intelligent summarization")

            # Build rich context for semantic analysis
            query_context = self._build_query_context(document)
            sections = self._split_into_sections(content)

            # Choose ranking strategy based on document size and AI availability
            if self.ollama_client and len(sections) > 15:
                # Use semantic ranking for very large documents with many sections
                self.logger.debug("Using semantic ranking for large document with many sections")
                ranked_sections = self._semantic_section_ranking(sections, document, query_context)
            else:
                # Use improved keyword ranking for smaller documents or when AI unavailable
                self.logger.debug("Using improved keyword ranking")
                ranked_sections = self._improved_keyword_ranking(sections, document)

            # Intelligent section selection with narrative coherence
            selected_sections = self._select_coherent_sections(ranked_sections, max_chars)

            # Build coherent document with proper structure
            return self._build_coherent_document(selected_sections, document, len(content))

        except Exception as e:
            self.logger.error(f"Error in hybrid document handling: {e}")
            # Ultimate fallback: just truncate
            return content[:4000]

    def _build_query_context(self, document: DocumentRecord) -> str:
        """Build rich context for semantic matching"""
        context_parts = []

        if document.commit_message:
            context_parts.append(f"Commit: {document.commit_message}")

        if hasattr(document, 'changed_paths') and document.changed_paths:
            context_parts.append(f"Changed files: {', '.join(document.changed_paths[:5])}")

        if document.filename:
            context_parts.append(f"Document: {document.filename}")

        # Add repository context if available
        if document.repository_name:
            context_parts.append(f"Repository: {document.repository_name}")

        return " | ".join(context_parts)

    def _semantic_section_ranking(self, sections: List[Dict[str, Any]], document: DocumentRecord, query_context: str) -> List[Dict[str, Any]]:
        """Use Ollama to semantically rank sections by relevance"""
        if not self.ollama_client:
            self.logger.warning("No Ollama client available, falling back to keyword ranking")
            return self._improved_keyword_ranking(sections, document)

        try:
            # For very large documents, use embedding-based similarity if available
            if len(sections) > 25:
                return self._embedding_based_ranking(sections, query_context)

            # For moderately large documents, use LLM-based scoring
            return self._llm_based_section_scoring(sections, document, query_context)

        except Exception as e:
            self.logger.warning(f"Semantic ranking failed: {e}, falling back to keyword ranking")
            return self._improved_keyword_ranking(sections, document)

    def _improved_keyword_ranking(self, sections: List[Dict[str, Any]], document: DocumentRecord) -> List[Dict[str, Any]]:
        """Enhanced keyword-based ranking with better scoring logic"""
        try:
            keywords = self._extract_context_keywords(document)

            for section in sections:
                score = 0
                content_lower = section['content'].lower()
                title_lower = section['title'].lower()

                # High priority keywords (technical terms)
                high_priority_keywords = ['api', 'authentication', 'auth', 'oauth', 'login', 'security', 'endpoint',
                                        'configuration', 'setup', 'installation', 'error', 'bug', 'fix', 'update']
                for keyword in high_priority_keywords:
                    if keyword in title_lower:
                        score += 50
                    if keyword in content_lower:
                        score += content_lower.count(keyword) * 10

                # Context-specific keywords from document analysis
                for keyword in keywords:
                    if keyword in title_lower:
                        score += 25
                    if keyword in content_lower:
                        score += content_lower.count(keyword) * 3

                # Commit-specific keywords with better filtering
                if document.commit_message:
                    commit_keywords = [word.strip('.,!?;:()[]{}') for word in document.commit_message.lower().split()]
                    for keyword in commit_keywords:
                        if len(keyword) > 3 and keyword not in ['the', 'and', 'for', 'with', 'this', 'that', 'from', 'have', 'been']:
                            if keyword in title_lower:
                                score += 30
                            if keyword in content_lower:
                                score += content_lower.count(keyword) * 5

                # Boost score for important section characteristics
                if any(term in title_lower for term in ['introduction', 'overview', 'getting started', 'quick start']):
                    score += 20
                if any(term in title_lower for term in ['troubleshooting', 'faq', 'common issues', 'problems']):
                    score += 15
                if len(section['content']) > 500:  # Substantial content
                    score += 10

                section['relevance_score'] = score

            # Sort by relevance score
            return sorted(sections, key=lambda x: x.get('relevance_score', 0), reverse=True)

        except Exception as e:
            self.logger.error(f"Error in improved keyword ranking: {e}")
            return sections

    def _embedding_based_ranking(self, sections: List[Dict[str, Any]], query_context: str) -> List[Dict[str, Any]]:
        """Use embeddings to rank sections by semantic similarity"""
        try:
            # Check if embeddings API is available
            if not self._check_embeddings_support():
                self.logger.debug("Embeddings not supported, falling back to LLM scoring")
                return self._llm_based_section_scoring(sections, None, query_context)

            # Get context embedding
            context_embedding = self._get_embedding(query_context)
            if not context_embedding:
                raise Exception("Failed to get context embedding")

            # Get section embeddings and calculate similarity
            for section in sections:
                section_text = f"{section['title']} {section['content'][:1000]}"  # Limit content for embedding
                section_embedding = self._get_embedding(section_text)

                if section_embedding:
                    similarity = self._cosine_similarity(context_embedding, section_embedding)
                    section['similarity_score'] = similarity
                else:
                    section['similarity_score'] = 0.0

            # Sort by similarity score
            return sorted(sections, key=lambda x: x.get('similarity_score', 0), reverse=True)

        except Exception as e:
            self.logger.warning(f"Embedding-based ranking failed: {e}")
            return self._llm_based_section_scoring(sections, None, query_context)

    def _llm_based_section_scoring(self, sections: List[Dict[str, Any]], document: Optional[DocumentRecord], query_context: str) -> List[Dict[str, Any]]:
        """Use LLM to score sections for relevance"""
        try:
            # For efficiency, batch process sections in groups
            batch_size = 5
            scored_sections = []

            for i in range(0, len(sections), batch_size):
                batch = sections[i:i + batch_size]
                batch_scores = self._score_section_batch(batch, query_context)

                for j, section in enumerate(batch):
                    section['llm_score'] = batch_scores.get(j, 50)  # Default score
                    scored_sections.append(section)

            # Sort by LLM score
            return sorted(scored_sections, key=lambda x: x.get('llm_score', 0), reverse=True)

        except Exception as e:
            self.logger.warning(f"LLM-based scoring failed: {e}")
            # Fallback to basic scoring
            for section in sections:
                section['llm_score'] = 50
            return sections

    def _check_embeddings_support(self) -> bool:
        """Check if the Ollama server supports embeddings"""
        try:
            if not self.ollama_client or not hasattr(self.ollama_client, 'config'):
                return False

            url = f"{self.ollama_client.config.ollama_host}/api/embeddings"
            test_payload = {"model": self.ollama_client.config.ollama_model, "prompt": "test"}
            timeout = getattr(self.ollama_client.config, 'ollama_timeout_connection', 30)
            response = requests.post(url, json=test_payload, timeout=timeout)
            return response.status_code == 200
        except Exception:
            return False

    def _get_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding from Ollama"""
        try:
            if not self.ollama_client or not hasattr(self.ollama_client, 'config'):
                return None

            url = f"{self.ollama_client.config.ollama_host}/api/embeddings"
            payload = {"model": self.ollama_client.config.ollama_model, "prompt": text}
            timeout = getattr(self.ollama_client.config, 'ollama_timeout_embeddings', 60)
            response = requests.post(url, json=payload, timeout=timeout)
            response.raise_for_status()

            result = response.json()
            return result.get('embedding', [])
        except Exception as e:
            self.logger.debug(f"Failed to get embedding: {e}")
            return None

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            import math

            if len(vec1) != len(vec2) or not vec1 or not vec2:
                return 0.0

            dot_product = sum(a * b for a, b in zip(vec1, vec2))
            magnitude1 = math.sqrt(sum(a * a for a in vec1))
            magnitude2 = math.sqrt(sum(a * a for a in vec2))

            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0

            return dot_product / (magnitude1 * magnitude2)
        except Exception:
            return 0.0

    def _score_section_batch(self, sections: List[Dict[str, Any]], query_context: str) -> Dict[int, int]:
        """Score a batch of sections using LLM"""
        try:
            if not self.ollama_client:
                return {i: 50 for i in range(len(sections))}

            # Build prompt for batch scoring
            sections_text = ""
            for i, section in enumerate(sections):
                sections_text += f"\n--- Section {i} ---\nTitle: {section['title']}\nContent: {section['content'][:300]}...\n"

            prompt = f"""Context: {query_context}

Rate the relevance of each section (0-100) based on how well it matches the context.
Consider technical relevance, documentation impact, and relationship to the commit/changes.

{sections_text}

Return only a JSON object with section numbers as keys and scores as values:
{{"0": 85, "1": 45, "2": 92, ...}}"""

            system_prompt = "You are a technical document analyzer. Return only valid JSON with numeric scores."

            response = self.ollama_client.call_ollama(prompt, system_prompt)

            # Parse JSON response
            import json
            try:
                scores = json.loads(response.strip())
                return {int(k): int(v) for k, v in scores.items() if str(k).isdigit()}
            except json.JSONDecodeError:
                # Fallback: extract numbers from response
                return self._extract_scores_from_text(response, len(sections))

        except Exception as e:
            self.logger.debug(f"Batch scoring failed: {e}")
            return {i: 50 for i in range(len(sections))}  # Default scores

    def _extract_scores_from_text(self, text: str, num_sections: int) -> Dict[int, int]:
        """Extract scores from text response as fallback"""
        import re
        scores = {}

        # Look for patterns like "0": 85, "1": 45, etc.
        matches = re.findall(r'["\']?(\d+)["\']?\s*:\s*(\d+)', text)
        for match in matches:
            section_idx, score = int(match[0]), int(match[1])
            if 0 <= section_idx < num_sections and 0 <= score <= 100:
                scores[section_idx] = score

        # Fill missing scores with default
        for i in range(num_sections):
            if i not in scores:
                scores[i] = 50

        return scores

    def _select_coherent_sections(self, ranked_sections: List[Dict[str, Any]], max_chars: int) -> List[Dict[str, Any]]:
        """Select sections that maintain narrative coherence"""
        try:
            selected = []
            total_chars = 0

            # Always include top-ranked section if available
            if ranked_sections:
                selected.append(ranked_sections[0])
                total_chars += len(ranked_sections[0]['content'])

            # Try to include consecutive sections for better flow
            for section in ranked_sections[1:]:
                section_chars = len(section['content'])

                if total_chars + section_chars > max_chars:
                    break

                # Prefer sections that are consecutive to already selected ones
                is_consecutive = any(
                    abs(section.get('start_line', 0) - sel.get('start_line', 0)) < 50
                    for sel in selected
                )

                # Always take top 3 sections, then prefer consecutive ones
                if len(selected) < 3 or is_consecutive:
                    selected.append(section)
                    total_chars += section_chars

            # Sort selected sections by original document order for coherent reading
            return sorted(selected, key=lambda x: x.get('start_line', 0))

        except Exception as e:
            self.logger.error(f"Error selecting coherent sections: {e}")
            # Fallback: just take top sections until limit
            selected = []
            total_chars = 0
            for section in ranked_sections:
                if total_chars + len(section['content']) > max_chars:
                    break
                selected.append(section)
                total_chars += len(section['content'])
            return selected

    def _build_coherent_document(self, selected_sections: List[Dict[str, Any]], document: DocumentRecord, original_length: int) -> str:
        """Build coherent document with proper structure and context"""
        try:
            if not selected_sections:
                return "No relevant sections found."

            # Build content from selected sections
            content_parts = []
            for section in selected_sections:
                title = section.get('title', 'Section')
                content = section.get('content', '')

                # Format section with proper heading
                if title and not title.startswith('#'):
                    content_parts.append(f"## {title}\n{content}")
                else:
                    content_parts.append(content)

            result = '\n\n'.join(content_parts)

            # Add informative header about the selection process
            selection_info = self._create_selection_summary(document, selected_sections, original_length, len(result))

            return selection_info + result

        except Exception as e:
            self.logger.error(f"Error building coherent document: {e}")
            # Fallback: just concatenate content
            return '\n\n'.join(section.get('content', '') for section in selected_sections)

    def _create_selection_summary(self, document: DocumentRecord, selected_sections: List[Dict[str, Any]],
                                original_length: int, final_length: int) -> str:
        """Create informative summary about the selection process"""
        try:
            # Determine which ranking method was used
            ranking_method = "keyword-based"
            if any('similarity_score' in section for section in selected_sections):
                ranking_method = "semantic embedding"
            elif any('llm_score' in section for section in selected_sections):
                ranking_method = "AI-powered"

            summary = f"""[INTELLIGENT DOCUMENT PROCESSING]
Original document: {original_length:,} characters
Processed document: {final_length:,} characters ({final_length/original_length*100:.1f}% of original)
Sections selected: {len(selected_sections)} most relevant sections
Ranking method: {ranking_method} analysis
Context: {document.commit_message[:100] if document.commit_message else 'N/A'}...

Selected sections are ordered for optimal readability and technical coherence.

"""
            return summary

        except Exception as e:
            self.logger.debug(f"Error creating selection summary: {e}")
            return f"[PROCESSED DOCUMENT - {len(selected_sections)} sections selected]\n\n"

    def _select_top_sections_inclusive(self, scored_sections: List[Dict[str, Any]], max_tokens: int) -> str:
        """More inclusive section selection when initial selection was too restrictive"""
        try:
            selected_content = []
            total_chars = 0
            max_chars = max_tokens * 4

            # Include sections with any relevance score > 0, plus some with score 0
            relevant_sections = [s for s in scored_sections if s['relevance_score'] > 0]

            # If still not enough, include some sections with score 0 (up to 30% of content)
            if len(relevant_sections) < 3:
                zero_score_sections = [s for s in scored_sections if s['relevance_score'] == 0]
                additional_sections = zero_score_sections[:max(2, len(scored_sections) // 3)]
                relevant_sections.extend(additional_sections)

            # Add sections until we hit the limit
            for section in relevant_sections:
                if total_chars + len(section['content']) > max_chars:
                    break

                selected_content.append(f"## {section['title']}\n{section['content']}")
                total_chars += len(section['content'])

            result = '\n\n'.join(selected_content)
            self.logger.debug(f"Inclusive selection: {len(selected_content)} sections, {len(result)} chars")
            return result

        except Exception as e:
            self.logger.error(f"Error in inclusive section selection: {e}")
            return scored_sections[0]['content'][:max_tokens * 4] if scored_sections else ""
