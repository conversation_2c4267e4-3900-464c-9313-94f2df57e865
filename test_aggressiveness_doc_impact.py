#!/usr/bin/env python3
"""
Test script to verify that documentation impact assessment respects repository aggressiveness settings
"""

import sys
import os
from datetime import datetime

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from metadata_extractor import MetadataExtractor
from document_database import DocumentRecord
from config_manager import ConfigManager

def test_aggressiveness_impact():
    """Test documentation impact assessment with different aggressiveness levels"""
    print("🧪 Testing Documentation Impact Assessment with Aggressiveness Settings...")
    
    # Sample documentation content that should be treated differently based on aggressiveness
    test_content_explicit = """
## Summary
This commit introduces fundamental core system components for the RepoSense C++ Test project, including logging, configuration management, and timing utilities.

## Documentation Impact
Yes, documentation updates are needed. The commit introduces new APIs and configuration options that require:
- API documentation for Logger, Config, and Timer classes
- Configuration file format specification
- Usage guidelines for the logging system

## Risk Level
Low risk. The changes are isolated utility components with no external dependencies.
"""

    # Content with contextual indicators (should be more sensitive to aggressiveness)
    test_content_contextual = """
## Summary
This commit refactors internal logging utilities and adds minor configuration cleanup for better code organization.

## Technical Details
The implementation includes internal refactoring of:
1. Logger Class: Internal cleanup of private methods
2. Config Class: Minor style improvements and internal optimization
3. Timer Utility: Small performance improvements to internal calculations

## Documentation Impact
The commit affects internal implementation details and includes minor cleanup of configuration handling. The changes involve refactoring of private methods and internal optimization without affecting public interfaces.

## Risk Level
Low risk. The changes are internal refactoring with no external dependencies.
"""

    # Initial setup content (should definitely be NO in VERY_AGGRESSIVE)
    test_content_initial = """
## Summary
This commit establishes the initial project structure for a C++ project intended for comprehensive RepoSense AI testing.

## Technical Details
The changes introduce core infrastructure components:
- A basic CMakeLists.txt that sets the minimum required CMake version
- An empty LICENSE file containing only "MIT License"
- A basic README.md with a placeholder title
- Directory structure for source code organization

## Documentation Impact
The commit establishes basic project structure with placeholder files. While initial documentation is present, the project structure and build configuration may need documentation as development progresses.

## Risk Level
Low risk. This is a foundational commit that lays the groundwork for further development.
"""

    try:
        # Initialize metadata extractor with config manager
        config_manager = ConfigManager('data/config.json')
        extractor = MetadataExtractor(config_manager=config_manager)
        
        # Test different aggressiveness levels with different content types
        aggressiveness_levels = ["CONSERVATIVE", "BALANCED", "AGGRESSIVE", "VERY_AGGRESSIVE"]
        test_cases = [
            ("Explicit YES", test_content_explicit),
            ("Contextual (refactoring)", test_content_contextual),
            ("Initial setup", test_content_initial)
        ]

        for test_name, test_content in test_cases:
            print(f"\n🔍 Testing: {test_name}")

            for aggressiveness in aggressiveness_levels:
                print(f"\n📊 {aggressiveness} mode:")

                # Create a mock document record for reposense_cpp_test repository
                mock_doc = DocumentRecord(
                    id="test-doc",
                    repository_id="test-repo",
                    repository_name="reposense_cpp_test",  # This should have VERY_AGGRESSIVE setting
                    revision="2",
                    date=datetime.now(),
                    filename="revision_2.md",
                    filepath="/test/revision_2.md",
                    size=1000,
                    author="test",
                    commit_message="Test commit"
                )

                # Temporarily override the aggressiveness for testing
                original_method = extractor._get_repository_aggressiveness
                extractor._get_repository_aggressiveness = lambda doc_record: aggressiveness

                try:
                    # Test heuristic analysis
                    heuristic_result = extractor._extract_doc_impact_heuristic_simple(test_content, aggressiveness)
                    if heuristic_result:
                        impact, confidence = heuristic_result
                        print(f"    Heuristic: {impact} (confidence: {confidence:.2f})")
                    else:
                        print(f"    Heuristic: No result")

                    # Test full documentation impact assessment
                    doc_impact = extractor.extract_documentation_impact(test_content, mock_doc)
                    print(f"    Final Decision: {doc_impact}")

                finally:
                    # Restore original method
                    extractor._get_repository_aggressiveness = original_method
        
        print("\n✅ Aggressiveness testing completed!")
        print("\nExpected behavior:")
        print("- CONSERVATIVE: Should be more likely to say YES (documentation needed)")
        print("- BALANCED: Standard behavior")
        print("- AGGRESSIVE: Should be less likely to say YES")
        print("- VERY_AGGRESSIVE: Should be much less likely to say YES")
        print("\nKey observations:")
        print("- Explicit YES/NO statements should be respected but with adjusted confidence")
        print("- Contextual content (refactoring, internal) should show more variation")
        print("- Initial setup should be NO in VERY_AGGRESSIVE mode")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_real_repository_config():
    """Test with actual repository configuration"""
    print("\n🧪 Testing with Real Repository Configuration...")

    try:
        config_manager = ConfigManager('data/config.json')
        extractor = MetadataExtractor(config_manager=config_manager)

        # Create document record for reposense_cpp_test
        mock_doc = DocumentRecord(
            id="test-doc",
            repository_id="test-repo",
            repository_name="reposense_cpp_test",
            revision="1",
            date=datetime.now(),
            filename="revision_1.md",
            filepath="/test/revision_1.md",
            size=1000,
            author="test",
            commit_message="Initial project setup"
        )

        # Get actual aggressiveness setting
        aggressiveness = extractor._get_repository_aggressiveness(mock_doc)
        print(f"Repository 'reposense_cpp_test' aggressiveness: {aggressiveness}")
        print("(No repository configured, so defaults to BALANCED)")

        # Test with initial setup content using different aggressiveness levels
        initial_setup_content = """
## Summary
This commit establishes the initial project structure for a C++ project.

## Documentation Impact
The commit establishes basic project structure with placeholder files. The project structure and build configuration may need documentation as development progresses.

## Risk Level
Low risk. This is a foundational commit.
"""

        print(f"\nTesting initial setup content with different aggressiveness:")
        for test_aggressiveness in ["BALANCED", "AGGRESSIVE", "VERY_AGGRESSIVE"]:
            # Override aggressiveness for testing
            original_method = extractor._get_repository_aggressiveness
            extractor._get_repository_aggressiveness = lambda doc_record: test_aggressiveness

            try:
                doc_impact = extractor.extract_documentation_impact(initial_setup_content, mock_doc)
                print(f"  {test_aggressiveness}: {doc_impact}")
            finally:
                extractor._get_repository_aggressiveness = original_method

    except Exception as e:
        print(f"❌ Error testing real config: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_aggressiveness_impact()
    test_real_repository_config()
