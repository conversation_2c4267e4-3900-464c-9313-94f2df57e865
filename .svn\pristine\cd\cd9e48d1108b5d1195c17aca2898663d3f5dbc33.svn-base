#!/usr/bin/env python3
"""
Helper methods for ContextAnalyzer
Contains the implementation of analysis methods for code change context
"""

import re
import json
import logging
from pathlib import Path
from typing import List, Optional, Tuple, Dict, TYPE_CHECKING

if TYPE_CHECKING:
    from prompt_templates import ChangeType
    from document_database import DocumentRecord


class ContextAnalyzerHelpers:
    """Helper methods for analyzing code change context"""
    
    def __init__(self):
        # Import at runtime to avoid circular imports
        from prompt_templates import ChangeType

        # File pattern mappings for change type detection
        self.file_patterns = {
            ChangeType.SECURITY: [
                r'auth', r'login', r'password', r'token', r'jwt', r'oauth', r'security',
                r'encrypt', r'decrypt', r'hash', r'ssl', r'tls', r'cert'
            ],
            ChangeType.DATABASE: [
                r'\.sql$', r'migration', r'schema', r'database', r'db_', r'model',
                r'entity', r'repository', r'dao'
            ],
            ChangeType.API: [
                r'api', r'endpoint', r'route', r'controller', r'service',
                r'rest', r'graphql', r'swagger', r'openapi'
            ],
            ChangeType.UI_FRONTEND: [
                r'\.html$', r'\.css$', r'\.js$', r'\.jsx$', r'\.tsx$', r'\.vue$',
                r'component', r'template', r'view', r'ui', r'frontend'
            ],
            ChangeType.INFRASTRUCTURE: [
                r'docker', r'kubernetes', r'k8s', r'terraform', r'ansible',
                r'deploy', r'infra', r'config\.', r'\.yml$', r'\.yaml$'
            ],
            ChangeType.CONFIGURATION: [
                r'config', r'settings', r'properties', r'\.env', r'\.ini$',
                r'\.conf$', r'\.json$'
            ],
            ChangeType.TESTING: [
                r'test', r'spec', r'\.test\.', r'\.spec\.', r'mock', r'stub'
            ],
            ChangeType.DOCUMENTATION: [
                r'\.md$', r'\.txt$', r'readme', r'doc', r'manual', r'guide'
            ]
        }

        # Store ChangeType for later use
        self.ChangeType = ChangeType
        
        # Critical file patterns
        self.critical_patterns = [
            r'auth', r'login', r'password', r'payment', r'billing',
            r'core', r'main', r'index', r'app', r'server',
            r'database', r'migration', r'schema'
        ]
        
        # Programming language detection
        self.language_patterns = {
            'Python': [r'\.py$'],
            'JavaScript': [r'\.js$', r'\.jsx$'],
            'TypeScript': [r'\.ts$', r'\.tsx$'],
            'Java': [r'\.java$'],
            'C#': [r'\.cs$'],
            'C++': [r'\.cpp$', r'\.cc$', r'\.cxx$'],
            'C': [r'\.c$'],
            'Go': [r'\.go$'],
            'Rust': [r'\.rs$'],
            'PHP': [r'\.php$'],
            'Ruby': [r'\.rb$'],
            'SQL': [r'\.sql$'],
            'HTML': [r'\.html$', r'\.htm$'],
            'CSS': [r'\.css$', r'\.scss$', r'\.sass$'],
            'Shell': [r'\.sh$', r'\.bash$'],
            'YAML': [r'\.yml$', r'\.yaml$'],
            'JSON': [r'\.json$'],
            'XML': [r'\.xml$']
        }
    
    def extract_changed_files(self, document: 'DocumentRecord', diff_content: Optional[str] = None) -> List[str]:
        """Extract list of changed files from document or diff content"""
        changed_files = []
        
        # Try to get from document first
        if hasattr(document, 'changed_paths') and document.changed_paths:
            if isinstance(document.changed_paths, list):
                changed_files = document.changed_paths
            elif isinstance(document.changed_paths, str):
                # Parse comma-separated or newline-separated files
                changed_files = [f.strip() for f in re.split(r'[,\n]', document.changed_paths) if f.strip()]
        
        # If no files from document, try to extract from diff content
        if not changed_files and diff_content:
            # Look for common diff patterns
            file_patterns = [
                r'^\+\+\+ (.+)$',  # Git diff
                r'^--- (.+)$',     # Git diff
                r'^Index: (.+)$',  # SVN diff
                r'^diff --git a/(.+) b/',  # Git diff header
            ]
            
            for pattern in file_patterns:
                matches = re.findall(pattern, diff_content, re.MULTILINE)
                for match in matches:
                    # Clean up the file path
                    file_path = match.strip()
                    if file_path.startswith('a/') or file_path.startswith('b/'):
                        file_path = file_path[2:]
                    if file_path not in changed_files:
                        changed_files.append(file_path)
        
        # If still no files, try to infer from document filename
        if not changed_files and hasattr(document, 'filename') and document.filename:
            changed_files = [document.filename]
        
        return changed_files
    
    def detect_repository_type(self, document: 'DocumentRecord') -> str:
        """Detect repository type from document"""
        if hasattr(document, 'repository_type') and document.repository_type:
            return document.repository_type.lower()
        
        # Try to infer from repository name or URL
        repo_name = getattr(document, 'repository_name', '').lower()
        if 'git' in repo_name:
            return 'git'
        elif 'svn' in repo_name:
            return 'svn'
        
        # Default to SVN (most common in your system)
        return 'svn'
    
    def detect_change_type(self, changed_files: List[str],
                          commit_message: Optional[str] = None,
                          diff_content: Optional[str] = None) -> 'ChangeType':
        """Detect the primary type of change based on files and content"""

        # Score each change type based on file patterns
        type_scores = {change_type: 0 for change_type in self.ChangeType}
        
        for file_path in changed_files:
            file_lower = file_path.lower()
            
            for change_type, patterns in self.file_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, file_lower):
                        type_scores[change_type] += 1
        
        # Boost scores based on commit message
        if commit_message:
            message_lower = commit_message.lower()
            
            if any(word in message_lower for word in ['security', 'auth', 'login', 'password']):
                type_scores[self.ChangeType.SECURITY] += 3

            if any(word in message_lower for word in ['database', 'migration', 'schema', 'sql']):
                type_scores[self.ChangeType.DATABASE] += 3

            if any(word in message_lower for word in ['api', 'endpoint', 'rest']):
                type_scores[self.ChangeType.API] += 2

            if any(word in message_lower for word in ['ui', 'frontend', 'interface']):
                type_scores[self.ChangeType.UI_FRONTEND] += 2

            if any(word in message_lower for word in ['fix', 'bug', 'issue']):
                type_scores[self.ChangeType.BUGFIX] += 2

            if any(word in message_lower for word in ['feature', 'add', 'new']):
                type_scores[self.ChangeType.FEATURE] += 1

            if any(word in message_lower for word in ['refactor', 'cleanup', 'reorganize']):
                type_scores[self.ChangeType.REFACTORING] += 1

            if any(word in message_lower for word in ['test', 'testing']):
                type_scores[self.ChangeType.TESTING] += 2

            if any(word in message_lower for word in ['doc', 'documentation', 'readme']):
                type_scores[self.ChangeType.DOCUMENTATION] += 2
        
        # Find the highest scoring type
        max_score = max(type_scores.values())
        if max_score > 0:
            for change_type, score in type_scores.items():
                if score == max_score:
                    return change_type
        
        # Default to GENERAL if no specific type detected
        return self.ChangeType.GENERAL
    
    def detect_programming_languages(self, changed_files: List[str]) -> List[str]:
        """Detect programming languages from file extensions"""
        languages = set()
        
        for file_path in changed_files:
            file_lower = file_path.lower()
            
            for language, patterns in self.language_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, file_lower):
                        languages.add(language)
                        break
        
        return list(languages) if languages else ['Unknown']
    
    def detect_project_type(self, changed_files: List[str], repository_name: str) -> str:
        """Detect project type based on files and repository name"""
        
        # Check for web application indicators
        web_indicators = ['.html', '.css', '.js', '.jsx', '.vue', '.php', 'template', 'view']
        if any(indicator in ' '.join(changed_files).lower() for indicator in web_indicators):
            return "web_app"
        
        # Check for library indicators
        library_indicators = ['lib', 'package', 'module', 'sdk']
        if any(indicator in repository_name.lower() for indicator in library_indicators):
            return "library"
        
        # Check for service indicators
        service_indicators = ['service', 'api', 'server', 'daemon']
        if any(indicator in repository_name.lower() for indicator in service_indicators):
            return "service"
        
        # Check for mobile app indicators
        mobile_indicators = ['.swift', '.kt', '.java', 'android', 'ios']
        if any(indicator in ' '.join(changed_files).lower() for indicator in mobile_indicators):
            return "mobile_app"
        
        # Default
        return "application"

    def analyze_change_magnitude(self, diff_content: Optional[str] = None) -> Tuple[int, int]:
        """Analyze the magnitude of changes (lines added/removed)"""
        lines_added = 0
        lines_removed = 0

        if not diff_content:
            return lines_added, lines_removed

        # Parse diff content for line changes
        for line in diff_content.split('\n'):
            if line.startswith('+') and not line.startswith('+++'):
                lines_added += 1
            elif line.startswith('-') and not line.startswith('---'):
                lines_removed += 1

        return lines_added, lines_removed

    def affects_core_logic(self, changed_files: List[str], commit_message: Optional[str] = None) -> bool:
        """Determine if changes affect core business logic"""
        core_indicators = [
            'core', 'main', 'business', 'logic', 'service', 'manager',
            'controller', 'processor', 'handler', 'engine'
        ]

        # Check file paths
        for file_path in changed_files:
            file_lower = file_path.lower()
            if any(indicator in file_lower for indicator in core_indicators):
                return True

        # Check commit message
        if commit_message:
            message_lower = commit_message.lower()
            if any(indicator in message_lower for indicator in ['core', 'business logic', 'main']):
                return True

        return False

    def affects_security(self, changed_files: List[str],
                        commit_message: Optional[str] = None,
                        diff_content: Optional[str] = None) -> bool:
        """Determine if changes affect security"""
        security_indicators = [
            'auth', 'login', 'password', 'token', 'jwt', 'oauth', 'security',
            'encrypt', 'decrypt', 'hash', 'ssl', 'tls', 'cert', 'permission',
            'role', 'access', 'session', 'cookie'
        ]

        # Check file paths
        for file_path in changed_files:
            file_lower = file_path.lower()
            if any(indicator in file_lower for indicator in security_indicators):
                return True

        # Check commit message
        if commit_message:
            message_lower = commit_message.lower()
            if any(indicator in message_lower for indicator in security_indicators):
                return True

        # Check diff content for security-related changes
        if diff_content:
            diff_lower = diff_content.lower()
            if any(indicator in diff_lower for indicator in security_indicators):
                return True

        return False

    def affects_ui(self, changed_files: List[str]) -> bool:
        """Determine if changes affect user interface"""
        ui_extensions = ['.html', '.css', '.js', '.jsx', '.tsx', '.vue', '.scss', '.sass']
        ui_indicators = ['ui', 'frontend', 'view', 'template', 'component', 'page']

        for file_path in changed_files:
            file_lower = file_path.lower()

            # Check file extensions
            if any(file_lower.endswith(ext) for ext in ui_extensions):
                return True

            # Check path indicators
            if any(indicator in file_lower for indicator in ui_indicators):
                return True

        return False

    def affects_database(self, changed_files: List[str], commit_message: Optional[str] = None) -> bool:
        """Determine if changes affect database"""
        db_indicators = [
            'database', 'db_', 'sql', 'migration', 'schema', 'model',
            'entity', 'repository', 'dao', 'orm'
        ]

        # Check file paths
        for file_path in changed_files:
            file_lower = file_path.lower()
            if any(indicator in file_lower for indicator in db_indicators):
                return True

            # Check for SQL files
            if file_lower.endswith('.sql'):
                return True

        # Check commit message
        if commit_message:
            message_lower = commit_message.lower()
            if any(indicator in message_lower for indicator in db_indicators):
                return True

        return False

    def affects_api(self, changed_files: List[str], commit_message: Optional[str] = None) -> bool:
        """Determine if changes affect API"""
        api_indicators = [
            'api', 'endpoint', 'route', 'controller', 'service',
            'rest', 'graphql', 'swagger', 'openapi'
        ]

        # Check file paths
        for file_path in changed_files:
            file_lower = file_path.lower()
            if any(indicator in file_lower for indicator in api_indicators):
                return True

        # Check commit message
        if commit_message:
            message_lower = commit_message.lower()
            if any(indicator in message_lower for indicator in api_indicators):
                return True

        return False

    def affects_configuration(self, changed_files: List[str]) -> bool:
        """Determine if changes affect configuration"""
        config_extensions = ['.json', '.yml', '.yaml', '.ini', '.conf', '.env', '.properties']
        config_indicators = ['config', 'settings', 'properties', 'environment']

        for file_path in changed_files:
            file_lower = file_path.lower()

            # Check file extensions
            if any(file_lower.endswith(ext) for ext in config_extensions):
                return True

            # Check path indicators
            if any(indicator in file_lower for indicator in config_indicators):
                return True

        return False

    def extract_product_documentation_context(self, document_record: 'DocumentRecord') -> Dict[str, str]:
        """
        Extract context from product documentation files for enhanced AI analysis.

        This implements a RAG-like approach by reading product documentation files
        and extracting relevant context to inform AI recommendations.

        Args:
            document_record: The document record containing repository information

        Returns:
            Dictionary containing extracted product documentation context
        """
        context = {
            'product_overview': '',
            'key_features': '',
            'changelog_structure': '',
            'documentation_style': '',
            'project_goals': '',
            'user_audience': '',
            'technical_stack': '',
            'deployment_info': ''
        }

        try:
            # Get repository path from document record
            repo_path = self._get_repository_path(document_record)
            if not repo_path:
                return context

            # Define product documentation files to analyze
            product_doc_files = [
                'README.md',
                'README.txt',
                'CHANGELOG.md',
                'CHANGELOG.html',
                'CHANGELOG.docx',
                'CHANGELOG.txt',
                'docs/README.md',
                'docs/overview.md',
                'docs/getting-started.md',
                'CONTRIBUTING.md',
                'package.json',
                'setup.py',
                'pyproject.toml',
                'Cargo.toml',
                'pom.xml'
            ]

            # Extract context from each available file
            for doc_file in product_doc_files:
                file_path = repo_path / doc_file
                if file_path.exists() and file_path.is_file():
                    file_context = self._extract_file_context(file_path, doc_file)
                    self._merge_context(context, file_context)

            # Post-process and clean up context
            context = self._clean_and_summarize_context(context)

        except Exception as e:
            # Log error but don't fail the analysis
            logging.getLogger(__name__).debug(f"Error extracting product documentation context: {e}")

        return context

    def _get_repository_path(self, document_record: 'DocumentRecord') -> Optional[Path]:
        """Get the repository root path from document record"""
        try:
            # Try to determine repository path from document filepath
            doc_path = Path(document_record.filepath)

            # Look for common repository indicators going up the directory tree
            current_path = doc_path.parent
            max_levels = 10  # Prevent infinite loops

            for _ in range(max_levels):
                if not current_path or current_path == current_path.parent:
                    break

                # Check for repository indicators
                repo_indicators = ['.git', '.svn', 'README.md', 'package.json', 'setup.py']
                if any((current_path / indicator).exists() for indicator in repo_indicators):
                    return current_path

                # Check if this looks like a repository directory based on name
                if any(keyword in current_path.name.lower() for keyword in ['repo', 'project', document_record.repository_name.lower()]):
                    return current_path

                current_path = current_path.parent

            # Fallback: try to construct path from repository name
            if hasattr(document_record, 'repository_name') and document_record.repository_name:
                # Common repository path patterns (repository_name now includes branch path)
                possible_paths = [
                    Path(f"/app/data/output/repositories/{document_record.repository_name}"),
                    Path(f"repositories/{document_record.repository_name}"),
                    Path(f"data/output/repositories/{document_record.repository_name}"),
                    Path(f"/tmp/{document_record.repository_name}"),
                ]

                for path in possible_paths:
                    if path.exists() and path.is_dir():
                        return path

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error determining repository path: {e}")

        return None

    def _extract_file_context(self, file_path: Path, filename: str) -> Dict[str, str]:
        """Extract context from a specific product documentation file"""
        context = {}

        try:
            # Determine file type and extraction strategy
            file_extension = file_path.suffix.lower()

            if file_extension in ['.md', '.txt', '.rst']:
                context = self._extract_text_file_context(file_path, filename)
            elif file_extension == '.html':
                context = self._extract_html_file_context(file_path, filename)
            elif file_extension == '.json':
                context = self._extract_json_file_context(file_path, filename)
            elif file_extension in ['.toml', '.yaml', '.yml']:
                context = self._extract_config_file_context(file_path, filename)
            elif file_extension == '.py' and filename == 'setup.py':
                context = self._extract_setup_py_context(file_path)
            elif file_extension == '.docx':
                # For now, skip DOCX files as they require additional libraries
                # Could be enhanced later with python-docx
                context = {'documentation_style': 'Uses Microsoft Word documents'}

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error extracting context from {file_path}: {e}")

        return context

    def _extract_text_file_context(self, file_path: Path, filename: str) -> Dict[str, str]:
        """Extract context from text-based files (MD, TXT, RST)"""
        context: Dict[str, str] = {}

        try:
            # Read file with multiple encoding attempts
            content = self._read_file_with_encoding(file_path)
            if not content:
                return context

            # Extract context based on filename
            if 'readme' in filename.lower():
                context.update(self._extract_readme_context(content))
            elif 'changelog' in filename.lower():
                context.update(self._extract_changelog_context(content))
            elif 'contributing' in filename.lower():
                context.update(self._extract_contributing_context(content))
            else:
                # Generic text file analysis
                context.update(self._extract_generic_text_context(content, filename))

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error extracting text context from {file_path}: {e}")

        return context

    def _read_file_with_encoding(self, file_path: Path) -> Optional[str]:
        """Read file with multiple encoding attempts"""
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception:
                break

        return None

    def _extract_readme_context(self, content: str) -> Dict[str, str]:
        """Extract context from README files"""
        context = {}

        # Extract project overview (first few paragraphs)
        lines = content.split('\n')
        overview_lines: list[str] = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Skip title lines
            if line.startswith('#') and len(overview_lines) == 0:
                continue

            # Stop at first major section
            if line.startswith('##') and len(overview_lines) > 0:
                break

            # Collect overview content
            if not line.startswith('#'):
                overview_lines.append(line)

            # Limit overview length
            if len(overview_lines) >= 5:
                break

        if overview_lines:
            context['product_overview'] = ' '.join(overview_lines)[:500]

        # Extract features section
        features = self._extract_section_content(content, ['features', 'key features', 'capabilities'])
        if features:
            context['key_features'] = features[:400]

        # Extract technical stack information
        tech_stack = self._extract_section_content(content, ['requirements', 'dependencies', 'installation', 'setup', 'technology'])
        if tech_stack:
            context['technical_stack'] = tech_stack[:300]

        # Extract deployment information
        deployment = self._extract_section_content(content, ['deployment', 'docker', 'installation', 'getting started'])
        if deployment:
            context['deployment_info'] = deployment[:300]

        # Determine user audience from content
        if any(word in content.lower() for word in ['developer', 'api', 'sdk', 'library']):
            context['user_audience'] = 'developers'
        elif any(word in content.lower() for word in ['user', 'guide', 'tutorial', 'how to']):
            context['user_audience'] = 'end-users'
        else:
            context['user_audience'] = 'general'

        return context

    def _extract_section_content(self, content: str, section_names: List[str]) -> Optional[str]:
        """Extract content from specific sections in markdown/text files"""
        lines = content.split('\n')
        section_content = []
        in_target_section = False

        for line in lines:
            line_lower = line.lower().strip()

            # Check if this is a section header
            if line.startswith('#') or line.startswith('=') or line.startswith('-'):
                # Check if this is one of our target sections
                if any(section_name.lower() in line_lower for section_name in section_names):
                    in_target_section = True
                    continue
                elif in_target_section and (line.startswith('#') or line.startswith('=')):
                    # We've hit another section, stop collecting
                    break

            # Collect content if we're in a target section
            if in_target_section and line.strip():
                section_content.append(line.strip())

                # Limit content length
                if len(' '.join(section_content)) > 800:
                    break

        return ' '.join(section_content) if section_content else None

    def _extract_changelog_context(self, content: str) -> Dict[str, str]:
        """Extract context from CHANGELOG files"""
        context = {}

        # Analyze changelog structure
        if '##' in content and '[' in content:
            context['changelog_structure'] = 'Structured with version headers and categorized changes'
        elif '##' in content:
            context['changelog_structure'] = 'Version-based with headers'
        elif '*' in content or '-' in content:
            context['changelog_structure'] = 'Simple bullet-point format'
        else:
            context['changelog_structure'] = 'Free-form text format'

        # Extract recent changes (first few entries)
        lines = content.split('\n')[:20]  # First 20 lines
        recent_changes = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                recent_changes.append(line)
                if len(recent_changes) >= 3:
                    break

        if recent_changes:
            context['project_goals'] = f"Recent focus: {' '.join(recent_changes)[:300]}"

        return context

    def _extract_contributing_context(self, content: str) -> Dict[str, str]:
        """Extract context from CONTRIBUTING files"""
        context = {}

        # Determine development style from contributing guidelines
        if 'pull request' in content.lower() or 'pr' in content.lower():
            context['documentation_style'] = 'Collaborative development with pull requests'
        if 'issue' in content.lower():
            context['documentation_style'] += ', issue-based workflow'
        if 'test' in content.lower():
            context['documentation_style'] += ', test-driven development'

        return context

    def _extract_generic_text_context(self, content: str, filename: str) -> Dict[str, str]:
        """Extract context from generic text files"""
        context = {}

        # Basic content analysis
        word_count = len(content.split())
        if word_count > 1000:
            context['documentation_style'] = 'Comprehensive documentation'
        elif word_count > 200:
            context['documentation_style'] = 'Moderate documentation'
        else:
            context['documentation_style'] = 'Minimal documentation'

        return context

    def _extract_html_file_context(self, file_path: Path, filename: str) -> Dict[str, str]:
        """Extract context from HTML files"""
        context: dict[str, str] = {}

        try:
            content = self._read_file_with_encoding(file_path)
            if not content:
                return context

            # Basic HTML analysis
            if 'changelog' in filename.lower():
                context['changelog_structure'] = 'HTML-formatted changelog'
                # Extract title if present
                title_match = re.search(r'<title>(.*?)</title>', content, re.IGNORECASE)
                if title_match:
                    context['product_overview'] = f"HTML changelog: {title_match.group(1)}"

            context['documentation_style'] = 'HTML-based documentation'

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error extracting HTML context: {e}")

        return context

    def _extract_json_file_context(self, file_path: Path, filename: str) -> Dict[str, str]:
        """Extract context from JSON files (package.json, etc.)"""
        context: dict[str, str] = {}

        try:
            content = self._read_file_with_encoding(file_path)
            if not content:
                return context

            data = json.loads(content)

            if filename == 'package.json':
                # Extract Node.js project information
                if 'description' in data:
                    context['product_overview'] = data['description'][:300]
                if 'keywords' in data:
                    context['key_features'] = ', '.join(data['keywords'][:10])
                if 'dependencies' in data or 'devDependencies' in data:
                    context['technical_stack'] = 'Node.js/JavaScript project'
                if 'scripts' in data:
                    scripts = list(data['scripts'].keys())[:5]
                    context['deployment_info'] = f"Available scripts: {', '.join(scripts)}"

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error extracting JSON context: {e}")

        return context

    def _extract_config_file_context(self, file_path: Path, filename: str) -> Dict[str, str]:
        """Extract context from configuration files (TOML, YAML)"""
        context: Dict[str, str] = {}

        try:
            content = self._read_file_with_encoding(file_path)
            if not content:
                return context

            # Basic analysis for common config files
            if 'pyproject.toml' in filename:
                context['technical_stack'] = 'Python project with modern packaging'
            elif 'cargo.toml' in filename:
                context['technical_stack'] = 'Rust project'
            elif filename.endswith('.yml') or filename.endswith('.yaml'):
                context['technical_stack'] = 'Uses YAML configuration'

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error extracting config context: {e}")

        return context

    def _extract_setup_py_context(self, file_path: Path) -> Dict[str, str]:
        """Extract context from setup.py files"""
        context: dict[str, str] = {}

        try:
            content = self._read_file_with_encoding(file_path)
            if not content:
                return context

            # Extract description
            desc_match = re.search(r'description\s*=\s*["\']([^"\']+)["\']', content)
            if desc_match:
                context['product_overview'] = desc_match.group(1)[:300]

            # Extract keywords
            keywords_match = re.search(r'keywords\s*=\s*\[([^\]]+)\]', content)
            if keywords_match:
                context['key_features'] = keywords_match.group(1).replace('"', '').replace("'", '')[:200]

            context['technical_stack'] = 'Python project with setuptools'

        except Exception as e:
            logging.getLogger(__name__).debug(f"Error extracting setup.py context: {e}")

        return context

    def _merge_context(self, main_context: Dict[str, str], file_context: Dict[str, str]) -> None:
        """Merge file context into main context"""
        for key, value in file_context.items():
            if key in main_context and main_context[key]:
                # Combine existing content with new content
                if len(main_context[key]) < 400:  # Only combine if not too long
                    main_context[key] = f"{main_context[key]}. {value}"[:500]
            else:
                # Add new content
                main_context[key] = value

    def _clean_and_summarize_context(self, context: Dict[str, str]) -> Dict[str, str]:
        """Clean up and summarize the extracted context"""
        cleaned_context = {}

        for key, value in context.items():
            if value and isinstance(value, str):
                # Clean up the text
                cleaned_value = re.sub(r'\s+', ' ', value.strip())
                cleaned_value = re.sub(r'[^\w\s\-\.,;:()\[\]{}]', '', cleaned_value)

                # Limit length
                if len(cleaned_value) > 500:
                    cleaned_value = cleaned_value[:497] + "..."

                if cleaned_value:
                    cleaned_context[key] = cleaned_value

        return cleaned_context
