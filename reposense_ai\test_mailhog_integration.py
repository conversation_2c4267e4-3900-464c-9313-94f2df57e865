#!/usr/bin/env python3
"""
Test script to verify MailHog integration with RepoSense AI
"""

import smtplib
import requests
import json
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

def test_mailhog_smtp():
    """Test SMTP connection to MailHog"""
    print("🧪 Testing MailHog SMTP connection...")
    
    try:
        # MailHog SMTP settings
        smtp_host = "localhost"  # Use localhost when running outside Docker
        smtp_port = 1025
        
        # Create test email
        msg = MIMEMultipart()
        msg['From'] = "reposense-ai@localhost"
        msg['To'] = "<EMAIL>"
        msg['Subject'] = f"RepoSense AI Test Email - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        body = """
        This is a test email from RepoSense AI to verify MailHog integration.
        
        Test Details:
        - Timestamp: {timestamp}
        - SMTP Host: {host}
        - SMTP Port: {port}
        - Purpose: Integration testing
        
        If you see this email in MailHog, the integration is working correctly!
        
        Best regards,
        RepoSense AI Test Suite
        """.format(
            timestamp=datetime.now().isoformat(),
            host=smtp_host,
            port=smtp_port
        )
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email via MailHog SMTP
        with smtplib.SMTP(smtp_host, smtp_port) as server:
            # MailHog doesn't require authentication
            server.send_message(msg)
        
        print("✅ Email sent successfully to MailHog!")
        return True
        
    except Exception as e:
        print(f"❌ SMTP test failed: {e}")
        return False

def test_mailhog_api():
    """Test MailHog API to retrieve emails"""
    print("🧪 Testing MailHog API...")
    
    try:
        # MailHog API endpoint
        api_url = "http://localhost:8025/api/v1/messages"
        
        # Get messages from MailHog
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        # MailHog API returns a list directly, not a dict with count/items
        if isinstance(data, list):
            messages = data
            message_count = len(messages)
        else:
            message_count = data.get('count', 0)
            messages = data.get('items', [])
        
        print(f"✅ MailHog API accessible - {message_count} messages found")
        
        # Show recent messages
        if messages:
            print("\n📧 Recent messages:")
            for i, msg in enumerate(messages[:3]):  # Show last 3 messages
                created = msg.get('Created', 'Unknown')
                from_addr = msg.get('From', {}).get('Mailbox', 'Unknown') + '@' + msg.get('From', {}).get('Domain', 'Unknown')
                to_addr = msg.get('To', [{}])[0].get('Mailbox', 'Unknown') + '@' + msg.get('To', [{}])[0].get('Domain', 'Unknown')
                subject = msg.get('Content', {}).get('Headers', {}).get('Subject', ['No Subject'])[0]
                
                print(f"  {i+1}. From: {from_addr}")
                print(f"     To: {to_addr}")
                print(f"     Subject: {subject}")
                print(f"     Created: {created}")
                print()
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ MailHog API not accessible - is MailHog running?")
        return False
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_mailhog_web_interface():
    """Test MailHog web interface accessibility"""
    print("🧪 Testing MailHog web interface...")
    
    try:
        # MailHog web interface
        web_url = "http://localhost:8025"
        
        response = requests.get(web_url, timeout=10)
        response.raise_for_status()
        
        if "MailHog" in response.text:
            print("✅ MailHog web interface accessible")
            print(f"   Access at: {web_url}")
            return True
        else:
            print("❌ MailHog web interface returned unexpected content")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ MailHog web interface not accessible")
        print("   Make sure MailHog is running: docker-compose up -d")
        return False
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        return False

def run_integration_test():
    """Run complete MailHog integration test"""
    print("🚀 RepoSense AI MailHog Integration Test")
    print("=" * 50)
    
    tests = [
        ("MailHog Web Interface", test_mailhog_web_interface),
        ("MailHog API", test_mailhog_api),
        ("MailHog SMTP", test_mailhog_smtp),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! MailHog integration is working correctly.")
        print("\nNext steps:")
        print("1. Access MailHog web interface: http://localhost:8025")
        print("2. Configure RepoSense AI to use MailHog")
        print("3. Test email notifications in RepoSense AI")
    else:
        print("\n⚠️  Some tests failed. Check the following:")
        print("1. Is Docker Compose running? (docker-compose up -d)")
        print("2. Is MailHog container running? (docker ps | grep mailhog)")
        print("3. Are ports 1025 and 8025 available?")
    
    return passed == total

if __name__ == "__main__":
    success = run_integration_test()
    exit(0 if success else 1)
