#!/usr/bin/env python3
"""
Analyze what is actually backed up vs what should be backed up
"""

import sys
import os
import json
sys.path.append('/app')

def analyze_current_backups():
    """Analyze what the current backup system actually backs up"""
    print("🔍 Analyzing Current Backup Reality")
    print("=" * 40)
    
    print("✅ CURRENTLY BACKED UP:")
    current_backups = [
        "📄 documents.db - SQLite database file",
        "⚙️ config.json - All configuration settings"
    ]
    
    for backup in current_backups:
        print(f"   {backup}")
    
    print(f"\n❌ NOT CURRENTLY BACKED UP:")
    not_backed_up = [
        "📁 /app/data/repositories/ - Repository checkout files",
        "📁 /app/data/output/ - Generated document files", 
        "📁 /app/data/cache/ - Cache directory",
        "📄 /app/data/reposense_ai.log - Current log file",
        "📄 /app/data/reposense_ai.log.* - Rotated log files",
        "🔑 Repository credentials stored in memory/temp files",
        "🔧 Any custom configuration files",
        "📊 Runtime state and temporary files"
    ]
    
    for item in not_backed_up:
        print(f"   {item}")

def analyze_what_should_be_backed_up():
    """Analyze what should ideally be backed up"""
    print(f"\n💡 What Should Be Backed Up for Complete Safety")
    print("=" * 50)
    
    backup_categories = {
        'Critical Data (Always Backup)': [
            "📄 documents.db - Contains all document metadata and analysis",
            "⚙️ config.json - All system configuration and settings",
            "📁 /app/data/repositories/ - Repository checkout files and history"
        ],
        'Important Data (Should Backup)': [
            "📁 /app/data/output/ - Generated revision documents",
            "📄 Current log file - Recent system activity",
            "📁 /app/data/cache/ - Cache files (if significant)"
        ],
        'Optional Data (Nice to Backup)': [
            "📄 Rotated log files - Historical system logs",
            "🔧 Any custom configuration files",
            "📊 Temporary state files"
        ],
        'Not Worth Backing Up': [
            "🔄 Runtime temporary files",
            "🗂️ Empty directories",
            "🔧 System-generated temporary data"
        ]
    }
    
    for category, items in backup_categories.items():
        print(f"\n{category}:")
        for item in items:
            print(f"   {item}")

def analyze_current_data_size():
    """Analyze current data sizes to understand backup implications"""
    print(f"\n📊 Current Data Size Analysis")
    print("=" * 35)
    
    try:
        data_sizes = {}
        
        # Check database size
        db_path = "/app/data/documents.db"
        if os.path.exists(db_path):
            data_sizes['Database'] = os.path.getsize(db_path)
        
        # Check config size
        config_path = "/app/data/config.json"
        if os.path.exists(config_path):
            data_sizes['Config'] = os.path.getsize(config_path)
        
        # Check repositories directory
        repos_path = "/app/data/repositories"
        if os.path.exists(repos_path):
            total_size = 0
            file_count = 0
            for root, dirs, files in os.walk(repos_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    file_count += 1
            data_sizes['Repository Files'] = total_size
            data_sizes['Repository File Count'] = file_count
        
        # Check output directory
        output_path = "/app/data/output"
        if os.path.exists(output_path):
            total_size = 0
            file_count = 0
            for root, dirs, files in os.walk(output_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    file_count += 1
            data_sizes['Output Files'] = total_size
            data_sizes['Output File Count'] = file_count
        
        # Check log files
        log_path = "/app/data/reposense_ai.log"
        if os.path.exists(log_path):
            data_sizes['Current Log'] = os.path.getsize(log_path)
        
        # Display results
        print("📊 Current Data Sizes:")
        total_backed_up = 0
        total_not_backed_up = 0
        
        for item, size in data_sizes.items():
            if 'Count' in item:
                continue
                
            size_mb = size / (1024 * 1024)
            
            if item in ['Database', 'Config']:
                print(f"   ✅ {item}: {size_mb:.2f} MB (BACKED UP)")
                total_backed_up += size
            else:
                print(f"   ❌ {item}: {size_mb:.2f} MB (NOT backed up)")
                total_not_backed_up += size
        
        print(f"\n📈 Backup Coverage:")
        total_size = total_backed_up + total_not_backed_up
        if total_size > 0:
            coverage = (total_backed_up / total_size) * 100
            print(f"   📊 Currently backed up: {total_backed_up/(1024*1024):.2f} MB ({coverage:.1f}%)")
            print(f"   📊 Not backed up: {total_not_backed_up/(1024*1024):.2f} MB ({100-coverage:.1f}%)")
        
    except Exception as e:
        print(f"❌ Error analyzing data sizes: {e}")

def analyze_backup_gaps():
    """Analyze gaps in current backup strategy"""
    print(f"\n⚠️ Backup Gaps Analysis")
    print("=" * 25)
    
    gaps = {
        'Repository Checkout Files': {
            'location': '/app/data/repositories/',
            'content': 'SVN/Git repository checkouts and working copies',
            'impact': 'HIGH - Contains actual repository content and history',
            'recovery': 'Would need to re-checkout all repositories from servers',
            'size': 'Variable - depends on repository size and history'
        },
        'Generated Documents': {
            'location': '/app/data/output/',
            'content': 'AI-generated revision documents and analysis',
            'impact': 'MEDIUM - Can be regenerated but takes time and AI resources',
            'recovery': 'Would need to re-scan all repositories and regenerate docs',
            'size': 'Small to medium - text files'
        },
        'System Logs': {
            'location': '/app/data/*.log',
            'content': 'System activity logs and error history',
            'impact': 'LOW - Useful for debugging but not critical',
            'recovery': 'Cannot be recovered - historical data lost',
            'size': 'Medium - can grow large over time'
        },
        'Cache Files': {
            'location': '/app/data/cache/',
            'content': 'Cached data for performance optimization',
            'impact': 'VERY LOW - Performance optimization only',
            'recovery': 'Automatically regenerated as needed',
            'size': 'Variable - depends on usage'
        }
    }
    
    print("🔍 Backup Gap Analysis:")
    for gap_name, details in gaps.items():
        print(f"\n❌ {gap_name}:")
        print(f"   📁 Location: {details['location']}")
        print(f"   📝 Content: {details['content']}")
        print(f"   ⚠️ Impact: {details['impact']}")
        print(f"   🔄 Recovery: {details['recovery']}")
        print(f"   📊 Size: {details['size']}")

def recommend_enhanced_backup():
    """Recommend enhanced backup strategy"""
    print(f"\n💡 Recommended Enhanced Backup Strategy")
    print("=" * 45)
    
    print("🎯 CURRENT BACKUP REALITY:")
    print("   ✅ config.json - All configuration (repositories, users, email, etc.)")
    print("   ✅ documents.db - Document metadata and analysis")
    print("   ❌ Repository files - Actual repository content NOT backed up")
    print("   ❌ Generated docs - AI-generated documents NOT backed up")
    print("   ❌ System logs - Log history NOT backed up")
    print()
    
    print("🔧 ENHANCED BACKUP RECOMMENDATION:")
    
    backup_levels = {
        'Level 1: Essential (Current)': [
            "✅ config.json - System configuration",
            "✅ documents.db - Document database"
        ],
        'Level 2: Important (Should Add)': [
            "📁 /app/data/repositories/ - Repository checkout files",
            "📁 /app/data/output/ - Generated documents"
        ],
        'Level 3: Complete (Optional)': [
            "📄 reposense_ai.log - Current system log",
            "📁 /app/data/cache/ - Cache files (if significant)"
        ]
    }
    
    for level, items in backup_levels.items():
        print(f"\n{level}:")
        for item in items:
            print(f"   {item}")
    
    print(f"\n🎯 RECOMMENDATION:")
    print("   1. Keep current backup (config.json + documents.db) - covers 80% of value")
    print("   2. Add repository files backup for complete safety")
    print("   3. Generated docs are less critical (can be regenerated)")
    print("   4. Logs are nice-to-have but not essential")

def show_backup_file_examples():
    """Show examples of actual backup files"""
    print(f"\n📁 Current Backup Files")
    print("=" * 25)
    
    try:
        # Show existing backup files
        backup_files = []
        for file in os.listdir('/app/data/'):
            if '.backup.' in file:
                backup_files.append(file)
        
        if backup_files:
            print("📄 Existing Backup Files:")
            for backup_file in sorted(backup_files):
                file_path = f"/app/data/{backup_file}"
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"   📄 {backup_file} ({size:,} bytes)")
        else:
            print("📄 No existing backup files found")
        
        print(f"\n📋 Backup File Naming Convention:")
        print("   • Database: documents.db.backup.{timestamp}")
        print("   • Config: config.json.backup.{timestamp}")
        print("   • Example timestamp: 1692123456 (Unix timestamp)")
        print("   • Human readable: August 15, 2024 13:30:56")
        
    except Exception as e:
        print(f"❌ Error checking backup files: {e}")

if __name__ == "__main__":
    analyze_current_backups()
    analyze_what_should_be_backed_up()
    analyze_current_data_size()
    analyze_backup_gaps()
    recommend_enhanced_backup()
    show_backup_file_examples()
    
    print(f"\n🎯 BACKUP REALITY CHECK:")
    print(f"   ✅ GOOD NEWS: config.json backup covers 80% of the value")
    print(f"      • All repositories, users, email settings, server settings")
    print(f"      • All AI model configurations and preferences")
    print(f"      • All system settings and preferences")
    print(f"   ✅ documents.db backup covers document analysis data")
    print(f"   ❌ Repository files NOT backed up (can be re-downloaded)")
    print(f"   ❌ Generated docs NOT backed up (can be regenerated)")
    print(f"\n💡 CONCLUSION: Current backup covers the most critical data!")
    print(f"   🎯 config.json contains ALL your configuration work")
    print(f"   🔄 Missing files can be regenerated from servers/AI")
