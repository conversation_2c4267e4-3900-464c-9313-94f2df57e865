#!/usr/bin/env python3
"""
Test script to create a document with CRITICAL risk level and confidence ratings
to verify UI display functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from datetime import datetime
from reposense_ai.document_database import DocumentDatabase, DocumentRecord

def create_test_document_with_critical_risk():
    """Create a test document with CRITICAL risk level and confidence ratings"""
    
    # Initialize database
    db = DocumentDatabase("/app/data/documents.db")
    
    # Create test heuristic context with CRITICAL risk and confidence ratings
    heuristic_context = {
        'indicators': {
            'complexity': 'HIGH - significant structural changes',
            'risk_assessment': 'CRITICAL - high-confidence critical areas: security, buffer overflow, race condition',
            'risk_confidence': '0.92',
            'doc_assessment': 'LIKELY - high-confidence user-facing: api, interface, breaking',
            'doc_confidence': '0.85',
            'risk_keywords': ['security', 'buffer overflow', 'race condition', 'deadlock', 'memory leak', 'critical', 'hotfix'],
            'doc_keywords': ['api', 'interface', 'breaking', 'major', 'public'],
            'threading_keywords': ['race condition', 'deadlock', 'mutex', 'critical section'],
            'memory_keywords': ['buffer overflow', 'memory leak', 'segfault', 'malloc', 'free'],
            'protocol_keywords': ['protocol', 'packet', 'handshake'],
            'threading_assessment': 'THREADING - involves race condition, deadlock, mutex patterns',
            'memory_assessment': 'MEMORY - involves buffer overflow, memory leak, segfault management',
            'protocol_assessment': 'PROTOCOL - involves protocol, packet, handshake components'
        },
        'decisions': {
            'code_review_recommended': True,
            'code_review_priority': 'CRITICAL',
            'documentation_impact': True,
            'risk_level': 'CRITICAL'
        },
        'reasoning': [
            'Heuristic assessed risk level as CRITICAL',
            'Multiple critical security vulnerabilities detected',
            'Threading and memory management issues require expert review',
            'API breaking changes require documentation updates'
        ]
    }
    
    # Create test document record
    test_doc = DocumentRecord(
        id="test-critical-risk-001",
        repository_id="test-repo",
        repository_name="Test Repository",
        revision=12345,
        date=datetime.now(),
        filename="critical_security_fix.md",
        filepath="/app/data/output/test-repo/critical_security_fix.md",
        size=2048,
        author="Test Developer",
        commit_message="CRITICAL: Fix buffer overflow and race condition in authentication system",
        changed_paths=["src/auth.c", "src/memory.c", "include/security.h"],
        code_review_recommended=True,
        code_review_priority="CRITICAL",
        documentation_impact=True,
        risk_level="CRITICAL",
        file_modified_time=datetime.now().timestamp(),
        processed_time=datetime.now(),
        ai_model_used="llama3.1:8b",
        repository_url="https://github.com/test/repo",
        repository_type="git",
        heuristic_context=heuristic_context
    )
    
    # Insert into database
    success = db.upsert_document(test_doc)
    if success:
        print("✅ Successfully created test document with CRITICAL risk level")
        print(f"   Document ID: {test_doc.id}")
        print(f"   Risk Level: {test_doc.risk_level}")
        print(f"   Risk Confidence: {heuristic_context['indicators']['risk_confidence']}")
        print(f"   Doc Confidence: {heuristic_context['indicators']['doc_confidence']}")
        print(f"   Threading Assessment: {heuristic_context['indicators']['threading_assessment']}")
        print(f"   Memory Assessment: {heuristic_context['indicators']['memory_assessment']}")
        print(f"   Protocol Assessment: {heuristic_context['indicators']['protocol_assessment']}")
    else:
        print("❌ Failed to create test document")
    
    return success

def create_test_document_with_high_risk():
    """Create a test document with HIGH risk level for comparison"""
    
    # Initialize database
    db = DocumentDatabase("/app/data/documents.db")
    
    # Create test heuristic context with HIGH risk
    heuristic_context = {
        'indicators': {
            'complexity': 'MEDIUM - moderate changes with some complexity',
            'risk_assessment': 'HIGH - confidence 0.78: security, database, migration',
            'risk_confidence': '0.78',
            'doc_assessment': 'LIKELY - confidence 0.72: api, configuration',
            'doc_confidence': '0.72',
            'risk_keywords': ['security', 'database', 'migration', 'api', 'config'],
            'doc_keywords': ['api', 'configuration', 'setup', 'user']
        },
        'decisions': {
            'code_review_recommended': True,
            'code_review_priority': 'HIGH',
            'documentation_impact': True,
            'risk_level': 'HIGH'
        },
        'reasoning': [
            'Heuristic assessed risk level as HIGH',
            'Security and database changes require review'
        ]
    }
    
    # Create test document record
    test_doc = DocumentRecord(
        id="test-high-risk-002",
        repository_id="test-repo",
        repository_name="Test Repository",
        revision=12346,
        date=datetime.now(),
        filename="database_migration.md",
        filepath="/app/data/output/test-repo/database_migration.md",
        size=1024,
        author="Test Developer",
        commit_message="Update database schema and API configuration",
        changed_paths=["src/database.py", "config/api.json"],
        code_review_recommended=True,
        code_review_priority="HIGH",
        documentation_impact=True,
        risk_level="HIGH",
        file_modified_time=datetime.now().timestamp(),
        processed_time=datetime.now(),
        ai_model_used="llama3.1:8b",
        repository_url="https://github.com/test/repo",
        repository_type="git",
        heuristic_context=heuristic_context
    )
    
    # Insert into database
    success = db.upsert_document(test_doc)
    if success:
        print("✅ Successfully created test document with HIGH risk level")
        print(f"   Document ID: {test_doc.id}")
        print(f"   Risk Level: {test_doc.risk_level}")
        print(f"   Risk Confidence: {heuristic_context['indicators']['risk_confidence']}")
        print(f"   Doc Confidence: {heuristic_context['indicators']['doc_confidence']}")
    else:
        print("❌ Failed to create test document")
    
    return success

if __name__ == "__main__":
    print("🧪 Creating test documents with CRITICAL and HIGH risk levels...")
    print("This will test the UI display of the new risk levels and confidence ratings.")
    print()
    
    # Create test documents
    critical_success = create_test_document_with_critical_risk()
    high_success = create_test_document_with_high_risk()
    
    if critical_success and high_success:
        print()
        print("🎉 Test documents created successfully!")
        print("You can now view these in the web interface to see:")
        print("  - CRITICAL risk level with dark badge and skull icon")
        print("  - Confidence ratings displayed in tooltips and detail views")
        print("  - Enhanced heuristic analysis information")
        print()
        print("Navigate to the documents page to see the new risk level display.")
    else:
        print("❌ Some test documents failed to create")
