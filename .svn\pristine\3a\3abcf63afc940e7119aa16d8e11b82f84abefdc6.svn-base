# RepoSense AI Environment Variables (Optional)
# 
# ⚠️  IMPORTANT: Environment variables are now OPTIONAL!
# 
# RepoSense AI uses the web interface for primary configuration.
# Only use these environment variables for deployment overrides.
# 
# Most users should configure via the web interface at http://localhost:5000

# =============================================================================
# DEPLOYMENT OVERRIDES (Optional)
# =============================================================================

# Uncomment only if you need to override web interface configuration:

# Ollama server URL override:
# OLLAMA_BASE_URL=http://localhost:11434

# Ollama model override:
# OLLAMA_MODEL=qwen3

# Web interface overrides (for deployment):
# REPOSENSE_AI_WEB_HOST=0.0.0.0
# REPOSENSE_AI_WEB_PORT=5000

# =============================================================================
# DEVELOPMENT (Optional)
# =============================================================================

# Uncomment for development mode with enhanced debugging:
# REPOSENSE_AI_LOG_LEVEL=DEBUG
# REPOSENSE_AI_DB_DEBUG=true
# FLASK_DEBUG=1
# PYTHONUNBUFFERED=1
