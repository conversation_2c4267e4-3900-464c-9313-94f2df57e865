#!/bin/bash
# RepoSense AI Integration Setup Script
# This script helps prepare RepoSense AI for integration into a larger Docker Compose setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 RepoSense AI Integration Setup${NC}"
echo "This script will prepare RepoSense AI for integration into your larger Docker Compose setup."
echo

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "reposense_ai" ]; then
    log_error "reposense_ai directory not found. Please run this script from the parent directory."
    exit 1
fi

log_step "1. Checking directory structure..."

# Ensure required directories exist
mkdir -p reposense_ai/data
mkdir -p reposense_ai/logs
mkdir -p reposense_ai/data/output
mkdir -p reposense_ai/data/cache

log_info "Directory structure verified"

log_step "2. Updating configuration for Docker integration..."

# Update config.json to use Docker service names
CONFIG_FILE="reposense_ai/data/config.json"
if [ -f "$CONFIG_FILE" ]; then
    # Backup original config
    cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%s)"
    log_info "Backed up original config.json"
    
    # Update ollama_host to use Docker service name
    if command -v jq >/dev/null 2>&1; then
        jq '.ollama_host = "http://ollama:11434"' "$CONFIG_FILE" > "$CONFIG_FILE.tmp" && mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"
        log_info "Updated ollama_host to use Docker service name"
    else
        log_warn "jq not found - please manually update ollama_host in config.json to 'http://ollama:11434'"
    fi
else
    log_warn "config.json not found - will be created on first run"
fi

log_step "3. Creating integration files..."

# Create .env template for easy configuration
cat > .env.template << 'EOF'
# RepoSense AI Environment Configuration
# Copy this file to .env and customize as needed

# Logging
REPOSENSE_AI_LOG_LEVEL=INFO
REPOSENSE_AI_DB_DEBUG=false

# AI Model Configuration (optional overrides)
# REPOSENSE_AI_MODEL=qwen3:14b
# OLLAMA_BASE_URL=http://ollama:11434

# Development settings
# REPOSENSE_AI_DEV_MODE=true
EOF

log_info "Created .env.template"

# Create integration documentation
cat > INTEGRATION.md << 'EOF'
# RepoSense AI Integration Guide

## Directory Structure
```
your-project/
├── docker-compose.yml          # Your main docker-compose file
├── reposense_ai/              # RepoSense AI application
│   ├── data/                  # Persistent data (databases, config)
│   ├── logs/                  # Application logs
│   └── ...                    # Application files
└── Dockerfile                 # RepoSense AI Dockerfile
```

## Integration Steps

1. **Add RepoSense AI service to your docker-compose.yml:**
   Copy the service definition from `docker-compose-integrated.yml`

2. **Configure environment variables:**
   Copy `.env.template` to `.env` and customize as needed

3. **Update network configuration:**
   Ensure RepoSense AI uses the `ollama-network` to communicate with Ollama

4. **Start the services:**
   ```bash
   docker-compose up -d
   ```

## Configuration

RepoSense AI will automatically connect to your Ollama service using the Docker service name `ollama:11434`.

The web interface will be available at: http://localhost:5000

## Data Persistence

- Database: `./reposense_ai/data/documents.db`
- Configuration: `./reposense_ai/data/config.json`
- Logs: `./reposense_ai/logs/`
- Output files: `./reposense_ai/data/output/`

## Troubleshooting

1. **Connection issues with Ollama:**
   - Verify both services are on the same network (`ollama-network`)
   - Check that Ollama is running and accessible

2. **Permission issues:**
   - Ensure the `reposense_ai/data` directory is writable
   - Run: `sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs`

3. **Database issues:**
   - Check logs in `reposense_ai/logs/`
   - Verify database file permissions in `reposense_ai/data/`
EOF

log_info "Created INTEGRATION.md"

log_step "4. Setting up permissions..."

# Set proper permissions for data directories
# This is critical for Docker containers running as non-root user
log_info "Setting up Docker-compatible permissions..."

# Create directories if they don't exist
mkdir -p reposense_ai/data reposense_ai/logs
mkdir -p reposense_ai/data/output reposense_ai/data/cache

# Set ownership to uid 1000 (appuser in container)
if command -v sudo >/dev/null 2>&1; then
    sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set ownership - you may need to run manually"
    sudo chmod -R 755 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set permissions"
    log_info "Set Docker-compatible ownership (uid 1000) and permissions"
else
    # Fallback for systems without sudo
    chown -R 1000:1000 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set ownership - run: sudo chown -R 1000:1000 reposense_ai/"
    chmod -R 755 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set permissions"
    log_info "Set directory permissions (ownership may need manual fix)"
fi

log_step "5. Validating setup..."

# Check if Docker and docker-compose are available
if ! command -v docker >/dev/null 2>&1; then
    log_error "Docker not found - please install Docker"
    exit 1
fi

if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
    log_error "Docker Compose not found - please install Docker Compose"
    exit 1
fi

log_info "Docker and Docker Compose are available"

echo
echo -e "${GREEN}✅ Integration setup complete!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Copy the RepoSense AI service definition from docker-compose-integrated.yml to your main docker-compose.yml"
echo "2. Ensure your main docker-compose.yml includes the ollama-network"
echo "3. Copy .env.template to .env and customize if needed"
echo "4. Run: docker-compose up -d"
echo "5. Access RepoSense AI at: http://localhost:5000"
echo
echo -e "${YELLOW}📖 See INTEGRATION.md for detailed instructions${NC}"
