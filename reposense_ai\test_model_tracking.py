#!/usr/bin/env python3
"""
Test that the new AI model tracking is working correctly
"""

import logging
from datetime import datetime
from pathlib import Path
from document_service import DocumentService
from document_database import DocumentRecord, DocumentDatabase
from monitor_service import MonitorService
from metadata_extractor import MetadataExtractor

def setup_logging():
    """Setup logging to see model usage"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_new_document_processing():
    """Test processing a new document to verify model tracking works"""
    print("🧪 Testing New Document Processing with Model Tracking")
    print("=" * 60)
    
    setup_logging()
    
    # Initialize services
    monitor_service = MonitorService("data/config.json")
    
    print(f"📋 Configuration:")
    print(f"  Default model: {monitor_service.config.ollama_model}")
    print(f"  Documentation model: {monitor_service.config.ollama_model_documentation}")
    print(f"  Code review model: {monitor_service.config.ollama_model_code_review}")
    print(f"  Risk assessment model: {monitor_service.config.ollama_model_risk_assessment}")
    
    # Create test document content
    test_content = """
    ## Code Review Recommendation
    This change introduces OAuth2 authentication with enhanced security measures for the RepoSense AI platform.
    
    ## Impact Assessment
    - Security: HIGH - New authentication mechanism with token validation
    - Performance: MEDIUM - Additional validation overhead  
    - Compatibility: LOW - Backward compatible API
    
    ## Risk Level
    HIGH RISK - Authentication changes require careful security review
    
    ## Documentation Impact
    YES - API documentation needs updates for new OAuth2 flow
    
    ## Changes Made
    - Implemented OAuth2 flow with JWT tokens
    - Added user authentication middleware
    - Updated API endpoints for token validation
    - Enhanced security logging and monitoring
    """
    
    # Create test document record
    test_doc = DocumentRecord(
        id="test-model-tracking-" + str(int(datetime.now().timestamp())),
        repository_id="test-repo",
        repository_name="reposense_ai",
        revision=999,
        date=datetime.now(),
        filename="test_model_tracking.md",
        filepath="/app/data/output/test/test_model_tracking.md",
        size=len(test_content),
        author="model_tracking_test",
        commit_message="Test OAuth2 authentication with security improvements",
        changed_paths=[
            "src/auth/oauth_handler.py",
            "src/auth/security_manager.py",
            "config/auth_config.json"
        ],
        repository_type="git"
    )
    
    print(f"\n🔍 Testing metadata extraction with model tracking...")
    
    # Test with MetadataExtractor (this should track the model used)
    try:
        metadata_extractor = MetadataExtractor(
            ollama_client=monitor_service.ollama_client,
            config_manager=monitor_service.config_manager
        )
        
        # This should use specialized models and track which one was used
        metadata = metadata_extractor.extract_all_metadata(test_content, test_doc)
        
        print(f"\n📊 Metadata extraction results:")
        print(f"  Code review recommended: {metadata.get('code_review_recommended')}")
        print(f"  Code review priority: {metadata.get('code_review_priority')}")
        print(f"  Risk level: {metadata.get('risk_level')}")
        print(f"  Documentation impact: {metadata.get('documentation_impact')}")
        print(f"  AI model used: {test_doc.ai_model_used}")
        
        if test_doc.ai_model_used:
            print(f"\n✅ SUCCESS: Model tracking is working!")
            print(f"🎯 Specialized model used: {test_doc.ai_model_used}")
            
            # Save to database to test complete workflow
            db = DocumentDatabase("data/documents.db")
            if db.upsert_document(test_doc):
                print(f"✅ Document saved to database with model tracking")
                
                # Retrieve and verify
                retrieved_doc = db.get_document_by_id(test_doc.id)
                if retrieved_doc and retrieved_doc.ai_model_used:
                    print(f"✅ Database verification: {retrieved_doc.ai_model_used}")
                    return True
                else:
                    print(f"❌ Database verification failed")
                    return False
            else:
                print(f"❌ Failed to save document to database")
                return False
        else:
            print(f"\n❌ ISSUE: Model tracking is not working - ai_model_used is still None")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_document_processing()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Model tracking is working correctly!")
        print("New documents will show the specialized model used.")
    else:
        print("❌ Model tracking needs debugging.")
