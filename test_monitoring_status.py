#!/usr/bin/env python3
"""
Test script to check monitoring status and repository configuration
"""

import sys
import os
from pathlib import Path

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from config_manager import ConfigManager
from monitor_service import MonitorService

def test_monitoring_status():
    """Test the current monitoring status"""
    print("🔍 Checking RepoSense AI Monitoring Status...")
    
    try:
        # Load configuration
        config_manager = ConfigManager('data/config.json')
        config = config_manager.load_config()
        
        print(f"\n📋 Configuration Summary:")
        print(f"  Check interval: {config.check_interval} seconds")
        print(f"  Web enabled: {config.web_enabled}")
        print(f"  Total repositories: {len(config.repositories)}")
        
        # Check enabled repositories
        enabled_repos = config.get_enabled_repositories()
        print(f"  Enabled repositories: {len(enabled_repos)}")
        
        if enabled_repos:
            print(f"\n📂 Enabled Repositories:")
            for repo in enabled_repos:
                print(f"    - {repo.name} ({repo.type})")
                print(f"      URL: {repo.url}")
                print(f"      Last revision: {repo.last_revision}")
                print(f"      Enabled: {repo.enabled}")
        else:
            print(f"\n⚠️  No repositories are configured or enabled!")
            print(f"     This is why the 'Check' button fails and no periodic scanning occurs.")
        
        # Test monitor service initialization
        print(f"\n🔧 Testing Monitor Service...")
        monitor_service = MonitorService('data/config.json')
        
        print(f"  Monitor service initialized: ✅")
        print(f"  Is running: {monitor_service.is_running()}")
        print(f"  Last check time: {monitor_service.last_check_time}")
        
        # Test what happens when we try to run a check
        print(f"\n🧪 Testing Manual Check...")
        if enabled_repos:
            print(f"  Would check {len(enabled_repos)} repositories")
            # Don't actually run the check to avoid side effects
            print(f"  Check would succeed ✅")
        else:
            print(f"  Check would fail: No repositories configured ❌")
            print(f"  This explains the 'Unknown error' message")
        
        print(f"\n💡 Recommendations:")
        if not enabled_repos:
            print(f"  1. Add repositories via the web interface")
            print(f"  2. Ensure repositories are enabled")
            print(f"  3. Verify repository URLs and credentials")
        else:
            print(f"  1. Monitoring should be working")
            print(f"  2. Check logs for any scanning activity")
            print(f"  3. Verify repository connectivity")
        
    except Exception as e:
        print(f"❌ Error testing monitoring status: {e}")
        import traceback
        traceback.print_exc()

def test_periodic_scanning_logic():
    """Test the periodic scanning logic"""
    print(f"\n🔄 Testing Periodic Scanning Logic...")
    
    try:
        # Check if monitoring would start automatically
        config_manager = ConfigManager('data/config.json')
        config = config_manager.load_config()
        enabled_repos = config.get_enabled_repositories()
        
        print(f"  Application startup logic:")
        if config.web_enabled:
            print(f"    ✅ Web interface enabled")
            if enabled_repos:
                print(f"    ✅ Would start monitoring for {len(enabled_repos)} repositories")
                print(f"    ✅ Periodic scanning every {config.check_interval} seconds")
            else:
                print(f"    ❌ No repositories configured - monitoring NOT started")
                print(f"    ❌ No periodic scanning occurs")
        else:
            print(f"    ❌ Web interface disabled")
            if enabled_repos:
                print(f"    ✅ Would run in daemon mode")
            else:
                print(f"    ❌ Would exit with error")
        
        print(f"\n📊 Current Status:")
        if enabled_repos:
            print(f"    Status: Monitoring should be active")
            print(f"    Behavior: Checking every {config.check_interval} seconds")
            print(f"    Action: Look for 'Checking for new commits...' in logs")
        else:
            print(f"    Status: No monitoring active")
            print(f"    Behavior: Only health checks and web interface")
            print(f"    Action: Configure repositories to enable monitoring")
            
    except Exception as e:
        print(f"❌ Error testing periodic scanning: {e}")

if __name__ == "__main__":
    test_monitoring_status()
    test_periodic_scanning_logic()
