# RepoSense C++ Test Project

## ⚠️ BREAKING CHANGES IN VERSION 2.0.0 ⚠️

**IMPORTANT**: Version 2.0.0 introduces significant breaking changes that require code modifications. See [API Migration Guide](docs/API_MIGRATION_GUIDE.md) for detailed upgrade instructions.

### Major Breaking Changes
- **Authentication Required**: All API calls now require valid authentication tokens
- **SSL/TLS Mandatory**: All network communications must use SSL/TLS encryption
- **Method Signatures Changed**: Core logging and configuration methods have new signatures
- **Namespace Changes**: Timer class moved to `reposense::performance` namespace
- **Protocol Incompatibility**: Version 2 protocol incompatible with version 1 clients

## Overview
This is a comprehensive C/C++ test project designed to exercise all features of the RepoSense AI system, including:

- Protocol communications software with SSL/TLS encryption
- Multi-threading and concurrency with atomic operations
- Memory management with bounds checking
- Security features with two-factor authentication
- API design with breaking changes and migrations
- Configuration management with validation

## Project Structure

```
reposense_cpp_test/
├── src/                    # Source code
│   ├── core/              # Core system components
│   ├── protocol/          # Protocol communication modules
│   ├── security/          # Security and authentication
│   ├── threading/         # Multi-threading utilities
│   └── memory/            # Memory management utilities
├── include/               # Header files
├── tests/                 # Unit tests
├── docs/                  # Documentation
├── config/                # Configuration files
└── build/                 # Build artifacts (ignored)
```

## Features

### Protocol Communications
- Custom TCP/UDP protocol implementation
- Message serialization/deserialization
- Packet parsing and validation
- Connection management

### Multi-threading
- Thread pool implementation
- Producer-consumer patterns
- Synchronization primitives
- Lock-free data structures

### Memory Management
- Custom memory allocators
- RAII patterns
- Smart pointer implementations
- Memory leak detection

### Security
- Authentication systems
- Encryption/decryption
- Input validation
- Secure communication

## Building

### Prerequisites (NEW in v2.0)
- CMake 3.16 or higher
- C++17 compatible compiler
- OpenSSL development libraries
- Valid SSL certificates for testing

### Build Instructions
```bash
mkdir build
cd build
cmake .. -DENABLE_SSL=ON -DREQUIRE_AUTH=ON
make
```

### Configuration (REQUIRED in v2.0)
Before running, create a configuration file:
```bash
cp config/app.conf.example config/app.conf
# Edit config/app.conf with your SSL certificates and authentication settings
```

## Testing

### Prerequisites
- SSL test certificates in `tests/certs/` directory
- Test authentication tokens configured

### Running Tests
```bash
cd build
make test

# Or run individual test suites
./reposense_tests --suite=core
./reposense_tests --suite=protocol --ssl-certs=../tests/certs/
./reposense_tests --suite=security --auth-token=test_token
```

### Security Testing
```bash
# Run security vulnerability tests (intentional vulnerabilities for testing)
./reposense_tests --suite=security --enable-vulnerability-tests
```

## License
MIT License - See LICENSE file for details

## Overview
This is a comprehensive C/C++ test project designed to exercise all features of the RepoSense AI system, including:

- Protocol communications software
- Multi-threading and concurrency
- Memory management
- Security features
- API design
- Configuration management

## Project Structure

```
reposense_cpp_test/
├── src/                    # Source code
│   ├── core/              # Core system components
│   ├── protocol/          # Protocol communication modules
│   ├── security/          # Security and authentication
│   ├── threading/         # Multi-threading utilities
│   └── memory/            # Memory management utilities
├── include/               # Header files
├── tests/                 # Unit tests
├── docs/                  # Documentation
├── config/                # Configuration files
└── build/                 # Build artifacts (ignored)
```

## Features

### Protocol Communications
- Custom TCP/UDP protocol implementation
- Message serialization/deserialization
- Packet parsing and validation
- Connection management

### Multi-threading
- Thread pool implementation
- Producer-consumer patterns
- Synchronization primitives
- Lock-free data structures

### Memory Management
- Custom memory allocators
- RAII patterns
- Smart pointer implementations
- Memory leak detection

### Security
- Authentication systems
- Encryption/decryption
- Input validation
- Secure communication

## Building

```bash
mkdir build
cd build
cmake ..
make
```

## Testing

```bash
cd build
make test
```

## License
MIT License - See LICENSE file for details

[![Docker](https://img.shields.io/badge/Docker-Ready-blue?logo=docker)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-green?logo=python)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## ✨ Features

### Core Functionality
- 🔒 **Private Local AI** - Your code never leaves your infrastructure (any LLM supported)
- 🔌 **Plugin Architecture** - Extensible backend system (SVN supported, Git planned)
- 🤖 **AI Documentation** - Automatic commit analysis with flexible LLM integration
- 📊 **Document Management** - Browse, search, and manage generated documentation
- 🌐 **Modern Web Interface** - Responsive UI with real-time monitoring
- 🐳 **Docker Ready** - Single-command deployment with unified Docker setup
- 🔍 **Repository Discovery** - Automatic repository detection and configuration

### Enterprise Features (v2.3.0)
- 🔒 **Complete Data Privacy** - Local AI processing with universal LLM support (Ollama, OpenAI, Claude)
- 🏢 **Enterprise Repository Management** - Advanced bulk operations with real-time status monitoring
  - Professional bulk actions (enable/disable/start/stop/reset/delete multiple repositories)
  - Real-time progress tracking with live counters and professional spinner animations
  - Duplicate prevention system with client and server-side validation
  - Multi-criteria filtering and sorting with multiple view modes (Table/Cards/Groups)
- 📊 **Advanced Document Discovery** - Comprehensive search and filtering with professional interface
  - Real-time search across commit messages, authors, and repositories
  - Multi-criteria filtering by repository, author, date range, risk level, and more
  - Multiple view modes optimized for different workflows with responsive design
  - Auto-submit functionality and keyboard shortcuts for efficient navigation
- 👥 **User Feedback System** - Code review tracking, documentation ratings, and risk assessment overrides
- 📋 **Enhanced Side-by-Side Diff Viewer** - Professional diff visualization with inline highlighting and improved formatting
- 🏗️ **Single Source of Truth Architecture** - Repository backend as primary data source with intelligent fallbacks
- 📄 **Professional Document Exports** - Enhanced PDF and Markdown downloads with complete formatting and no truncation
- 🔄 **Hybrid AI Analysis** - Fast heuristics with LLM fallback for robust metadata extraction
- 🛡️ **Robust Error Handling** - Multi-encoding support and binary file detection with graceful degradation
- 📈 **Accurate Progress Tracking** - Fixed progress calculation for all revision ranges with real-time updates
- 🔐 **Enhanced Authentication** - Seamless SVN credential integration for diff viewing

## 🚀 Quick Start

### Standalone Deployment

```bash
# Clone the repository
git clone <repository-url>
cd reposense_ai

# Start RepoSense AI (works for both development and production)
docker-compose up -d

# Access the web interface
open http://localhost:5000
```

**That's it!** 🎉 Configure everything via the web interface - no environment variables needed!

### Integration with Existing Docker Compose Setup

RepoSense AI can be easily integrated into existing Docker Compose environments (e.g., with Ollama, Open WebUI, etc.):

```bash
# Run the integration setup script
./setup-integration.sh

# Add the RepoSense AI service to your existing docker-compose.yml
# See docker-compose-full-example.yml for a complete example

# Start your integrated services
docker-compose up -d
```

📖 **See [INTEGRATION_README.md](INTEGRATION_README.md) for detailed integration instructions**

## 📚 Documentation

| Guide | Description |
|-------|-------------|
| [📖 Getting Started](docs/index.md) | Complete setup and usage guide |
| [🚀 Deployment](docs/DEPLOYMENT.md) | Simple deployment guide |
| [🐳 Docker Guide](docs/DOCKER_README.md) | Unified Docker setup |
| [🔧 Quick Configuration](docs/quick-configuration.md) | Web interface configuration |
| [🏗️ Architecture](docs/design.md) | System design and plugin development |
| [🔗 Integration](docs/integration.md) | External service integration guide |

## 🛠️ System Requirements

- **Docker & Docker Compose** (recommended)
- **Python 3.8+** (for local development)
- **Git** (for cloning and development)

## 🏗️ Architecture

RepoSense AI uses a modern plugin-based architecture:

```
┌─────────────────────────────────────┐
│           Web Interface             │
├─────────────────────────────────────┤
│         Document Management         │
├─────────────────────────────────────┤
│          Monitor Service            │
├─────────────────────────────────────┤
│       Repository Backends           │
│    ┌─────────┐  ┌─────────────┐     │
│    │   SVN   │  │ Git (Future) │     │
│    └─────────┘  └─────────────┘     │
└─────────────────────────────────────┘
```

## 🔧 Configuration

Simple JSON configuration:

```json
{
  "repositories": [
    {
      "id": "my-project",
      "url": "http://svn-server/svn/project",
      "enabled": true
    }
  ],
  "ollama": {
    "host": "localhost",
    "port": 11434,
    "model": "qwen3"
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

See [Development Guide](docs/development.md) for detailed instructions.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Ollama](https://ollama.ai/) for AI integration
- [Flask](https://flask.palletsprojects.com/) for web framework
- [Bootstrap](https://getbootstrap.com/) for UI components
