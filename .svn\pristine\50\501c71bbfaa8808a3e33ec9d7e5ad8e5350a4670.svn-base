#!/usr/bin/env python3
"""
Metadata Extractor Service
Centralized service for extracting metadata from documentation content.
Consolidates logic previously duplicated across HistoricalScanner, DocumentProcessor, and DocumentService.
"""

import logging
import re
import json
from datetime import datetime
from typing import Dict, Any, Optional, TYPE_CHECKING, Tuple
from pathlib import Path

if TYPE_CHECKING:
    from document_database import DocumentRecord


class MetadataExtractor:
    """Centralized metadata extraction service with hybrid heuristic + LLM approach"""
    
    def __init__(self, ollama_client=None, config_manager=None):
        """
        Initialize metadata extractor

        Args:
            ollama_client: Optional Ollama client for LLM fallback
            config_manager: Optional config manager for enhanced prompts
        """
        self.logger = logging.getLogger(__name__)
        self.ollama_client = ollama_client
        self.config_manager = config_manager
    
    def extract_all_metadata(self, documentation: str, document_record: Optional['DocumentRecord'] = None) -> Dict[str, Any]:
        """Extract all metadata using heuristics to prime LLM for better decisions"""
        metadata: Dict[str, Any] = {}

        try:
            # Always run heuristic analysis first to gather context indicators
            self.logger.debug("Running heuristic analysis to gather context indicators...")
            heuristic_context = self._gather_heuristic_context(documentation, document_record)

            # Use LLM with heuristic context when available (most accurate)
            if self.ollama_client and document_record:
                self.logger.info(f"🎯 Using LLM with heuristic context for document {document_record.id}")
                llm_metadata, model_used = self._extract_metadata_with_llm_enhanced(
                    documentation, document_record, heuristic_context
                )

                # Store the model used for this analysis
                if model_used and document_record:
                    document_record.ai_model_used = model_used
                    self.logger.info(f"🎯 Stored AI model used in document record: {model_used}")

                # Use LLM results as primary source if successful
                if llm_metadata:
                    for field, value in llm_metadata.items():
                        if value is not None:
                            metadata[field] = value
                            self.logger.debug(f"LLM (with context) provided {field}: {value}")

                    # Extract priority if review is recommended and not provided by LLM
                    if metadata.get('code_review_recommended') and 'code_review_priority' not in metadata:
                        priority = self.extract_code_review_priority(documentation, document_record)
                        if priority:
                            metadata['code_review_priority'] = priority
                else:
                    # LLM failed, use heuristic results as fallback
                    self.logger.warning("LLM extraction failed, falling back to heuristic results")
                    if heuristic_context and 'decisions' in heuristic_context:
                        metadata = heuristic_context['decisions'].copy()
                        self.logger.info(f"Using heuristic fallback decisions: {metadata}")

            else:
                # No LLM available - use heuristic results directly
                self.logger.debug("No LLM available, using heuristic results directly")
                if heuristic_context and 'decisions' in heuristic_context:
                    metadata = heuristic_context['decisions'].copy()
                    self.logger.info(f"Using heuristic fallback decisions: {metadata}")
                else:
                    self.logger.warning("No heuristic context available for fallback")

        except Exception as e:
            self.logger.error(f"Error extracting document metadata: {e}")

        return metadata

    def _gather_heuristic_context(self, documentation: str, document_record: Optional['DocumentRecord'] = None) -> Dict[str, Any]:
        """Gather heuristic context indicators to prime the LLM for better decisions"""
        context: Dict[str, Any] = {
            'indicators': {},
            'decisions': {},
            'reasoning': []
        }

        try:
            # Extract heuristic decisions
            code_review_rec = self.extract_code_review_recommendation(documentation, document_record)
            doc_impact = self.extract_documentation_impact(documentation, document_record)
            risk_level = self.extract_risk_level(documentation, document_record)

            # Store decisions for fallback
            if code_review_rec is not None:
                context['decisions']['code_review_recommended'] = code_review_rec
                if code_review_rec:
                    priority = self.extract_code_review_priority(documentation, document_record)
                    if priority:
                        context['decisions']['code_review_priority'] = priority

            if doc_impact is not None:
                context['decisions']['documentation_impact'] = doc_impact

            if risk_level:
                context['decisions']['risk_level'] = risk_level

            # Gather context indicators for LLM
            indicators = {}

            # Complexity indicators
            if 'new' in documentation.lower() and ('function' in documentation.lower() or 'class' in documentation.lower()):
                indicators['complexity'] = 'HIGH - adds new functions/classes'
            elif 'simple' in documentation.lower() or 'minimal' in documentation.lower():
                indicators['complexity'] = 'LOW - simple changes'
            else:
                indicators['complexity'] = 'MEDIUM - moderate changes'

            # Risk indicators
            risk_keywords = ['security', 'authentication', 'database', 'migration', 'config', 'production']
            found_risk_keywords = [kw for kw in risk_keywords if kw in documentation.lower()]
            if found_risk_keywords:
                indicators['risk_keywords'] = found_risk_keywords
                indicators['risk_assessment'] = f'HIGH - affects {", ".join(found_risk_keywords)}'
            else:
                indicators['risk_assessment'] = 'LOW - no high-risk areas detected'

            # Documentation impact indicators
            doc_keywords = ['api', 'interface', 'public', 'user', 'configuration', 'setup', 'install']
            found_doc_keywords = [kw for kw in doc_keywords if kw in documentation.lower()]
            if found_doc_keywords:
                indicators['doc_keywords'] = found_doc_keywords
                indicators['doc_assessment'] = f'LIKELY - affects {", ".join(found_doc_keywords)}'
            else:
                indicators['doc_assessment'] = 'UNLIKELY - internal changes only'

            # File type indicators (if available from document_record)
            if document_record and hasattr(document_record, 'filename'):
                filename = document_record.filename.lower()
                if any(ext in filename for ext in ['.py', '.js', '.java', '.cpp']):
                    indicators['file_type'] = 'CODE - source code changes'
                elif any(ext in filename for ext in ['.md', '.txt', '.rst']):
                    indicators['file_type'] = 'DOCS - documentation changes'
                elif any(ext in filename for ext in ['.json', '.yaml', '.xml', '.conf']):
                    indicators['file_type'] = 'CONFIG - configuration changes'

            context['indicators'] = indicators

            # Build reasoning summary
            reasoning = []
            if code_review_rec is True:
                reasoning.append(f"Heuristic suggests code review needed based on content analysis")
            elif code_review_rec is False:
                reasoning.append(f"Heuristic suggests no code review needed based on content analysis")

            if doc_impact is True:
                reasoning.append(f"Heuristic suggests documentation updates needed")
            elif doc_impact is False:
                reasoning.append(f"Heuristic suggests no documentation updates needed")

            if risk_level:
                reasoning.append(f"Heuristic assessed risk level as {risk_level}")

            context['reasoning'] = reasoning

        except Exception as e:
            self.logger.warning(f"Error gathering heuristic context: {e}")

        return context

    def _extract_metadata_with_llm_enhanced(self, documentation: str, document_record: Optional['DocumentRecord'] = None, heuristic_context: Optional[Dict[str, Any]] = None) -> tuple[Dict[str, Any], Optional[str]]:
        """Extract metadata using LLM enhanced with heuristic context for better decisions"""
        if not self.ollama_client:
            self.logger.warning("No Ollama client available for enhanced LLM metadata extraction")
            return {}, None

        try:
            # Build enhanced prompt with heuristic context
            context_prompt = self._build_context_prompt(heuristic_context) if heuristic_context else ""

            # Try to use enhanced prompts if available
            config = self.config_manager.load_config() if self.config_manager else None
            use_enhanced = config and getattr(config, 'use_enhanced_prompts', True)

            if document_record and use_enhanced:
                try:
                    from prompt_templates import PromptTemplateManager, ContextAnalyzer

                    if not config:
                        raise Exception("No config available")

                    # Initialize enhanced prompt system
                    prompt_manager = PromptTemplateManager(config)
                    context_analyzer = ContextAnalyzer(config)

                    # Analyze the change context
                    context = context_analyzer.analyze_change_context(
                        document=document_record,
                        commit_message=getattr(document_record, 'commit_message', None)
                    )

                    # Get enhanced prompts (note: enhanced prompts don't support heuristic context yet)
                    system_prompt, user_prompt = prompt_manager.get_metadata_extraction_prompt(
                        documentation, context
                    )

                    # Enhance the system prompt with heuristic context
                    if heuristic_context:
                        context_prompt = self._build_context_prompt(heuristic_context)
                        system_prompt = context_prompt + "\n\n" + system_prompt

                    self.logger.debug(f"Using enhanced prompts with heuristic context for document {document_record.id}")

                except Exception as e:
                    self.logger.warning(f"Failed to use enhanced prompts, falling back to basic with context: {e}")
                    # Fall back to basic prompts with context
                    system_prompt, user_prompt = self._get_basic_metadata_prompts_with_context(documentation, context_prompt)
            else:
                # Use basic prompts with context when no document context available or enhanced prompts disabled
                system_prompt, user_prompt = self._get_basic_metadata_prompts_with_context(documentation, context_prompt)

            # Determine which specialized model to use
            specialized_model = None
            actual_model_used = None

            if config and hasattr(self.ollama_client, 'config') and self.ollama_client.config:
                # Prefer risk assessment model, fall back to code review model
                risk_model = getattr(self.ollama_client.config, 'ollama_model_risk_assessment', None)
                code_review_model = getattr(self.ollama_client.config, 'ollama_model_code_review', None)
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(f"🎯 MetadataExtractor using specialized model with heuristic context: {specialized_model}")
                    actual_model_used = specialized_model
                else:
                    actual_model_used = self.ollama_client.config.ollama_model if self.ollama_client.config else None

            response = self.ollama_client.call_ollama(user_prompt, system_prompt, model=specialized_model)

            if response:
                try:
                    # Clean up response and parse JSON
                    cleaned_response = response.strip()
                    if cleaned_response.startswith('```json'):
                        cleaned_response = cleaned_response[7:]
                    if cleaned_response.endswith('```'):
                        cleaned_response = cleaned_response[:-3]
                    cleaned_response = cleaned_response.strip()

                    return json.loads(cleaned_response), actual_model_used
                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse enhanced LLM metadata response as JSON: {e}")
                    return {}, actual_model_used

            return {}, actual_model_used
        except Exception as e:
            self.logger.error(f"Error extracting metadata with enhanced LLM: {e}")
            return {}, None

    def _build_context_prompt(self, heuristic_context: Dict[str, Any]) -> str:
        """Build context prompt from heuristic analysis"""
        if not heuristic_context:
            return ""

        context_parts = ["HEURISTIC ANALYSIS CONTEXT:"]

        # Add indicators
        indicators = heuristic_context.get('indicators', {})
        if indicators:
            context_parts.append("\nKey Indicators:")
            for key, value in indicators.items():
                context_parts.append(f"- {key.replace('_', ' ').title()}: {value}")

        # Add reasoning
        reasoning = heuristic_context.get('reasoning', [])
        if reasoning:
            context_parts.append("\nHeuristic Assessment:")
            for reason in reasoning:
                context_parts.append(f"- {reason}")

        # Add preliminary decisions
        decisions = heuristic_context.get('decisions', {})
        if decisions:
            context_parts.append("\nPreliminary Heuristic Decisions:")
            for field, value in decisions.items():
                context_parts.append(f"- {field.replace('_', ' ').title()}: {value}")

        context_parts.append("\nPlease consider this heuristic analysis when making your final decisions, but feel free to override if the full context suggests different conclusions.\n")

        return "\n".join(context_parts)

    def _get_basic_metadata_prompts_with_context(self, documentation: str, context_prompt: str) -> Tuple[str, str]:
        """Get basic metadata extraction prompts enhanced with heuristic context"""

        system_prompt = f"""You are a metadata extraction assistant with access to heuristic analysis context.

{context_prompt}

Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- risk_level: "HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- documentation_impact: true/false (whether documentation needs updates)

Consider both the heuristic analysis context and the full documentation content. The heuristic analysis provides helpful indicators, but make your final decision based on the complete picture.

Be precise and consistent. If you cannot determine a value, use null."""

        user_prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

        return system_prompt, user_prompt

    def extract_ai_summary(self, content: str) -> Optional[str]:
        """Extract AI-generated summary from document content to use as commit message"""
        try:
            # Look for the Summary section in the documentation
            summary_content = self.extract_section(content, "Summary")
            
            if summary_content:
                # Clean up the summary - take first sentence or first line if it's concise
                lines = summary_content.split('\n')
                first_line = lines[0].strip()
                
                # If first line is a complete sentence and not too long, use it
                if first_line and len(first_line) <= 100 and (first_line.endswith('.') or len(lines) == 1):
                    return first_line.rstrip('.')
                
                # Otherwise, try to find a concise summary sentence
                for line in lines:
                    line = line.strip()
                    if line and len(line) <= 100 and not line.startswith('*') and not line.startswith('-'):
                        return line.rstrip('.')
                
                # Fallback: use first 80 characters of summary
                if len(first_line) > 0:
                    return (first_line[:80] + '...') if len(first_line) > 80 else first_line.rstrip('.')
            
            return None
        except Exception as e:
            self.logger.debug(f"Error extracting AI summary: {e}")
            return None
    
    def extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            pattern = rf'##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)'
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None
    
    def extract_field(self, content: str, field_name: str) -> Optional[str]:
        """Extract field value from document content (for markdown metadata)"""
        pattern = rf'\*\*{re.escape(field_name)}:\*\*\s*(.+?)(?:\n|$)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else None
    
    def parse_date_with_fallbacks(self, date_input: Any, filename_date: Optional[str] = None) -> datetime:
        """Parse date with multiple fallback strategies"""
        # Try repository date first
        if date_input:
            try:
                if isinstance(date_input, str):
                    return datetime.fromisoformat(date_input.replace('Z', '+00:00'))
                elif isinstance(date_input, datetime):
                    return date_input
            except (ValueError, TypeError):
                pass
        
        # Try filename date
        if filename_date:
            try:
                return datetime.strptime(filename_date, '%Y-%m-%d')
            except ValueError:
                pass
        
        # Final fallback
        return datetime.now()
    
    def generate_document_id(self, repository_id: str, revision: int) -> str:
        """Generate consistent document ID"""
        return f"{repository_id}_{revision}"
    
    def extract_code_review_recommendation(self, content: str, document_record: Optional['DocumentRecord'] = None) -> Optional[bool]:
        """Extract code review recommendation from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            review_section = self.extract_section(content, "Code Review Recommendation")
            if review_section:
                review_lower = review_section.lower()

                # Look for clear decisions at the start of the section
                first_sentence = review_lower.split('.')[0] if '.' in review_lower else review_lower[:100]

                # Check for explicit "No" decisions first (more specific patterns)
                if ("no, this commit does not require" in first_sentence or
                    "no, this commit should not" in first_sentence or
                    "not required" in first_sentence or "no review" in first_sentence or
                    "skip review" in first_sentence or "does not require" in first_sentence):
                    return False

                # Check for explicit "Yes" decisions (more specific patterns)
                elif ("yes, this commit should" in first_sentence or
                      "yes, this commit requires" in first_sentence or
                      "recommended" in first_sentence or "should be reviewed" in first_sentence or
                      "requires review" in first_sentence):
                    return True

                # Fallback to broader patterns in the full section
                elif ("not required" in review_lower or "no review" in review_lower or "skip review" in review_lower or
                      "does not require" in review_lower or "should not require" in review_lower or
                      "not be subject to" in review_lower or "not necessary" in review_lower):
                    return False
                elif ("recommended" in review_lower or "should be reviewed" in review_lower or
                      "should be code reviewed" in review_lower or "requires review" in review_lower or
                      "should be considered" in review_lower or "consider" in review_lower or
                      "priority" in review_lower):
                    return True

            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic code review extraction failed, trying LLM fallback")
                llm_result, _ = self._extract_metadata_with_llm(content, document_record)
                return llm_result.get('code_review_recommended')

            return None
        except Exception:
            return None
    
    def extract_code_review_priority(self, content: str, document_record: Optional['DocumentRecord'] = None) -> Optional[str]:
        """Extract code review priority from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            review_section = self.extract_section(content, "Code Review Recommendation")
            if review_section:
                review_lower = review_section.lower()
                if "high priority" in review_lower or "critical" in review_lower or "urgent" in review_lower:
                    return "HIGH"
                elif "medium priority" in review_lower or "moderate" in review_lower:
                    return "MEDIUM"
                elif "low priority" in review_lower or "minor" in review_lower:
                    return "LOW"

            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic priority extraction failed, trying LLM fallback")
                llm_result, _ = self._extract_metadata_with_llm(content, document_record)
                return llm_result.get('code_review_priority')

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting code review priority: {e}")
            return None
    
    def extract_documentation_impact(self, content: str, document_record: Optional['DocumentRecord'] = None) -> Optional[bool]:
        """Extract documentation impact from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            doc_section = self.extract_section(content, "Documentation Impact")
            if doc_section:
                doc_lower = doc_section.lower()

                # Look for clear decisions at the start of the section
                first_sentence = doc_lower.split('.')[0] if '.' in doc_lower else doc_lower[:100]

                # Check for explicit "No" decisions first (more specific patterns)
                if ("no, documentation updates are not required" in first_sentence or
                    "no, this commit does not affect" in first_sentence or
                    "not required" in first_sentence or "no updates" in first_sentence or
                    "no impact" in first_sentence or "no documentation updates" in first_sentence):
                    return False

                # Check for explicit "Yes" decisions (more specific patterns)
                elif ("yes, documentation updates are needed" in first_sentence or
                      "yes, this commit affects" in first_sentence or
                      "required" in first_sentence or "should be updated" in first_sentence or
                      "needs update" in first_sentence):
                    return True

                # Fallback to broader patterns in the full section
                elif ("not required" in doc_lower or "no updates" in doc_lower or "no impact" in doc_lower or
                      "no documentation updates" in doc_lower):
                    return False
                elif "required" in doc_lower or "should be updated" in doc_lower or "needs update" in doc_lower:
                    return True

            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic documentation impact extraction failed, trying LLM fallback")
                llm_result, _ = self._extract_metadata_with_llm(content, document_record)
                return llm_result.get('documentation_impact')

            return None
        except Exception:
            return None
    
    def extract_risk_level(self, content: str, document_record: Optional['DocumentRecord'] = None) -> Optional[str]:
        """Extract risk level from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            sections = ["Code Review Recommendation", "Impact Assessment", "Summary", "Recommendations"]

            for section_name in sections:
                section_content = self.extract_section(content, section_name)
                if section_content:
                    section_lower = section_content.lower()
                    if ("high risk" in section_lower or "critical" in section_lower or
                        "breaking change" in section_lower or "major impact" in section_lower):
                        return "HIGH"
                    elif ("medium risk" in section_lower or "moderate" in section_lower or
                          "some impact" in section_lower):
                        return "MEDIUM"
                    elif ("low risk" in section_lower or "minor" in section_lower or
                          "minimal impact" in section_lower or "safe" in section_lower):
                        return "LOW"

            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic risk level extraction failed, trying LLM fallback")
                llm_result, _ = self._extract_metadata_with_llm(content, document_record)
                return llm_result.get('risk_level')

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting risk level: {e}")
            return None
    
    def _extract_metadata_with_llm(self, documentation: str, document_record: Optional['DocumentRecord'] = None) -> tuple[Dict[str, Any], Optional[str]]:
        """Extract metadata using LLM with enhanced contextual prompts when available

        Returns:
            tuple: (metadata_dict, model_used)
        """
        if not self.ollama_client:
            self.logger.warning("No Ollama client available for LLM metadata extraction")
            return {}, None

        try:
            # Try to use enhanced prompts if available
            config = self.config_manager.load_config() if self.config_manager else None
            use_enhanced = config and getattr(config, 'use_enhanced_prompts', True)

            if document_record and use_enhanced:
                try:
                    from prompt_templates import PromptTemplateManager, ContextAnalyzer

                    if not config:
                        raise Exception("No config available")

                    # Initialize enhanced prompt system
                    prompt_manager = PromptTemplateManager(config)
                    context_analyzer = ContextAnalyzer(config)

                    # Analyze the change context
                    context = context_analyzer.analyze_change_context(
                        document=document_record,
                        commit_message=getattr(document_record, 'commit_message', None)
                    )

                    # Get enhanced prompts
                    system_prompt, user_prompt = prompt_manager.get_metadata_extraction_prompt(
                        documentation, context
                    )

                    self.logger.debug(f"Using enhanced prompts for document {document_record.id}")

                except Exception as e:
                    self.logger.warning(f"Failed to use enhanced prompts, falling back to basic: {e}")
                    # Fall back to basic prompts
                    system_prompt, user_prompt = self._get_basic_metadata_prompts(documentation)
            else:
                # Use basic prompts when no document context available or enhanced prompts disabled
                system_prompt, user_prompt = self._get_basic_metadata_prompts(documentation)

            # Determine which specialized model to use
            specialized_model = None
            actual_model_used = None

            if config and hasattr(self.ollama_client, 'config') and self.ollama_client.config:
                # Prefer risk assessment model, fall back to code review model
                risk_model = getattr(self.ollama_client.config, 'ollama_model_risk_assessment', None)
                code_review_model = getattr(self.ollama_client.config, 'ollama_model_code_review', None)
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(f"🎯 MetadataExtractor using specialized model: {specialized_model}")
                    actual_model_used = specialized_model
                else:
                    actual_model_used = self.ollama_client.config.ollama_model if self.ollama_client.config else None

            response = self.ollama_client.call_ollama(user_prompt, system_prompt, model=specialized_model)
            
            if response:
                try:
                    # Clean up response and parse JSON
                    cleaned_response = response.strip()
                    if cleaned_response.startswith('```json'):
                        cleaned_response = cleaned_response[7:]
                    if cleaned_response.endswith('```'):
                        cleaned_response = cleaned_response[:-3]
                    cleaned_response = cleaned_response.strip()
                    
                    return json.loads(cleaned_response), actual_model_used
                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse LLM metadata response as JSON: {e}")
                    return {}, actual_model_used

            return {}, actual_model_used
        except Exception as e:
            self.logger.error(f"Error extracting metadata with LLM: {e}")
            return {}, None

    def _get_basic_metadata_prompts(self, documentation: str) -> Tuple[str, str]:
        """Get basic metadata extraction prompts (fallback when enhanced prompts fail)"""

        system_prompt = """You are a metadata extraction assistant. Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- risk_level: "HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- documentation_impact: true/false (whether documentation needs updates)

Be precise and consistent. If you cannot determine a value, use null."""

        user_prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

        return system_prompt, user_prompt
