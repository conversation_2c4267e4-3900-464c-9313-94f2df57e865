#!/usr/bin/env python3
"""
Check why the Start Monitoring button is disabled
"""

import requests
import json

def check_button_status():
    """Check the monitoring status that controls button state"""
    print("🔍 Checking why Start Monitoring button is disabled...")
    
    try:
        # Check the main dashboard status
        response = requests.get('http://localhost:5000/api/status', timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"\n📊 Current System Status:")
            print(f"  Monitoring running: {status.get('monitoring_running', 'Unknown')}")
            print(f"  Last check time: {status.get('last_check_time', 'Never')}")
            print(f"  Enabled repositories: {status.get('enabled_repositories', 0)}")
            print(f"  Ollama connected: {status.get('ollama_connected', 'Unknown')}")
            
            # The button is disabled when status.running is True
            running = status.get('monitoring_running', False)
            if running:
                print(f"\n✅ System reports monitoring IS running")
                print(f"   This is why the 'Start Monitoring' button is disabled")
                print(f"   The 'Stop Monitoring' button should be enabled instead")
            else:
                print(f"\n❌ System reports monitoring is NOT running")
                print(f"   The 'Start Monitoring' button should be enabled")
                print(f"   There might be a UI state issue")
                
        else:
            print(f"❌ Could not get status (HTTP {response.status_code})")
            
    except Exception as e:
        print(f"❌ Error checking status: {e}")

def check_actual_monitoring_activity():
    """Check if monitoring is actually active by looking at logs"""
    print(f"\n🔍 Checking if monitoring is actually active...")
    
    try:
        # Check recent logs for monitoring activity
        log_path = "reposense_ai/data/reposense_ai.log"
        
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Look for recent monitoring activity
        recent_lines = lines[-50:]
        
        monitoring_activity = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in [
                'checking for new commits',
                'daemon',
                'monitoring started',
                'monitoring stopped'
            ]):
                monitoring_activity.append(line.strip())
        
        if monitoring_activity:
            print(f"🔍 Recent monitoring activity:")
            for line in monitoring_activity[-5:]:
                print(f"  {line}")
            print(f"\n✅ Monitoring appears to be active")
        else:
            print(f"❌ No recent monitoring activity found")
            print(f"   Monitoring may be reported as 'running' but not actually active")
            
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

def test_stop_and_start():
    """Test stopping and starting monitoring"""
    print(f"\n🔧 Testing Stop/Start Monitoring...")
    
    try:
        # Try to stop monitoring first
        print("🔴 Attempting to stop monitoring...")
        stop_response = requests.post('http://localhost:5000/api/stop', timeout=30)
        print(f"Stop status: {stop_response.status_code}")
        
        if stop_response.status_code == 200:
            stop_data = stop_response.json()
            print(f"Stop response: {json.dumps(stop_data, indent=2)}")
            
            # Wait a moment
            import time
            time.sleep(2)
            
            # Now try to start monitoring
            print(f"\n🟢 Attempting to start monitoring...")
            start_response = requests.post('http://localhost:5000/api/start', timeout=30)
            print(f"Start status: {start_response.status_code}")
            
            if start_response.status_code == 200:
                start_data = start_response.json()
                print(f"Start response: {json.dumps(start_data, indent=2)}")
                
                if start_data.get('repositories', 0) > 0:
                    print(f"✅ Successfully started monitoring for {start_data.get('repositories')} repositories")
                    print(f"🔄 Periodic scanning should now be active")
                else:
                    print(f"❌ Start succeeded but no repositories being monitored")
            else:
                print(f"❌ Start failed: {start_response.text}")
        else:
            print(f"❌ Stop failed: {stop_response.text}")
            
    except Exception as e:
        print(f"❌ Error testing stop/start: {e}")

def check_repositories_status():
    """Check repository configuration"""
    print(f"\n📋 Checking repository status...")
    
    try:
        # Manual check to see what repositories are configured
        check_response = requests.post('http://localhost:5000/api/check', timeout=30)
        print(f"Manual check status: {check_response.status_code}")
        
        if check_response.status_code == 200:
            check_data = check_response.json()
            repos_checked = check_data.get('repositories_checked', 0)
            print(f"✅ Manual check found {repos_checked} repositories")
            
            if repos_checked > 0:
                print(f"✅ Repositories are configured and accessible")
                print(f"💡 The issue is likely that monitoring is already running")
            else:
                print(f"❌ No repositories found - this could be the issue")
        else:
            print(f"❌ Manual check failed: {check_response.text}")
            
    except Exception as e:
        print(f"❌ Error checking repositories: {e}")

if __name__ == "__main__":
    check_button_status()
    check_actual_monitoring_activity()
    check_repositories_status()
    test_stop_and_start()
    
    print(f"\n💡 Solutions:")
    print(f"  1. If monitoring is running but not active: Use Stop → Start buttons")
    print(f"  2. If no repositories configured: Add repositories first")
    print(f"  3. If UI state is wrong: Refresh the page")
    print(f"  4. If all else fails: Restart the container")
    print(f"\n🎯 Goal: Get periodic scanning active to detect revision 9")
