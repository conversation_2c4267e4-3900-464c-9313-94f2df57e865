@echo off
REM RepoSense AI Docker Image Build Script for Windows
REM Simple batch file version for basic Docker image building

setlocal enabledelayedexpansion

REM Configuration
set IMAGE_NAME=reposense-ai
set IMAGE_TAG=latest
set BUILD_CONTEXT=.\reposense_ai

echo.
echo ================================
echo   RepoSense AI Docker Builder
echo ================================
echo.

REM Parse command line arguments
set SAVE_IMAGE=false
set OUTPUT_FILE=
set SHOW_HELP=false

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="--save" (
    set SAVE_IMAGE=true
    set OUTPUT_FILE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--tag" (
    set IMAGE_TAG=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--help" (
    set SHOW_HELP=true
    shift
    goto :parse_args
)
if /i "%~1"=="-h" (
    set SHOW_HELP=true
    shift
    goto :parse_args
)
echo Unknown argument: %~1
goto :show_help

:args_done

if "%SHOW_HELP%"=="true" goto :show_help

REM Check if Docker is available
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not in PATH
    echo Please install Docker Desktop for Windows
    exit /b 1
)

REM Check if we're in the right directory
if not exist "%BUILD_CONTEXT%" (
    echo [ERROR] reposense_ai directory not found
    echo Please run this script from the parent directory containing reposense_ai folder
    exit /b 1
)

if not exist "%BUILD_CONTEXT%\Dockerfile" (
    echo [ERROR] Dockerfile not found in %BUILD_CONTEXT%
    echo Please check the directory structure
    exit /b 1
)

echo [STEP 1] Validating build context...

REM Check required files
set REQUIRED_FILES=requirements.txt entrypoint.sh reposense_ai_app.py
for %%f in (%REQUIRED_FILES%) do (
    if not exist "%BUILD_CONTEXT%\%%f" (
        echo [ERROR] Required file not found: %%f
        exit /b 1
    )
)

echo [OK] Build context validated

echo.
echo [STEP 2] Building Docker image...
echo Image name: %IMAGE_NAME%:%IMAGE_TAG%
echo Build context: %BUILD_CONTEXT%
echo.

REM Build the Docker image
docker build -t %IMAGE_NAME%:%IMAGE_TAG% %BUILD_CONTEXT%
if errorlevel 1 (
    echo.
    echo [ERROR] Docker build failed!
    exit /b 1
)

echo.
echo [OK] Docker image built successfully!

REM Show image information
echo.
echo [STEP 3] Image information...
docker images %IMAGE_NAME%:%IMAGE_TAG%

REM Test the image
echo.
echo [STEP 4] Testing image...
echo Starting test container on port 5001...

docker run --rm -d --name reposense-ai-test -p 5001:5000 %IMAGE_NAME%:%IMAGE_TAG% >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Could not start test container (port 5001 may be in use)
    goto :skip_test
)

REM Wait a moment for container to start
timeout /t 5 /nobreak >nul

REM Test health endpoint using curl or PowerShell
curl -f http://localhost:5001/health >nul 2>&1
if errorlevel 1 (
    REM Try with PowerShell if curl is not available
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5001/health' -TimeoutSec 5 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Health check failed (may be normal if Ollama not available)
    ) else (
        echo [OK] Health check passed
    )
) else (
    echo [OK] Health check passed
)

REM Stop test container
docker stop reposense-ai-test >nul 2>&1

:skip_test

REM Save image if requested
if "%SAVE_IMAGE%"=="true" (
    echo.
    echo [STEP 5] Saving image to file...
    
    if "%OUTPUT_FILE%"=="" (
        set OUTPUT_FILE=reposense-ai-%IMAGE_TAG%.tar
    )
    
    echo Saving to: !OUTPUT_FILE!
    docker save %IMAGE_NAME%:%IMAGE_TAG% -o "!OUTPUT_FILE!"
    if errorlevel 1 (
        echo [ERROR] Failed to save image
        exit /b 1
    )
    
    echo [OK] Image saved successfully!
    echo.
    echo To deploy to remote server:
    echo 1. Transfer file: scp "!OUTPUT_FILE!" user@server:/path/
    echo 2. Load on server: docker load -i "!OUTPUT_FILE!"
    echo 3. Run container: docker run -p 5000:5000 %IMAGE_NAME%:%IMAGE_TAG%
)

echo.
echo ================================
echo   Build completed successfully!
echo ================================
echo.
echo Image: %IMAGE_NAME%:%IMAGE_TAG%
echo.
echo Next steps:
echo   - Save to file: %~nx0 --save reposense-ai.tar
echo   - Run locally:  docker run -p 5000:5000 %IMAGE_NAME%:%IMAGE_TAG%
echo   - View images:  docker images %IMAGE_NAME%
echo.

goto :end

:show_help
echo.
echo Usage: %~nx0 [OPTIONS]
echo.
echo Options:
echo   --save FILENAME    Save Docker image to tar file
echo   --tag TAG          Set image tag (default: latest)
echo   --help, -h         Show this help
echo.
echo Examples:
echo   %~nx0                              Build image locally
echo   %~nx0 --save reposense-ai.tar      Build and save to file
echo   %~nx0 --tag v1.0.0                 Build with custom tag
echo.

:end
endlocal
