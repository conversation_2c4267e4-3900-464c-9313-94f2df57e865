#!/usr/bin/env python3
"""
Test script to verify repository discovery page access and functionality
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
sys.path.append('/app')

def test_discovery_page_access():
    """Test that the repository discovery page is accessible and contains branch monitoring controls"""
    print("🔍 Testing Repository Discovery Page Access")
    print("=" * 50)
    
    try:
        # Test page access
        response = requests.get('http://localhost:5000/repositories/discover', timeout=10)
        
        if response.status_code == 200:
            print("✅ Repository discovery page accessible")
            
            # Parse HTML to check for branch monitoring controls
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for branch monitoring elements
            monitor_all_checkbox = soup.find('input', {'id': 'import_monitor_all_branches'})
            branch_path_input = soup.find('input', {'id': 'import_branch_path'})
            branch_monitoring_card = soup.find('div', class_='card-header', string=lambda text: text and 'Branch Monitoring' in text)
            
            print("\n🔍 Checking for branch monitoring UI elements:")
            
            if monitor_all_checkbox:
                print("✅ 'Monitor all branches' checkbox found")
                print(f"   Type: {monitor_all_checkbox.get('type')}")
                print(f"   Name: {monitor_all_checkbox.get('name')}")
            else:
                print("❌ 'Monitor all branches' checkbox NOT found")
            
            if branch_path_input:
                print("✅ Branch path input field found")
                print(f"   Type: {branch_path_input.get('type')}")
                print(f"   Name: {branch_path_input.get('name')}")
                print(f"   Placeholder: {branch_path_input.get('placeholder')}")
            else:
                print("❌ Branch path input field NOT found")
            
            if branch_monitoring_card:
                print("✅ Branch monitoring card section found")
            else:
                print("❌ Branch monitoring card section NOT found")
            
            # Check for JavaScript functions
            if 'toggleImportBranchPath' in response.text:
                print("✅ toggleImportBranchPath JavaScript function found")
            else:
                print("❌ toggleImportBranchPath JavaScript function NOT found")
            
            # Check for form handling
            if 'import_monitor_all_branches' in response.text:
                print("✅ Branch monitoring form handling found")
            else:
                print("❌ Branch monitoring form handling NOT found")
            
            print(f"\n📊 Page Analysis:")
            print(f"   Page size: {len(response.text)} characters")
            print(f"   Contains 'Branch Monitoring': {'Branch Monitoring' in response.text}")
            print(f"   Contains 'monitor_all_branches': {'monitor_all_branches' in response.text}")
            print(f"   Contains 'branch_path': {'branch_path' in response.text}")
            
        else:
            print(f"❌ Repository discovery page not accessible: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error accessing repository discovery page: {e}")

def test_discovery_endpoints():
    """Test repository discovery related endpoints"""
    print(f"\n🌐 Testing Repository Discovery Endpoints")
    print("=" * 50)
    
    endpoints = [
        '/repositories/discover',
        '/repositories/discover/scan',
        '/repositories/import'
    ]
    
    for endpoint in endpoints:
        try:
            if endpoint.endswith('/scan') or endpoint.endswith('/import'):
                # These are POST endpoints, just check if they exist (will return 405 Method Not Allowed)
                response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
                if response.status_code == 405:
                    print(f"✅ {endpoint} - POST endpoint exists (405 Method Not Allowed expected)")
                elif response.status_code == 200:
                    print(f"✅ {endpoint} - Accessible")
                else:
                    print(f"⚠️  {endpoint} - Status: {response.status_code}")
            else:
                # GET endpoint
                response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint} - Accessible")
                else:
                    print(f"❌ {endpoint} - Status: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")

def test_navigation_links():
    """Test that navigation links to repository discovery work"""
    print(f"\n🔗 Testing Navigation Links")
    print("=" * 30)
    
    try:
        # Check main repositories page for discovery link
        response = requests.get('http://localhost:5000/repositories', timeout=10)
        
        if response.status_code == 200:
            if '/repositories/discover' in response.text:
                print("✅ Repository discovery link found in repositories page")
            else:
                print("⚠️  Repository discovery link not found in repositories page")
                
            # Check for any mention of discovery functionality
            if 'discover' in response.text.lower():
                print("✅ Discovery functionality mentioned in repositories page")
            else:
                print("⚠️  No discovery functionality mentioned")
        else:
            print(f"❌ Could not access repositories page: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking navigation links: {e}")

if __name__ == "__main__":
    test_discovery_page_access()
    test_discovery_endpoints()
    test_navigation_links()
    print(f"\n🎉 Repository discovery page access tests completed!")
    print(f"The discovery page should now include branch monitoring controls! 🚀")
