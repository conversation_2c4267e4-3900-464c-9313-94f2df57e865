#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced priority voting mechanism
"""

def test_priority_voting():
    """Test the voting mechanism with different scenarios"""
    
    def vote_on_priority_simple(votes: dict, confidence_scores: dict) -> str:
        """Simple voting mechanism for testing"""
        if not votes:
            return "MEDIUM"
            
        if len(votes) == 1:
            method, priority = next(iter(votes.items()))
            confidence = confidence_scores.get(method, 0.5)
            return priority if confidence >= 0.3 else "MEDIUM"
        
        # Multiple votes - weighted by confidence
        priority_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
        total_weight = 0.0
        
        for method, priority in votes.items():
            confidence = confidence_scores.get(method, 0.5)
            priority_scores[priority] += confidence
            total_weight += confidence
            
        if total_weight > 0:
            for priority in priority_scores:
                priority_scores[priority] /= total_weight
                
        # Find winning priority
        winning_priority = max(priority_scores.keys(), key=lambda k: priority_scores[k])
        winning_score = priority_scores[winning_priority]
        
        # Require minimum confidence
        if winning_score < 0.5:  # Adjusted threshold
            return "MEDIUM"
            
        return winning_priority

    # Test scenarios
    test_cases = [
        {
            "name": "Agreement - High Priority",
            "votes": {"heuristic": "HIGH", "llm": "HIGH", "file_analysis": "HIGH"},
            "confidence": {"heuristic": 0.8, "llm": 0.7, "file_analysis": 0.6},
            "expected": "HIGH"
        },
        {
            "name": "Disagreement - Heuristic wins with higher confidence",
            "votes": {"heuristic": "HIGH", "llm": "MEDIUM"},
            "confidence": {"heuristic": 0.9, "llm": 0.5},
            "expected": "HIGH"
        },
        {
            "name": "Disagreement - LLM wins with higher confidence",
            "votes": {"heuristic": "LOW", "llm": "HIGH"},
            "confidence": {"heuristic": 0.4, "llm": 0.8},
            "expected": "HIGH"
        },
        {
            "name": "Three-way split - weighted average",
            "votes": {"heuristic": "HIGH", "llm": "MEDIUM", "file_analysis": "LOW"},
            "confidence": {"heuristic": 0.6, "llm": 0.8, "file_analysis": 0.4},
            "expected": "MEDIUM"  # LLM has highest confidence
        },
        {
            "name": "Low confidence - fallback to MEDIUM",
            "votes": {"heuristic": "HIGH", "llm": "LOW"},
            "confidence": {"heuristic": 0.2, "llm": 0.3},
            "expected": "MEDIUM"
        }
    ]
    
    print("🗳️  Testing Priority Voting Mechanism")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        result = vote_on_priority_simple(test_case["votes"], test_case["confidence"])
        status = "✅ PASS" if result == test_case["expected"] else "❌ FAIL"
        
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Votes: {test_case['votes']}")
        print(f"Confidence: {test_case['confidence']}")
        print(f"Expected: {test_case['expected']}, Got: {result} {status}")
        
        # Show detailed calculation
        if len(test_case["votes"]) > 1:
            priority_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
            total_weight = 0.0
            
            for method, priority in test_case["votes"].items():
                confidence = test_case["confidence"].get(method, 0.5)
                priority_scores[priority] += confidence
                total_weight += confidence
                
            if total_weight > 0:
                for priority in priority_scores:
                    priority_scores[priority] /= total_weight
                    
            print(f"Weighted scores: {priority_scores}")

if __name__ == "__main__":
    test_priority_voting()
