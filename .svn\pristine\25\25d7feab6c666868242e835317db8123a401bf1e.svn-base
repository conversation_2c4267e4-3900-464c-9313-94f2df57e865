#!/bin/bash
# Migration Script for RepoSense AI Directory Restructure
# This script helps migrate from the old flat structure to the new reposense_ai/ subdirectory structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔄 RepoSense AI Migration Script${NC}"
echo "This script will help migrate your RepoSense AI installation to the new directory structure."
echo

# Function to log messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in a RepoSense AI directory
if [ ! -f "start_reposense_ai.py" ] && [ ! -f "reposense_ai_app.py" ]; then
    log_error "This doesn't appear to be a RepoSense AI directory."
    log_error "Please run this script from your RepoSense AI installation directory."
    exit 1
fi

# Check if migration is needed
if [ -d "reposense_ai" ]; then
    log_info "Directory structure already appears to be migrated."
    log_info "If you need to re-run the migration, please remove the reposense_ai/ directory first."
    exit 0
fi

log_step "1. Creating backup..."

# Create backup
BACKUP_DIR="reposense_ai_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup important files
for file in data logs config.json docker-compose.yml .env; do
    if [ -e "$file" ]; then
        cp -r "$file" "$BACKUP_DIR/" 2>/dev/null || log_warn "Could not backup $file"
    fi
done

log_info "Created backup in $BACKUP_DIR"

log_step "2. Creating new directory structure..."

# Create the new reposense_ai directory
mkdir -p reposense_ai

# List of files/directories to move to reposense_ai/
FILES_TO_MOVE=(
    "*.py"
    "requirements*.txt"
    "entrypoint.sh"
    "static"
    "templates"
    "repository_backends"
    "scripts"
    "data"
    "logs"
    "node_modules"
    "__pycache__"
    "*.json"
    "*.html"
    "*.txt"
    "*.md"
    "*.yml"
    "*.yaml"
    "image"
    "docker_backup"
)

# Move files to reposense_ai directory
for pattern in "${FILES_TO_MOVE[@]}"; do
    for file in $pattern; do
        if [ -e "$file" ] && [ "$file" != "README.md" ] && [ "$file" != "docker-compose.yml" ] && [ "$file" != "Dockerfile" ]; then
            mv "$file" reposense_ai/ 2>/dev/null || true
        fi
    done
done

# Keep certain files in root (except Dockerfile which stays in reposense_ai/)
ROOT_FILES=("README.md" "docker-compose.yml" ".env" ".gitignore" "LICENSE")
for file in "${ROOT_FILES[@]}"; do
    if [ -e "reposense_ai/$file" ]; then
        mv "reposense_ai/$file" . 2>/dev/null || true
    fi
done

# Dockerfile should stay in reposense_ai/ directory
if [ -f "Dockerfile" ] && [ ! -f "reposense_ai/Dockerfile" ]; then
    mv "Dockerfile" "reposense_ai/" 2>/dev/null || true
    log_info "Moved Dockerfile to reposense_ai/ directory"
fi

log_info "Moved application files to reposense_ai/ directory"

log_step "3. Updating configuration files..."

# Update docker-compose.yml if it exists
if [ -f "docker-compose.yml" ]; then
    # Backup original
    cp docker-compose.yml docker-compose.yml.backup
    
    # Update volume paths
    sed -i.tmp 's|./data:/app/data|./reposense_ai/data:/app/data|g' docker-compose.yml
    sed -i.tmp 's|./logs:/app/logs|./reposense_ai/logs:/app/logs|g' docker-compose.yml
    sed -i.tmp 's|\.:/app|./reposense_ai:/app|g' docker-compose.yml

    # Update build context for Dockerfile in subdirectory
    sed -i.tmp 's|context: \.|context: ./reposense_ai|g' docker-compose.yml
    
    # Clean up temp file
    rm -f docker-compose.yml.tmp
    
    log_info "Updated docker-compose.yml volume paths"
fi

# Update Dockerfile if it exists
if [ -f "Dockerfile" ]; then
    # Backup original
    cp Dockerfile Dockerfile.backup
    
    # Update COPY commands
    sed -i.tmp 's|COPY requirements.txt|COPY reposense_ai/requirements.txt|g' Dockerfile
    sed -i.tmp 's|COPY \. \.|COPY reposense_ai/ .|g' Dockerfile
    sed -i.tmp 's|COPY entrypoint.sh|COPY reposense_ai/entrypoint.sh|g' Dockerfile
    sed -i.tmp 's|COPY scripts/|COPY reposense_ai/scripts/|g' Dockerfile
    
    # Clean up temp file
    rm -f Dockerfile.tmp
    
    log_info "Updated Dockerfile paths"
fi

log_step "4. Setting up permissions..."

# Ensure proper permissions for Docker container
log_info "Setting up Docker-compatible permissions..."

# Create missing directories
mkdir -p reposense_ai/data reposense_ai/logs
mkdir -p reposense_ai/data/output reposense_ai/data/cache

# Set ownership to uid 1000 (appuser in container) - critical for Docker
if command -v sudo >/dev/null 2>&1; then
    sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set ownership - you may need to run manually"
    sudo chmod -R 755 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set permissions"
    log_info "Set Docker-compatible ownership (uid 1000) and permissions"
else
    # Fallback for systems without sudo
    chown -R 1000:1000 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set ownership - run: sudo chown -R 1000:1000 reposense_ai/"
    chmod -R 755 reposense_ai/data reposense_ai/logs 2>/dev/null || log_warn "Could not set permissions"
    log_info "Set directory permissions (ownership may need manual fix)"
fi

# Set executable permissions for scripts
if [ -f "reposense_ai/entrypoint.sh" ]; then
    chmod +x reposense_ai/entrypoint.sh 2>/dev/null || log_warn "Could not set entrypoint.sh permissions"
    log_info "Set entrypoint script permissions"
fi

log_step "5. Validating migration..."

# Check that key files exist in the right places
VALIDATION_PASSED=true

# Check root files
for file in "Dockerfile" "docker-compose.yml" "README.md"; do
    if [ ! -f "$file" ]; then
        log_error "Missing root file: $file"
        VALIDATION_PASSED=false
    fi
done

# Check reposense_ai files
for file in "reposense_ai/start_reposense_ai.py" "reposense_ai/requirements.txt" "reposense_ai/data"; do
    if [ ! -e "$file" ]; then
        log_error "Missing application file: $file"
        VALIDATION_PASSED=false
    fi
done

if [ "$VALIDATION_PASSED" = true ]; then
    log_info "Migration validation passed"
else
    log_error "Migration validation failed - please check the structure manually"
    exit 1
fi

echo
echo -e "${GREEN}✅ Migration completed successfully!${NC}"
echo
echo -e "${BLUE}New directory structure:${NC}"
echo "├── Dockerfile                    # RepoSense AI Dockerfile"
echo "├── docker-compose.yml           # Docker Compose configuration"
echo "├── README.md                    # Main documentation"
echo "└── reposense_ai/               # Application directory"
echo "    ├── data/                   # Persistent data"
echo "    ├── logs/                   # Application logs"
echo "    ├── static/                 # Web assets"
echo "    ├── templates/              # Web templates"
echo "    └── *.py                    # Application code"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Test the migration: docker-compose up -d"
echo "2. Verify the web interface: http://localhost:5000"
echo "3. If everything works, you can remove the backup: rm -rf $BACKUP_DIR"
echo
echo -e "${YELLOW}📖 For integration with larger Docker Compose setups, see INTEGRATION_README.md${NC}"
