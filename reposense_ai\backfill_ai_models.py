#!/usr/bin/env python3
"""
Backfill AI model information for existing documents
"""

import logging
from document_database import DocumentDatabase
from config_manager import ConfigManager

def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def backfill_ai_models():
    """Backfill ai_model_used field for existing documents"""
    print("🔄 Backfilling AI Model Information for Existing Documents")
    print("=" * 60)
    
    setup_logging()
    
    # Load configuration to get specialized models
    config_manager = ConfigManager("data/config.json")
    config = config_manager.load_config()
    print(f"📋 Current specialized models:")
    print(f"  Documentation model: {config.ollama_model_documentation}")
    print(f"  Code review model: {config.ollama_model_code_review}")
    print(f"  Risk assessment model: {config.ollama_model_risk_assessment}")
    print(f"  Default model: {config.ollama_model}")
    
    # Determine which model to use for existing documents
    # Since existing documents were analyzed for risk/code review, use risk assessment model
    backfill_model = config.ollama_model_risk_assessment or config.ollama_model_code_review or config.ollama_model
    
    print(f"\n🎯 Will backfill existing documents with: {backfill_model}")
    
    # Get database
    db = DocumentDatabase('data/documents.db')
    
    try:
        # Get all documents that don't have ai_model_used set
        docs = db.get_documents(limit=1000)  # Get a large number
        
        documents_to_update = [doc for doc in docs if not doc.ai_model_used]
        
        print(f"\n📊 Found {len(documents_to_update)} documents to update")
        
        if not documents_to_update:
            print("✅ All documents already have AI model information!")
            return True
        
        # Ask for confirmation
        response = input(f"\nUpdate {len(documents_to_update)} documents with AI model '{backfill_model}'? (y/N): ")
        if response.lower() != 'y':
            print("❌ Cancelled by user")
            return False
        
        # Update documents
        updated_count = 0
        for doc in documents_to_update:
            doc.ai_model_used = backfill_model
            
            if db.upsert_document(doc):
                updated_count += 1
                print(f"✅ Updated {doc.filename}")
            else:
                print(f"❌ Failed to update {doc.filename}")
        
        print(f"\n🎉 Successfully updated {updated_count}/{len(documents_to_update)} documents")
        
        # Verify updates
        print(f"\n🔍 Verification - checking updated documents:")
        updated_docs = db.get_documents(limit=5)
        for i, doc in enumerate(updated_docs[:3]):
            ai_model = doc.ai_model_used or 'Not set'
            print(f"  {i+1}. {doc.filename}: {ai_model}")
        
        return updated_count > 0
        
    except Exception as e:
        print(f"❌ Error during backfill: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = backfill_ai_models()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Backfill completed successfully!")
        print("🌐 Refresh your web interface to see the updated AI model information.")
    else:
        print("❌ Backfill failed or was cancelled.")
