#!/usr/bin/env python3
"""
Database Initialization Script for RepoSense AI
Ensures proper database setup with correct permissions
"""

import os
import sys
import stat
import logging
from pathlib import Path

# Add the app directory to the path so we can import our modules
sys.path.insert(0, '/app')

from document_database import DocumentDatabase

def setup_logging():
    """Set up logging for the initialization script"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def ensure_directory_permissions(directory_path: Path, logger):
    """Ensure directory has proper permissions"""
    try:
        if not directory_path.exists():
            directory_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory_path}")
        
        # Set directory permissions (755 - rwxr-xr-x)
        directory_path.chmod(0o755)
        logger.info(f"Set directory permissions for: {directory_path}")
        
    except Exception as e:
        logger.error(f"Error setting directory permissions for {directory_path}: {e}")
        return False
    return True

def ensure_database_permissions(db_path: Path, logger):
    """Ensure database file has proper permissions"""
    try:
        if db_path.exists():
            # Set file permissions (644 - rw-r--r--)
            db_path.chmod(0o644)
            logger.info(f"Set database file permissions: {db_path}")
            
            # Verify the file is readable and writable
            if not os.access(db_path, os.R_OK | os.W_OK):
                logger.error(f"Database file is not readable/writable: {db_path}")
                return False
            else:
                logger.info("Database file is readable and writable")
        else:
            logger.info("Database file does not exist yet - will be created with proper permissions")
            
    except Exception as e:
        logger.error(f"Error setting database file permissions: {e}")
        return False
    return True

def test_database_operations(db_path: str, logger):
    """Test basic database operations to ensure everything works"""
    try:
        logger.info("Testing database operations...")
        
        # Initialize database
        db = DocumentDatabase(db_path)
        logger.info("Database initialization successful")
        
        # Test connection
        with db._get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM documents")
            count = cursor.fetchone()[0]
            logger.info(f"Database connection test successful - found {count} documents")
        
        return True
        
    except Exception as e:
        logger.error(f"Database operation test failed: {e}")
        return False

def main():
    """Main initialization function"""
    logger = setup_logging()
    logger.info("Starting RepoSense AI database initialization...")
    
    # Define paths
    data_dir = Path("/app/data")
    db_path = data_dir / "documents.db"
    
    success = True
    
    # Ensure data directory exists and has proper permissions
    if not ensure_directory_permissions(data_dir, logger):
        success = False
    
    # Ensure other important directories exist
    for subdir in ["output", "cache"]:
        subdir_path = data_dir / subdir
        if not ensure_directory_permissions(subdir_path, logger):
            success = False
    
    # Initialize database (this will create the file if it doesn't exist)
    logger.info("Initializing database...")
    try:
        db = DocumentDatabase(str(db_path))
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        success = False
    
    # Ensure database file has proper permissions
    if not ensure_database_permissions(db_path, logger):
        success = False
    
    # Test database operations
    if not test_database_operations(str(db_path), logger):
        success = False
    
    # Final status
    if success:
        logger.info("✅ Database initialization completed successfully!")
        logger.info(f"Database location: {db_path}")
        logger.info(f"Database size: {db_path.stat().st_size if db_path.exists() else 0} bytes")
        return 0
    else:
        logger.error("❌ Database initialization failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
