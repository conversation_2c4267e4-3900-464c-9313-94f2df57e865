#!/bin/bash
# RepoSense AI Container Entrypoint Script
# Ensures proper file permissions and directory setup before starting the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}[ENTRYPOINT]${NC} Starting RepoSense AI container setup..."

# Function to log messages
log_info() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[ENTRYPOINT]${NC} $1"
}

log_error() {
    echo -e "${RED}[ENTRYPOINT]${NC} $1"
}

# Show container environment for debugging
log_info "🔍 Container Environment:"
echo "   User: $(whoami) ($(id))"
echo "   Working directory: $(pwd)"
echo "   Python version: $(python3 --version 2>/dev/null || echo 'Not available')"
echo "   Container OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo 'Unknown')"

# Ensure data directories exist with proper permissions
log_info "Setting up data directories..."

# Create directories if they don't exist
mkdir -p /app/data
mkdir -p /app/logs
mkdir -p /app/data/output
mkdir -p /app/data/cache

# Set ownership to appuser (current user should already be appuser)
CURRENT_USER=$(whoami)
if [ "$CURRENT_USER" = "appuser" ]; then
    log_info "Running as appuser - setting up permissions..."
    
    # Ensure all data directories are owned by appuser
    if [ -w /app/data ]; then
        # Only try to change ownership if we have write access to the parent directory
        find /app/data -type d -exec chmod 755 {} \; 2>/dev/null || log_warn "Could not set directory permissions"
        find /app/data -type f -exec chmod 644 {} \; 2>/dev/null || log_warn "Could not set file permissions"
    else
        log_warn "No write access to /app/data - permissions may need to be set externally"
    fi
    
    # Special handling for database file if it exists
    if [ -f "/app/data/documents.db" ]; then
        if [ -w "/app/data/documents.db" ]; then
            chmod 644 /app/data/documents.db
            log_info "Set database file permissions to 644"
        else
            log_warn "Database file exists but is not writable - this may cause issues"
        fi
    fi
    
    # Ensure logs directory is writable
    if [ -d "/app/logs" ] && [ -w "/app/logs" ]; then
        chmod 755 /app/logs 2>/dev/null || log_warn "Could not set logs directory permissions"
        log_info "Set logs directory permissions"
    else
        log_warn "Logs directory is not writable - this is usually okay"
    fi
    
else
    log_warn "Not running as appuser (current: $CURRENT_USER) - skipping permission setup"
fi

# Validate critical directories and provide detailed diagnostics
log_info "Validating directory setup..."

# Check data directory
if [ ! -w "/app/data" ]; then
    log_error "❌ Data directory is not writable! This will cause database issues."

    # Get detailed ownership information
    DATA_OWNER=$(stat -c '%U:%G (%u:%g)' /app/data 2>/dev/null || echo "unknown")
    CURRENT_USER_ID=$(id -u)
    CURRENT_GROUP_ID=$(id -g)

    log_error "   Current user: $(whoami) (uid=$CURRENT_USER_ID, gid=$CURRENT_GROUP_ID)"
    log_error "   Data directory owner: $DATA_OWNER"
    log_error ""
    log_error "🔧 Fix options:"
    log_error "   Option 1 (from host): sudo chown -R 1000:1000 ./reposense_ai/data"
    log_error "   Option 2 (from container): docker-compose exec --user root reposense-ai chown -R appuser:appuser /app/data"
    log_error "   Option 3 (automated): ./fix-docker-permissions.sh"
else
    log_info "✅ Data directory is writable"
fi

# Check logs directory
if [ ! -w "/app/logs" ]; then
    log_warn "⚠️  Logs directory is not writable - logging may be limited"

    # Get detailed ownership information for logs
    LOGS_OWNER=$(stat -c '%U:%G (%u:%g)' /app/logs 2>/dev/null || echo "unknown")
    log_warn "   Logs directory owner: $LOGS_OWNER"
    log_warn "   Fix: sudo chown -R 1000:1000 ./reposense_ai/logs"
else
    log_info "✅ Logs directory is writable"
fi

# Check database file specifically if it exists
if [ -f "/app/data/documents.db" ]; then
    if [ ! -w "/app/data/documents.db" ]; then
        log_error "❌ Database file is not writable!"
        DB_OWNER=$(stat -c '%U:%G (%u:%g)' /app/data/documents.db 2>/dev/null || echo "unknown")
        log_error "   Database file owner: $DB_OWNER"
        log_error "   This will prevent database operations from working"
    else
        log_info "✅ Database file is writable"
    fi
fi

# Display detailed environment info for troubleshooting
log_info "📊 Detailed Environment Information:"
echo "   User: $(whoami) ($(id))"
echo "   Working directory: $(pwd)"
echo "   Data directory: $(ls -ld /app/data 2>/dev/null || echo 'Not accessible')"
echo "   Logs directory: $(ls -ld /app/logs 2>/dev/null || echo 'Not accessible')"
if [ -f "/app/data/documents.db" ]; then
    echo "   Database file: $(ls -l /app/data/documents.db)"
fi
if [ -f "/app/data/config.json" ]; then
    echo "   Config file: $(ls -l /app/data/config.json)"
fi
echo "   Available disk space: $(df -h /app/data | tail -1 | awk '{print $4}' 2>/dev/null || echo 'Unknown')"

# Initialize database with proper permissions
log_info "Initializing database..."
if command -v python3 >/dev/null 2>&1; then
    if [ -w "/app/data" ]; then
        if python3 /usr/local/bin/init_database.py; then
            log_info "✅ Database initialization successful"
        else
            log_error "❌ Database initialization script failed!"
            log_error "   This usually indicates permission issues or missing dependencies"
            log_error "   Check the error messages above for details"
            log_warn "   Continuing anyway - application may have limited functionality"
        fi
    else
        log_error "❌ Skipping database initialization - data directory not writable"
        log_error "   Database operations will fail until permissions are fixed"
    fi
else
    log_warn "Python3 not available - skipping database initialization script"
fi

# Final status summary
echo
log_info "📋 Container Setup Summary:"
if [ -w "/app/data" ] && [ -w "/app/logs" ]; then
    log_info "   ✅ All permissions are correct - ready to start"
elif [ -w "/app/data" ]; then
    log_warn "   ⚠️  Data OK, logs limited - application will work but logging may be reduced"
else
    log_error "   ❌ Critical permission issues detected - application may not function properly"
    log_error "   🔧 Run the fix-docker-permissions.sh script to resolve these issues"
fi

log_info "Container setup complete - starting application..."

# Execute the main command
exec "$@"
