#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced voting mechanisms for priority, risk, and documentation impact
"""

def test_all_voting_systems():
    """Test all three voting systems with different scenarios"""
    
    def vote_on_priority(votes: dict, confidence_scores: dict) -> str:
        """Priority voting mechanism"""
        if not votes:
            return "MEDIUM"
        
        priority_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
        total_weight = 0.0
        
        for method, priority in votes.items():
            confidence = confidence_scores.get(method, 0.5)
            priority_scores[priority] += confidence
            total_weight += confidence
            
        if total_weight > 0:
            for priority in priority_scores:
                priority_scores[priority] /= total_weight
                
        winning_priority = max(priority_scores.keys(), key=lambda k: priority_scores[k])
        winning_score = priority_scores[winning_priority]
        
        return winning_priority if winning_score >= 0.3 else "MEDIUM"

    def vote_on_risk(votes: dict, confidence_scores: dict) -> str:
        """Risk voting mechanism"""
        if not votes:
            return "MEDIUM"
        
        risk_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
        total_weight = 0.0
        
        for method, risk in votes.items():
            confidence = confidence_scores.get(method, 0.5)
            risk_scores[risk] += confidence
            total_weight += confidence
            
        if total_weight > 0:
            for risk in risk_scores:
                risk_scores[risk] /= total_weight
                
        winning_risk = max(risk_scores.keys(), key=lambda k: risk_scores[k])
        winning_score = risk_scores[winning_risk]
        
        return winning_risk if winning_score >= 0.4 else "MEDIUM"

    def vote_on_doc_impact(votes: dict, confidence_scores: dict) -> bool:
        """Documentation impact voting mechanism"""
        if not votes:
            return False
        
        impact_scores = {"True": 0.0, "False": 0.0}
        total_weight = 0.0
        
        for method, impact in votes.items():
            confidence = confidence_scores.get(method, 0.5)
            impact_key = "True" if impact else "False"
            impact_scores[impact_key] += confidence
            total_weight += confidence
            
        if total_weight > 0:
            for impact in impact_scores:
                impact_scores[impact] /= total_weight
                
        winning_impact = max(impact_scores.keys(), key=lambda k: impact_scores[k])
        winning_score = impact_scores[winning_impact]
        
        return (winning_impact == "True") if winning_score >= 0.4 else False

    # Test scenarios for all three systems
    test_scenarios = [
        {
            "name": "Security Vulnerability Fix",
            "priority_votes": {"heuristic": "HIGH", "llm": "HIGH", "file_analysis": "HIGH"},
            "priority_confidence": {"heuristic": 0.9, "llm": 0.8, "file_analysis": 0.7},
            "risk_votes": {"heuristic": "HIGH", "llm": "HIGH", "file_analysis": "HIGH"},
            "risk_confidence": {"heuristic": 0.9, "llm": 0.8, "file_analysis": 0.8},
            "doc_votes": {"heuristic": True, "llm": True, "file_analysis": True},
            "doc_confidence": {"heuristic": 0.8, "llm": 0.9, "file_analysis": 0.6},
            "expected": {"priority": "HIGH", "risk": "HIGH", "doc_impact": True}
        },
        {
            "name": "Documentation Update",
            "priority_votes": {"heuristic": "LOW", "llm": "LOW"},
            "priority_confidence": {"heuristic": 0.8, "llm": 0.7},
            "risk_votes": {"heuristic": "LOW", "llm": "LOW"},
            "risk_confidence": {"heuristic": 0.9, "llm": 0.8},
            "doc_votes": {"heuristic": False, "llm": False},
            "doc_confidence": {"heuristic": 0.9, "llm": 0.8},
            "expected": {"priority": "LOW", "risk": "LOW", "doc_impact": False}
        },
        {
            "name": "Mixed Signals - LLM Override",
            "priority_votes": {"heuristic": "LOW", "llm": "HIGH", "file_analysis": "MEDIUM"},
            "priority_confidence": {"heuristic": 0.4, "llm": 0.9, "file_analysis": 0.5},
            "risk_votes": {"heuristic": "LOW", "llm": "HIGH", "file_analysis": "MEDIUM"},
            "risk_confidence": {"heuristic": 0.3, "llm": 0.9, "file_analysis": 0.4},
            "doc_votes": {"heuristic": False, "llm": True, "file_analysis": True},
            "doc_confidence": {"heuristic": 0.3, "llm": 0.9, "file_analysis": 0.6},
            "expected": {"priority": "HIGH", "risk": "HIGH", "doc_impact": True}
        },
        {
            "name": "Low Confidence - Fallback to Defaults",
            "priority_votes": {"heuristic": "HIGH", "llm": "LOW"},
            "priority_confidence": {"heuristic": 0.2, "llm": 0.2},
            "risk_votes": {"heuristic": "HIGH", "llm": "LOW"},
            "risk_confidence": {"heuristic": 0.2, "llm": 0.2},
            "doc_votes": {"heuristic": True, "llm": False},
            "doc_confidence": {"heuristic": 0.2, "llm": 0.2},
            "expected": {"priority": "MEDIUM", "risk": "MEDIUM", "doc_impact": False}
        }
    ]
    
    print("🗳️  Testing Enhanced Voting Systems")
    print("=" * 60)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 Test {i}: {scenario['name']}")
        print("-" * 40)
        
        # Test Priority Voting
        priority_result = vote_on_priority(scenario["priority_votes"], scenario["priority_confidence"])
        priority_status = "✅" if priority_result == scenario["expected"]["priority"] else "❌"
        print(f"Priority: {scenario['priority_votes']} → {priority_result} {priority_status}")
        
        # Test Risk Voting
        risk_result = vote_on_risk(scenario["risk_votes"], scenario["risk_confidence"])
        risk_status = "✅" if risk_result == scenario["expected"]["risk"] else "❌"
        print(f"Risk:     {scenario['risk_votes']} → {risk_result} {risk_status}")
        
        # Test Documentation Impact Voting
        doc_result = vote_on_doc_impact(scenario["doc_votes"], scenario["doc_confidence"])
        doc_status = "✅" if doc_result == scenario["expected"]["doc_impact"] else "❌"
        print(f"Doc Impact: {scenario['doc_votes']} → {doc_result} {doc_status}")
        
        # Overall test result
        all_passed = (priority_result == scenario["expected"]["priority"] and
                     risk_result == scenario["expected"]["risk"] and
                     doc_result == scenario["expected"]["doc_impact"])
        overall_status = "✅ PASS" if all_passed else "❌ FAIL"
        print(f"Overall: {overall_status}")

    print(f"\n🎉 Enhanced Voting Systems Demonstration Complete!")
    print("\n📊 Key Benefits:")
    print("• Multi-method validation reduces single-point-of-failure")
    print("• Confidence weighting ensures more reliable methods have greater influence")
    print("• Fallback mechanisms provide safe defaults for low-confidence scenarios")
    print("• Cross-validation between heuristic, LLM, and file analysis methods")
    print("• Detailed logging provides full audit trail of decision process")

if __name__ == "__main__":
    test_all_voting_systems()
