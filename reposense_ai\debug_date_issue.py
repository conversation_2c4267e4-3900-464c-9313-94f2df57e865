#!/usr/bin/env python3
"""
Debug Date Issue

This test checks the exact date ranges being generated and compares them
with what's in the database to find the discrepancy.
"""

import sqlite3
from datetime import datetime, timedel<PERSON>

def debug_date_ranges():
    """Debug the date range issue"""
    print("🔍 Debugging Processing Date Range Issue")
    print("=" * 60)
    
    # Get current date and time info
    now = datetime.now()
    today_str = now.strftime('%Y-%m-%d')
    
    print(f"Current datetime: {now}")
    print(f"Today string: {today_str}")
    print(f"Day of week: {now.strftime('%A')} ({now.weekday()}) [0=Monday, 6=Sunday]")
    
    # Connect to database
    conn = sqlite3.connect('/app/data/documents.db')
    cursor = conn.cursor()
    
    # Get sample processing dates
    cursor.execute('SELECT processed_time FROM documents ORDER BY processed_time DESC LIMIT 5')
    results = cursor.fetchall()
    print(f"\nSample processing dates in database:")
    for i, row in enumerate(results, 1):
        print(f"  {i}. {row[0]}")
    
    print(f"\n" + "=" * 60)
    print("TESTING EACH DATE RANGE CALCULATION")
    print("=" * 60)
    
    # Test 1: Today calculation (JavaScript logic)
    print(f"\n1️⃣ TODAY BUTTON TEST:")
    print(f"JavaScript logic: fromDate = today, toDate = today")
    js_today_from = today_str
    js_today_to = today_str
    print(f"JavaScript would send: from={js_today_from}, to={js_today_to}")
    
    # Database query for today
    db_from_today = f"{js_today_from}T00:00:00"
    db_to_today_date = datetime.strptime(js_today_to, '%Y-%m-%d') + timedelta(days=1)
    db_to_today = db_to_today_date.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"Database query: processed_time >= '{db_from_today}' AND processed_time < '{db_to_today}'")
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', 
                   (db_from_today, db_to_today))
    count_today = cursor.fetchone()[0]
    print(f"Result: {count_today} documents")
    
    # Test 2: Last 7 days calculation
    print(f"\n2️⃣ LAST 7 DAYS BUTTON TEST:")
    print(f"JavaScript logic: fromDate = today - 7 days, toDate = today")
    
    # Simulate JavaScript date calculation
    js_from_date = now - timedelta(days=7)
    js_7_from = js_from_date.strftime('%Y-%m-%d')
    js_7_to = today_str
    print(f"JavaScript would send: from={js_7_from}, to={js_7_to}")
    
    # Database query for last 7 days
    db_from_7 = f"{js_7_from}T00:00:00"
    db_to_7_date = datetime.strptime(js_7_to, '%Y-%m-%d') + timedelta(days=1)
    db_to_7 = db_to_7_date.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"Database query: processed_time >= '{db_from_7}' AND processed_time < '{db_to_7}'")
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', 
                   (db_from_7, db_to_7))
    count_7 = cursor.fetchone()[0]
    print(f"Result: {count_7} documents")
    
    # Test 3: This Week calculation (the one that works)
    print(f"\n3️⃣ THIS WEEK BUTTON TEST (WORKING):")
    print(f"JavaScript logic: Monday to Friday of current week")
    
    # Calculate Monday and Friday (JavaScript logic)
    current_day = now.weekday()  # 0=Monday, 6=Sunday
    monday_offset = -current_day  # Days to subtract to get to Monday
    friday_offset = 4 - current_day  # Days to add to get to Friday
    
    monday = now + timedelta(days=monday_offset)
    friday = now + timedelta(days=friday_offset)
    
    js_week_from = monday.strftime('%Y-%m-%d')
    js_week_to = friday.strftime('%Y-%m-%d')
    print(f"JavaScript would send: from={js_week_from}, to={js_week_to}")
    
    # Database query for this week
    db_from_week = f"{js_week_from}T00:00:00"
    db_to_week_date = datetime.strptime(js_week_to, '%Y-%m-%d') + timedelta(days=1)
    db_to_week = db_to_week_date.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"Database query: processed_time >= '{db_from_week}' AND processed_time < '{db_to_week}'")
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', 
                   (db_from_week, db_to_week))
    count_week = cursor.fetchone()[0]
    print(f"Result: {count_week} documents")
    
    print(f"\n" + "=" * 60)
    print("ANALYSIS")
    print("=" * 60)
    
    print(f"Today ({today_str}): {count_today} documents")
    print(f"Last 7 days ({js_7_from} to {js_7_to}): {count_7} documents")
    print(f"This week ({js_week_from} to {js_week_to}): {count_week} documents")
    
    # Check if today is within each range
    print(f"\nRange inclusion check:")
    print(f"Is today ({today_str}) in Today range? {js_today_from <= today_str <= js_today_to}")
    print(f"Is today ({today_str}) in Last 7 days range? {js_7_from <= today_str <= js_7_to}")
    print(f"Is today ({today_str}) in This week range? {js_week_from <= today_str <= js_week_to}")
    
    if count_week > 0 and count_today == 0:
        print(f"\n❌ ISSUE FOUND:")
        print(f"   This Week works ({count_week} docs) but Today doesn't ({count_today} docs)")
        print(f"   This suggests a problem with the Today date range calculation")
        
        # Check if there's a timezone issue
        print(f"\n🔍 Timezone Investigation:")
        cursor.execute('SELECT processed_time FROM documents WHERE processed_time LIKE ? LIMIT 1', (f'{today_str}%',))
        sample = cursor.fetchone()
        if sample:
            print(f"   Sample document from today: {sample[0]}")
            print(f"   Today range start: {db_from_today}")
            print(f"   Today range end: {db_to_today}")
            print(f"   Is sample in range? {db_from_today <= sample[0] < db_to_today}")
        else:
            print(f"   No documents found with date starting with {today_str}")
    
    conn.close()
    return count_today, count_7, count_week

if __name__ == "__main__":
    debug_date_ranges()
