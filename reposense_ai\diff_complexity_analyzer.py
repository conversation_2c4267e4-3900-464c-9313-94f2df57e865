"""
Diff Complexity Analyzer for RepoSense AI

Analyzes commit diffs to determine complexity metrics and provide
risk assessment confidence scores based on the scope and nature of changes.
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass


@dataclass
class DiffComplexityMetrics:
    """Metrics extracted from a commit diff"""
    lines_added: int = 0
    lines_removed: int = 0
    lines_modified: int = 0
    files_changed: int = 0
    characters_added: int = 0
    characters_removed: int = 0
    
    # Complexity indicators
    new_functions: int = 0
    modified_functions: int = 0
    new_classes: int = 0
    modified_classes: int = 0
    control_flow_changes: int = 0  # if/else, loops, try/catch
    import_changes: int = 0
    config_changes: int = 0
    
    # File type analysis
    code_files: int = 0
    config_files: int = 0
    documentation_files: int = 0
    test_files: int = 0
    binary_files: int = 0
    
    # Risk indicators
    large_file_changes: int = 0  # Files with >100 lines changed
    critical_file_changes: int = 0  # Changes to critical system files
    
    def total_lines_changed(self) -> int:
        """Total lines changed (added + removed)"""
        return self.lines_added + self.lines_removed
    
    def net_lines_changed(self) -> int:
        """Net lines changed (added - removed)"""
        return self.lines_added - self.lines_removed
    
    def complexity_score(self) -> float:
        """Calculate overall complexity score (0.0 - 1.0)"""
        score = 0.0
        
        # Base score from lines changed
        lines_score = min(self.total_lines_changed() / 1000.0, 0.3)  # Max 0.3 for lines
        score += lines_score
        
        # File count impact
        files_score = min(self.files_changed / 20.0, 0.2)  # Max 0.2 for files
        score += files_score
        
        # Structural changes (higher impact)
        structure_score = min((self.new_functions + self.new_classes + self.modified_functions + self.modified_classes) / 10.0, 0.3)
        score += structure_score
        
        # Control flow complexity
        control_score = min(self.control_flow_changes / 20.0, 0.1)
        score += control_score
        
        # Critical file changes
        critical_score = min(self.critical_file_changes / 5.0, 0.1)
        score += critical_score
        
        return min(score, 1.0)


class DiffComplexityAnalyzer:
    """Analyzes commit diffs to extract complexity metrics and risk indicators"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # File type patterns
        self.code_extensions = {'.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt'}
        self.config_extensions = {'.json', '.yaml', '.yml', '.xml', '.ini', '.conf', '.cfg', '.properties', '.toml'}
        self.doc_extensions = {'.md', '.txt', '.rst', '.adoc', '.tex', '.doc', '.docx'}
        self.test_patterns = {'test_', '_test', '.test.', '/test/', '/tests/', 'spec_', '_spec', '.spec.'}
        
        # Critical file patterns
        self.critical_patterns = {
            'package.json', 'requirements.txt', 'Cargo.toml', 'pom.xml', 'build.gradle',
            'Dockerfile', 'docker-compose', '.env', 'config.json', 'settings.py',
            'web.config', 'app.config', 'database.yml', 'schema.sql'
        }
        
        # Code complexity patterns
        self.function_patterns = [
            r'^\+.*def\s+\w+\s*\(',  # Python functions
            r'^\+.*function\s+\w+\s*\(',  # JavaScript functions
            r'^\+.*\w+\s+\w+\s*\([^)]*\)\s*{',  # C/Java/C# methods
        ]
        
        self.class_patterns = [
            r'^\+.*class\s+\w+',  # Python/Java/C# classes
            r'^\+.*interface\s+\w+',  # Interfaces
            r'^\+.*struct\s+\w+',  # C/C++ structs
        ]
        
        self.control_flow_patterns = [
            r'^\+.*\b(if|else|elif|while|for|switch|case|try|catch|finally|except)\b',
        ]
        
        self.import_patterns = [
            r'^\+.*\b(import|from|include|require|using)\b',
        ]
    
    def analyze_diff(self, diff_content: str, file_paths: Optional[List[str]] = None) -> Tuple[DiffComplexityMetrics, float]:
        """
        Analyze a commit diff and return complexity metrics and confidence score
        
        Args:
            diff_content: The diff content as a string
            file_paths: Optional list of changed file paths
            
        Returns:
            Tuple of (DiffComplexityMetrics, confidence_score)
        """
        try:
            metrics = DiffComplexityMetrics()

            # Analyze diff content line by line (if available)
            if diff_content:
                self._analyze_diff_lines(diff_content, metrics)

            # Analyze file paths if provided (even without diff content)
            if file_paths:
                self._analyze_file_paths(file_paths, metrics)

            # Calculate confidence score based on metrics
            confidence = self._calculate_confidence(metrics)

            self.logger.debug(f"Diff analysis: {metrics.total_lines_changed()} lines, "
                            f"{metrics.files_changed} files, complexity={metrics.complexity_score():.2f}, "
                            f"confidence={confidence:.2f}")

            return metrics, confidence
            
        except Exception as e:
            self.logger.error(f"Error analyzing diff: {e}")
            return DiffComplexityMetrics(), 0.0
    
    def _analyze_diff_lines(self, diff_content: str, metrics: DiffComplexityMetrics):
        """Analyze diff content line by line"""
        lines = diff_content.split('\n')
        current_file = None
        file_lines_changed = 0
        
        for line in lines:
            # Track file changes
            if line.startswith('+++') or line.startswith('---'):
                if current_file and file_lines_changed > 100:
                    metrics.large_file_changes += 1
                
                if line.startswith('+++'):
                    current_file = line[4:].strip()
                    file_lines_changed = 0
                    if current_file != '/dev/null':
                        metrics.files_changed += 1
                continue
            
            # Skip diff headers
            if line.startswith('@@') or line.startswith('diff ') or line.startswith('index '):
                continue
            
            # Analyze added lines
            if line.startswith('+') and not line.startswith('+++'):
                metrics.lines_added += 1
                metrics.characters_added += len(line) - 1  # Exclude the '+' prefix
                file_lines_changed += 1
                
                # Check for code complexity patterns
                self._check_complexity_patterns(line, metrics)
            
            # Analyze removed lines
            elif line.startswith('-') and not line.startswith('---'):
                metrics.lines_removed += 1
                metrics.characters_removed += len(line) - 1  # Exclude the '-' prefix
                file_lines_changed += 1
        
        # Check last file for large changes
        if current_file and file_lines_changed > 100:
            metrics.large_file_changes += 1
    
    def _check_complexity_patterns(self, line: str, metrics: DiffComplexityMetrics):
        """Check a line for complexity patterns"""
        line_lower = line.lower()
        
        # Function definitions
        for pattern in self.function_patterns:
            if re.search(pattern, line):
                metrics.new_functions += 1
                break
        
        # Class definitions
        for pattern in self.class_patterns:
            if re.search(pattern, line):
                metrics.new_classes += 1
                break
        
        # Control flow
        for pattern in self.control_flow_patterns:
            if re.search(pattern, line):
                metrics.control_flow_changes += 1
                break
        
        # Import statements
        for pattern in self.import_patterns:
            if re.search(pattern, line):
                metrics.import_changes += 1
                break
    
    def _analyze_file_paths(self, file_paths: List[str], metrics: DiffComplexityMetrics):
        """Analyze file paths to categorize file types"""
        # Set total files changed
        metrics.files_changed = len(file_paths)

        for file_path in file_paths:
            file_lower = file_path.lower()
            file_name = file_path.split('/')[-1].lower()

            # Check for critical files
            if any(pattern in file_name for pattern in self.critical_patterns):
                metrics.critical_file_changes += 1

            # Check for test files
            is_test = any(pattern in file_lower for pattern in self.test_patterns)
            if is_test:
                metrics.test_files += 1
                continue

            # Check file extensions
            extension = '.' + file_path.split('.')[-1].lower() if '.' in file_path else ''

            if extension in self.code_extensions:
                metrics.code_files += 1
            elif extension in self.config_extensions:
                metrics.config_files += 1
                metrics.config_changes += 1
            elif extension in self.doc_extensions:
                metrics.documentation_files += 1
            elif extension in {'.exe', '.dll', '.so', '.dylib', '.bin', '.img', '.iso'}:
                metrics.binary_files += 1
    
    def _calculate_confidence(self, metrics: DiffComplexityMetrics) -> float:
        """Calculate confidence score for the complexity analysis"""
        # Base confidence from having any data
        if metrics.total_lines_changed() == 0 and metrics.files_changed == 0:
            return 0.0

        # Start with base confidence
        confidence = 0.3  # Lower base confidence for file-only analysis

        # Higher confidence for line changes
        if metrics.total_lines_changed() > 0:
            confidence = 0.5  # Higher base for actual diff content
            if metrics.total_lines_changed() > 10:
                confidence += 0.2

        # File-based confidence
        if metrics.files_changed > 1:
            confidence += 0.1
        if metrics.code_files > 0:
            confidence += 0.1
        if metrics.critical_file_changes > 0:
            confidence += 0.2  # Critical files are important indicators

        # Structural changes increase confidence
        if metrics.new_functions > 0 or metrics.new_classes > 0:
            confidence += 0.1

        return min(confidence, 1.0)
    
    def get_risk_assessment(self, metrics: DiffComplexityMetrics, aggressiveness: str = "BALANCED") -> Tuple[str, float]:
        """
        Get risk assessment based on complexity metrics and aggressiveness level
        
        Args:
            metrics: The complexity metrics
            aggressiveness: Risk assessment aggressiveness level
            
        Returns:
            Tuple of (risk_level, confidence)
        """
        complexity = metrics.complexity_score()
        confidence = self._calculate_confidence(metrics)
        
        # Adjust thresholds based on aggressiveness - balanced distribution
        if aggressiveness == "CONSERVATIVE":
            critical_threshold = 0.4
            high_threshold = 0.25
            medium_threshold = 0.12
        elif aggressiveness == "BALANCED":
            critical_threshold = 0.6
            high_threshold = 0.4
            medium_threshold = 0.2
        elif aggressiveness == "AGGRESSIVE":
            critical_threshold = 0.8
            high_threshold = 0.6
            medium_threshold = 0.35
        else:  # VERY_AGGRESSIVE
            critical_threshold = 0.9   # High threshold but not impossible
            high_threshold = 0.7       # Allow some HIGH risks for truly complex changes
            medium_threshold = 0.4     # Allow MEDIUM risks for moderate complexity
        
        # Determine risk level
        if complexity >= critical_threshold:
            risk_level = "CRITICAL"
        elif complexity >= high_threshold:
            risk_level = "HIGH"
        elif complexity >= medium_threshold:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        # Adjust confidence based on specific indicators
        if metrics.critical_file_changes > 0:
            confidence = min(confidence + 0.2, 1.0)
        if metrics.large_file_changes > 0:
            confidence = min(confidence + 0.1, 1.0)
        
        return risk_level, confidence
