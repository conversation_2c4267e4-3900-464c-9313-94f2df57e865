#!/usr/bin/env python3
"""
Test SVN connection and branch discovery with the real repository
"""

import sys
import os
sys.path.append('/app')

from repository_backends import get_backend_manager
from models import RepositoryConfig, Config

def test_svn_connection():
    """Test SVN connection and branch discovery"""
    print("🔗 Testing SVN Connection and Branch Discovery")
    print("=" * 60)

    # Test with the provided credentials
    test_scenarios = [
        {"name": "Provided Credentials", "username": "f<PERSON>ei<PERSON>", "password": "ankeanke"},
        {"name": "Anonymous Access", "username": None, "password": None},
    ]

    for scenario in test_scenarios:
        print(f"\n🧪 Testing Scenario: {scenario['name']}")
        print("-" * 40)

        # Create a test repository config
        repo_config = RepositoryConfig(
            name="reposense_branch_tag_test",
            url="http://sundc:81/svn/reposense_branch_tag_test",
            type="svn",
            username=scenario["username"],
            password=scenario["password"],
            enabled=True,
            monitor_all_branches=True
        )

        if test_single_scenario(repo_config):
            print(f"   ✅ {scenario['name']} worked!")
            break
        else:
            print(f"   ❌ {scenario['name']} failed")

    print(f"\n💡 If all scenarios failed, the repository requires specific credentials.")
    print(f"   Please provide the correct username/password for the SVN repository.")

def test_single_scenario(repo_config):
    """Test a single credential scenario"""
    try:
        print(f"   📋 Testing with credentials: {repo_config.username or 'Anonymous'}")

        # Get backend manager and SVN backend
        backend_manager = get_backend_manager()
        config = Config()  # Empty config for backend access

        backend = backend_manager.get_backend_for_repository(repo_config, config)
        if not backend:
            print(f"      ❌ No backend available")
            return False

        # Test basic SVN info with timeout
        print(f"      📡 Testing SVN info...")
        latest_revision = backend.get_latest_revision(repo_config)
        if latest_revision:
            print(f"      ✅ Latest revision: {latest_revision}")

            # Test branch discovery if basic access works
            print(f"      🌿 Testing branch discovery...")
            discovered_branches = backend._discover_svn_branches(
                repo_config.url,
                repo_config.name,
                repo_config.username,
                repo_config.password
            )

            if discovered_branches:
                print(f"      ✅ Discovered {len(discovered_branches)} branches:")
                for branch in discovered_branches:
                    print(f"         - {branch.name} ({branch.path})")
                return True
            else:
                print(f"      ⚠️  No branches discovered, but connection works")
                return True
        else:
            print(f"      ❌ Could not get revision info")
            return False

    except Exception as e:
        print(f"      ❌ Error: {str(e)[:100]}...")
        return False

if __name__ == "__main__":
    test_svn_connection()
    print(f"\n✅ SVN connection test completed!")
