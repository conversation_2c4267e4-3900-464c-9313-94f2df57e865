#!/usr/bin/env python3
"""
Fix revision 12 by processing the existing file through the unified processor
"""

import sys
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.append('/app')

from unified_document_processor import UnifiedDocumentProcessor
from monitor_service import MonitorService

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def fix_revision_12():
    """Fix revision 12 by processing the existing file"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🔧 Starting revision 12 fix...")
    
    # Check if the file exists
    file_path = Path('/app/data/output/repositories/reposense_cpp_test/docs/revision_12.md')
    if not file_path.exists():
        logger.error(f"❌ File does not exist: {file_path}")
        return False
    
    logger.info(f"✅ Found file: {file_path}")
    
    try:
        # Initialize monitor service to get configuration
        monitor_service = MonitorService("/app/data/config.json")
        
        # Create unified processor
        processor = UnifiedDocumentProcessor(
            output_dir=monitor_service.config.output_dir,
            db_path="/app/data/documents.db",
            ollama_client=monitor_service.ollama_client,
            config_manager=monitor_service.config_manager,
            max_concurrent_tasks=1
        )
        
        # Start the processor
        processor.start()
        logger.info("✅ Unified processor started")
        
        # Force process the file by using scan_file_system with force_rescan=True
        # This bypasses the modification time check that prevents reprocessing
        logger.info("🔄 Force scanning file system to process revision_12.md...")
        queued_count = processor.scan_file_system(force_rescan=True)
        logger.info(f"📄 Force scan queued {queued_count} files for processing")

        success = queued_count > 0
        
        if success:
            # Wait for processing to complete
            import time
            logger.info("⏳ Waiting for processing to complete...")

            # Wait and monitor progress
            for i in range(6):  # Wait up to 60 seconds
                time.sleep(10)
                stats = processor.get_stats()
                logger.info(f"⏳ Progress check {i+1}/6: processed={stats['processed_count']}, queue={stats['queue_size']}, errors={stats['error_count']}")

                # If queue is empty and we've processed some files, we're likely done
                if stats['queue_size'] == 0 and stats['processed_count'] > 0:
                    logger.info("✅ Processing appears complete (queue empty)")
                    break
            
            # Check statistics
            stats = processor.get_stats()
            logger.info(f"📊 Processing Statistics:")
            logger.info(f"  Processed count: {stats['processed_count']}")
            logger.info(f"  Error count: {stats['error_count']}")
            logger.info(f"  Queue size: {stats['queue_size']}")
            
            # Verify the document was added to database
            from document_database import DocumentDatabase
            db = DocumentDatabase("/app/data/documents.db")
            
            # Look for revision 12 documents
            docs = db.get_documents(limit=50)
            revision_12_docs = [doc for doc in docs if doc.revision == 12]
            
            if revision_12_docs:
                logger.info(f"✅ SUCCESS! Found {len(revision_12_docs)} revision 12 document(s) in database:")
                for doc in revision_12_docs:
                    logger.info(f"  - ID: {doc.id}")
                    logger.info(f"  - Repository: {doc.repository_name}")
                    logger.info(f"  - Revision: {doc.revision}")
                    logger.info(f"  - Author: {doc.author}")
                    logger.info(f"  - Date: {doc.date}")
            else:
                logger.error("❌ FAILED! No revision 12 documents found in database")
                return False
        
        # Stop the processor
        processor.stop()
        logger.info("✅ Unified processor stopped")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error during fix: {e}")
        return False

if __name__ == "__main__":
    success = fix_revision_12()
    if success:
        print("🎉 Revision 12 fix completed successfully!")
        sys.exit(0)
    else:
        print("💥 Revision 12 fix failed!")
        sys.exit(1)
