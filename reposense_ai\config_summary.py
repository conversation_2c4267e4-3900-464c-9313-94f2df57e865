#!/usr/bin/env python3
"""
RepoSense AI Configuration Summary

Shows the final effective configuration that will be used by the application.
This takes into account the priority order:
1. Environment Variables (highest priority)
2. Configuration File 
3. Application Defaults (lowest priority)
"""

import json
import os
import sys
from pathlib import Path

def get_effective_config():
    """Get the effective configuration that will actually be used"""
    
    # Step 1: Find the config file (only in data directory)
    config_paths = [
        "/app/data/config.json",
        "data/config.json"
    ]
    
    config_file_data = {}
    config_file_path = None
    
    for path in config_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    config_file_data = json.load(f)
                config_file_path = path
                break
            except Exception as e:
                print(f"⚠️ Error reading {path}: {e}")
    
    # Step 2: Get application defaults (from models.py Config class)
    defaults = {
        'ollama_host': 'http://localhost:11434',  # Default to localhost for local development
        'ollama_model': 'qwen3',
        'web_host': '0.0.0.0',
        'web_port': 5000,
        'check_interval': 300
    }
    
    # Step 3: Apply config file values
    effective_config = defaults.copy()
    effective_config.update(config_file_data)
    
    # Step 4: Apply environment variable overrides
    env_overrides = {}
    
    if 'OLLAMA_BASE_URL' in os.environ:
        env_overrides['ollama_host'] = os.environ['OLLAMA_BASE_URL']
        effective_config['ollama_host'] = os.environ['OLLAMA_BASE_URL']
    
    if 'OLLAMA_MODEL' in os.environ:
        env_overrides['ollama_model'] = os.environ['OLLAMA_MODEL']
        effective_config['ollama_model'] = os.environ['OLLAMA_MODEL']
    
    if 'REPOSENSE_AI_WEB_HOST' in os.environ:
        env_overrides['web_host'] = os.environ['REPOSENSE_AI_WEB_HOST']
        effective_config['web_host'] = os.environ['REPOSENSE_AI_WEB_HOST']
    
    if 'REPOSENSE_AI_WEB_PORT' in os.environ:
        try:
            env_overrides['web_port'] = int(os.environ['REPOSENSE_AI_WEB_PORT'])
            effective_config['web_port'] = int(os.environ['REPOSENSE_AI_WEB_PORT'])
        except ValueError:
            print(f"⚠️ Invalid REPOSENSE_AI_WEB_PORT: {os.environ['REPOSENSE_AI_WEB_PORT']}")
    
    return {
        'effective_config': effective_config,
        'config_file_path': config_file_path,
        'config_file_data': config_file_data,
        'env_overrides': env_overrides,
        'defaults': defaults
    }

def print_config_summary():
    """Print a clear summary of the configuration"""
    
    result = get_effective_config()
    
    print("🔧 RepoSense AI Configuration Summary")
    print("=" * 50)
    
    # Show config file
    if result['config_file_path']:
        print(f"📄 Config File: {result['config_file_path']}")
    else:
        print("📄 Config File: ❌ NOT FOUND (using defaults)")
    
    # Show environment overrides
    if result['env_overrides']:
        print(f"🔧 Environment Overrides: {len(result['env_overrides'])} active")
        for key, value in result['env_overrides'].items():
            print(f"   {key}: {value}")
    else:
        print("🔧 Environment Overrides: None")
    
    print("\n🎯 EFFECTIVE CONFIGURATION (what the app will actually use):")
    print("-" * 50)
    
    key_configs = ['ollama_host', 'ollama_model', 'web_host', 'web_port', 'check_interval']
    
    for key in key_configs:
        value = result['effective_config'].get(key, 'NOT_SET')
        
        # Show source
        if key in result['env_overrides']:
            source = "🔧 Environment Variable"
        elif key in result['config_file_data']:
            source = "📄 Config File"
        else:
            source = "⚙️ Default"
        
        print(f"{key:15} = {value:30} ({source})")
    
    # Validation
    print(f"\n✅ VALIDATION:")
    print("-" * 20)
    
    issues = []
    
    # Check Ollama host
    ollama_host = result['effective_config'].get('ollama_host', '')
    if 'ollama-server-local' in ollama_host:
        issues.append("❌ ollama_host contains 'ollama-server-local' (likely incorrect)")
    elif ollama_host.startswith('http://'):
        print("✅ Ollama host format looks correct")
    else:
        issues.append("⚠️ Ollama host doesn't start with http://")
    
    # Check model
    ollama_model = result['effective_config'].get('ollama_model', '')
    if ollama_model == 'llama2':
        issues.append("⚠️ Using legacy default model 'llama2' - consider upgrading to 'qwen3' for better performance")
    elif ollama_model:
        print(f"✅ Ollama model specified: {ollama_model}")
    else:
        issues.append("❌ No Ollama model specified")
    
    if issues:
        print(f"\n⚠️ ISSUES FOUND:")
        for issue in issues:
            print(f"   {issue}")
    else:
        print("✅ No issues found!")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print("-" * 20)
    
    if 'ollama-server-local' in ollama_host:
        print("1. Fix ollama_host - use either:")
        print("   - http://ollama:11434 (for Docker containers)")
        print("   - http://************:11434 (for external access)")
    
    if ollama_model == 'llama2':
        print("2. Update ollama_model to an available model:")
        print("   - codeqwen:7b-chat-v1.5-q8_0 (good for code)")
        print("   - qwen2.5:14b (general purpose)")
        print("   - llama3.1:latest (general purpose)")
    
    if not result['env_overrides']:
        print("3. Consider using environment variables for deployment:")
        print("   - OLLAMA_BASE_URL=http://ollama:11434")
        print("   - OLLAMA_MODEL=codeqwen:7b-chat-v1.5-q8_0")

def main():
    try:
        print_config_summary()
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
