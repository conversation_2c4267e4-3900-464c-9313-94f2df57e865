#!/usr/bin/env python3
"""
Enhanced Prompt Templates for AI Analysis
Provides contextual, specialized prompts for different types of code changes
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from models import Config
from document_database import DocumentRecord


class ChangeType(Enum):
    """Types of code changes for specialized analysis"""
    SECURITY = "security"
    BUSINESS_LOGIC = "business_logic"
    UI_FRONTEND = "ui_frontend"
    INFRASTRUCTURE = "infrastructure"
    DATABASE = "database"
    API = "api"
    CONFIGURATION = "configuration"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    REFACTORING = "refactoring"
    BUGFIX = "bugfix"
    FEATURE = "feature"
    GENERAL = "general"


class RiskContext(Enum):
    """Risk context levels for different environments"""
    PRODUCTION_CRITICAL = "production_critical"
    PRODUCTION_STANDARD = "production_standard"
    STAGING = "staging"
    DEVELOPMENT = "development"
    EXPERIMENTAL = "experimental"


@dataclass
class ChangeContext:
    """Context information for a code change"""
    # Basic change information
    change_type: ChangeType
    risk_context: RiskContext
    files_changed: int
    lines_added: int
    lines_removed: int
    
    # Repository context
    repository_name: str
    repository_type: str  # svn, git
    programming_languages: List[str]
    project_type: str  # web_app, library, service, etc.
    
    # Change characteristics
    affects_core_logic: bool
    affects_security: bool
    affects_user_interface: bool
    affects_database: bool
    affects_api: bool
    affects_configuration: bool
    
    # Historical context
    author_experience_level: str  # senior, intermediate, junior, unknown
    similar_changes_count: int
    recent_issues_in_area: int
    
    # Timing context
    is_pre_release: bool
    is_hotfix: bool
    is_maintenance_window: bool
    
    # File criticality
    critical_files_changed: List[str]
    test_files_changed: List[str]
    config_files_changed: List[str]

    # Product documentation context (RAG-extracted)
    product_overview: str = ""
    key_features: str = ""
    changelog_structure: str = ""
    documentation_style: str = ""
    project_goals: str = ""
    user_audience: str = ""
    technical_stack: str = ""
    deployment_info: str = ""


class PromptTemplateManager:
    """Manages enhanced prompt templates with contextual information"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize template cache
        self._template_cache: Dict[str, Any] = {}
        
    def get_metadata_extraction_prompt(self, content: str, context: ChangeContext) -> Tuple[str, str]:
        """Get enhanced metadata extraction prompt with context"""
        
        # Build context-aware system prompt
        system_prompt = self._build_metadata_system_prompt(context)
        
        # Build context-aware user prompt
        user_prompt = self._build_metadata_user_prompt(content, context)
        
        return system_prompt, user_prompt
    
    def get_documentation_generation_prompt(self, commit_info: Any, context: ChangeContext) -> Tuple[str, str]:
        """Get enhanced documentation generation prompt with context"""
        # For now, return basic prompts - this can be enhanced later
        system_prompt = "You are a technical documentation generator."
        user_prompt = f"Generate documentation for this change in {context.repository_name}"
        return system_prompt, user_prompt

    def get_product_documentation_prompt(self, document: DocumentRecord,
                                       technical_content: str,
                                       context: ChangeContext,
                                       existing_docs: Optional[str] = None) -> Tuple[str, str]:
        """Get enhanced product documentation prompt with context"""
        # For now, return basic prompts - this can be enhanced later
        system_prompt = "You are a product documentation specialist."
        user_prompt = f"Analyze this change for product documentation impact in {context.repository_name}"
        return system_prompt, user_prompt
    
    def _build_metadata_system_prompt(self, context: ChangeContext) -> str:
        """Build context-aware system prompt for metadata extraction"""
        
        base_prompt = """You are an expert code review and risk assessment specialist with deep knowledge of software development practices. Your task is to analyze technical documentation and extract precise metadata for development workflow decisions.

ANALYSIS FRAMEWORK:
You must consider multiple dimensions when making assessments:"""

        # Add context-specific guidance
        context_guidance = self._get_context_specific_guidance(context)
        
        risk_criteria = self._get_risk_assessment_criteria(context)
        
        review_criteria = self._get_code_review_criteria(context)
        
        output_format = """
OUTPUT FORMAT:
Return ONLY a valid JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "CRITICAL"/"HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- code_review_confidence: 0.0-1.0 (confidence in recommendation)
- risk_level: "CRITICAL"/"HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- risk_confidence: 0.0-1.0 (confidence in risk assessment, where 1.0 = completely certain)
- documentation_impact: true/false (whether documentation needs updates)
- documentation_confidence: 0.0-1.0 (confidence in documentation impact assessment)
- reasoning: "Brief explanation of key factors in decision"

DOCUMENTATION IMPACT GUIDELINES - Be CONSERVATIVE:
- documentation_impact should be FALSE (no updates needed) for:
  * Internal refactoring, code cleanup, performance optimizations
  * Bug fixes that don't change behavior or APIs
  * Test additions, code formatting, style improvements
  * Internal implementation changes without user-facing impact
  * Private/internal method changes
- documentation_impact should be TRUE (updates needed) ONLY for:
  * New public APIs, endpoints, or user-facing features
  * Breaking changes that affect external users
  * New configuration options or environment variables
  * Changes to command-line interfaces or user workflows
  * Security changes that affect user procedures

CRITICAL risk level should be used for:
- Security vulnerabilities, buffer overflows, memory corruption
- Race conditions, deadlocks, critical threading issues
- Production system failures, data loss risks
- Multiple high-risk factors combined

Be precise, consistent, and conservative in your assessments. If you cannot determine a value with reasonable confidence, use null and explain in reasoning."""

        return f"{base_prompt}\n\n{context_guidance}\n\n{risk_criteria}\n\n{review_criteria}\n\n{output_format}"
    
    def _get_context_specific_guidance(self, context: ChangeContext) -> str:
        """Get guidance specific to the change context"""

        guidance_parts = []

        # Product documentation context (RAG-extracted)
        if context.product_overview:
            guidance_parts.append(f"- PRODUCT CONTEXT: {context.product_overview}")

        if context.key_features:
            guidance_parts.append(f"- KEY FEATURES: {context.key_features}")

        if context.user_audience:
            if context.user_audience == "developers":
                guidance_parts.append("- TARGET AUDIENCE: Developers - focus on API impact, breaking changes, and technical documentation")
            elif context.user_audience == "end-users":
                guidance_parts.append("- TARGET AUDIENCE: End-users - focus on user experience, feature changes, and user documentation")

        if context.technical_stack:
            guidance_parts.append(f"- TECHNICAL STACK: {context.technical_stack}")

        if context.changelog_structure:
            guidance_parts.append(f"- CHANGELOG STYLE: {context.changelog_structure}")

        # Project type specific guidance
        if context.project_type == "web_app":
            guidance_parts.append("- This is a web application - consider user-facing impact, security implications, and performance")
        elif context.project_type == "library":
            guidance_parts.append("- This is a library - consider API compatibility, breaking changes, and downstream impact")
        elif context.project_type == "service":
            guidance_parts.append("- This is a service - consider availability, performance, and integration points")
        
        # Change type specific guidance
        if context.change_type == ChangeType.SECURITY:
            guidance_parts.append("- SECURITY CHANGE: Apply highest scrutiny - any security change requires thorough review")
        elif context.change_type == ChangeType.DATABASE:
            guidance_parts.append("- DATABASE CHANGE: Consider data integrity, migration risks, and performance impact")
        elif context.change_type == ChangeType.API:
            guidance_parts.append("- API CHANGE: Evaluate backward compatibility and client impact")
        
        # Risk context guidance
        if context.risk_context == RiskContext.PRODUCTION_CRITICAL:
            guidance_parts.append("- PRODUCTION CRITICAL: Use maximum caution - prefer HIGH risk and review recommendations")
        elif context.risk_context == RiskContext.DEVELOPMENT:
            guidance_parts.append("- DEVELOPMENT ENVIRONMENT: More tolerance for experimentation, but maintain quality standards")
        
        return "CONTEXT-SPECIFIC GUIDANCE:\n" + "\n".join(guidance_parts) if guidance_parts else ""

    def _get_risk_assessment_criteria(self, context: ChangeContext) -> str:
        """Get risk assessment criteria based on context"""

        base_criteria = """RISK ASSESSMENT CRITERIA:
HIGH RISK indicators:
- Changes to authentication, authorization, or security systems
- Database schema changes or data migration scripts
- Core business logic modifications in critical paths
- API breaking changes or major interface modifications
- Infrastructure changes affecting availability or performance
- Changes to financial, payment, or sensitive data processing
- Large refactoring (>500 lines changed) in core systems"""

        context_criteria = []

        # Add context-specific risk factors
        if context.affects_security:
            context_criteria.append("- ANY security-related change automatically elevates risk")

        if context.affects_database:
            context_criteria.append("- Database changes require careful migration and rollback planning")

        if context.is_pre_release:
            context_criteria.append("- Pre-release timing increases risk due to limited testing time")

        if context.is_hotfix:
            context_criteria.append("- Hotfix context requires balance between urgency and safety")

        if context.files_changed > 10:
            context_criteria.append(f"- Large change scope ({context.files_changed} files) increases complexity risk")

        if context.recent_issues_in_area > 0:
            context_criteria.append(f"- Recent issues in this area ({context.recent_issues_in_area}) suggest higher risk")

        # Add author experience context
        if context.author_experience_level == "junior":
            context_criteria.append("- Junior author may require additional review attention")
        elif context.author_experience_level == "senior":
            context_criteria.append("- Senior author experience may reduce some risk factors")

        medium_criteria = """
MEDIUM RISK indicators:
- Moderate refactoring or code reorganization
- New feature additions with existing patterns
- Configuration changes with clear rollback paths
- UI/UX changes that don't affect core functionality
- Test additions or improvements
- Documentation updates with code changes"""

        low_criteria = """
LOW RISK indicators:
- Pure documentation changes
- Test-only changes
- Code formatting or style improvements
- Comment additions or improvements
- Minor bug fixes in non-critical areas
- Development/debugging tool changes"""

        context_specific = "\n".join(context_criteria) if context_criteria else ""

        return f"{base_criteria}\n\n{context_specific}\n\n{medium_criteria}\n\n{low_criteria}"

    def _get_code_review_criteria(self, context: ChangeContext) -> str:
        """Get code review criteria based on context"""

        base_criteria = """CODE REVIEW RECOMMENDATION CRITERIA:
ALWAYS RECOMMEND REVIEW for:
- Security-related changes (authentication, authorization, encryption)
- Database schema or migration changes
- API changes affecting external clients
- Core business logic modifications
- Infrastructure or deployment configuration changes
- Changes by junior developers in critical areas
- Large changes (>200 lines) in any area"""

        context_criteria = []

        # Context-specific review triggers
        if context.change_type in [ChangeType.SECURITY, ChangeType.DATABASE, ChangeType.API]:
            context_criteria.append(f"- {context.change_type.value.upper()} changes always require review")

        if context.affects_core_logic:
            context_criteria.append("- Core business logic changes require review")

        if context.critical_files_changed:
            context_criteria.append(f"- Critical files modified: {', '.join(context.critical_files_changed[:3])}")

        if context.lines_added + context.lines_removed > 200:
            context_criteria.append(f"- Large change size ({context.lines_added + context.lines_removed} lines)")

        priority_criteria = """
REVIEW PRIORITY LEVELS:
HIGH PRIORITY:
- Security vulnerabilities or security system changes
- Production hotfixes
- Breaking API changes
- Database migrations affecting live data
- Critical system component changes

MEDIUM PRIORITY:
- New features in existing systems
- Moderate refactoring
- Configuration changes
- Non-breaking API additions

LOW PRIORITY:
- Documentation improvements
- Test additions
- Code style improvements
- Minor bug fixes in non-critical areas"""

        context_specific = "\n".join(context_criteria) if context_criteria else ""

        return f"{base_criteria}\n\n{context_specific}\n\n{priority_criteria}"

    def _build_metadata_user_prompt(self, content: str, context: ChangeContext) -> str:
        """Build context-aware user prompt for metadata extraction"""

        # Build context summary
        context_summary = self._build_context_summary(context)

        # Build change analysis section
        change_analysis = f"""
CHANGE ANALYSIS:
- Repository: {context.repository_name} ({context.repository_type})
- Project Type: {context.project_type}
- Change Type: {context.change_type.value}
- Files Changed: {context.files_changed}
- Lines Added/Removed: +{context.lines_added}/-{context.lines_removed}
- Programming Languages: {', '.join(context.programming_languages)}
- Author Experience: {context.author_experience_level}"""

        # Add critical file information if available
        if context.critical_files_changed:
            change_analysis += f"\n- Critical Files: {', '.join(context.critical_files_changed[:5])}"

        if context.test_files_changed:
            change_analysis += f"\n- Test Files: {', '.join(context.test_files_changed[:3])}"

        # Build impact flags
        impact_flags = []
        if context.affects_core_logic:
            impact_flags.append("Core Logic")
        if context.affects_security:
            impact_flags.append("Security")
        if context.affects_database:
            impact_flags.append("Database")
        if context.affects_api:
            impact_flags.append("API")
        if context.affects_user_interface:
            impact_flags.append("User Interface")
        if context.affects_configuration:
            impact_flags.append("Configuration")

        if impact_flags:
            change_analysis += f"\n- Impact Areas: {', '.join(impact_flags)}"

        # Build timing context
        timing_flags = []
        if context.is_pre_release:
            timing_flags.append("Pre-release")
        if context.is_hotfix:
            timing_flags.append("Hotfix")
        if context.is_maintenance_window:
            timing_flags.append("Maintenance Window")

        if timing_flags:
            change_analysis += f"\n- Timing Context: {', '.join(timing_flags)}"

        # Historical context
        if context.similar_changes_count > 0:
            change_analysis += f"\n- Similar Changes Previously: {context.similar_changes_count}"

        if context.recent_issues_in_area > 0:
            change_analysis += f"\n- Recent Issues in Area: {context.recent_issues_in_area}"

        # Add product documentation context if available
        product_context_parts = []
        if context.product_overview:
            product_context_parts.append(f"Overview: {context.product_overview[:200]}...")
        if context.project_goals:
            product_context_parts.append(f"Goals: {context.project_goals[:150]}...")
        if context.deployment_info:
            product_context_parts.append(f"Deployment: {context.deployment_info[:150]}...")

        if product_context_parts:
            change_analysis += f"\n- Product Context: {' | '.join(product_context_parts)}"

        prompt = f"""{context_summary}

{change_analysis}

TECHNICAL DOCUMENTATION TO ANALYZE:
{content}

Based on the context and documentation above, provide your assessment as a JSON object."""

        return prompt

    def _build_context_summary(self, context: ChangeContext) -> str:
        """Build a summary of the change context"""

        summary_parts = []

        # Risk context
        if context.risk_context == RiskContext.PRODUCTION_CRITICAL:
            summary_parts.append("🔴 PRODUCTION CRITICAL SYSTEM")
        elif context.risk_context == RiskContext.PRODUCTION_STANDARD:
            summary_parts.append("🟡 PRODUCTION SYSTEM")
        elif context.risk_context == RiskContext.STAGING:
            summary_parts.append("🟠 STAGING ENVIRONMENT")
        else:
            summary_parts.append("🟢 DEVELOPMENT ENVIRONMENT")

        # Change magnitude
        total_lines = context.lines_added + context.lines_removed
        if total_lines > 500:
            summary_parts.append("📊 LARGE CHANGE")
        elif total_lines > 100:
            summary_parts.append("📊 MEDIUM CHANGE")
        else:
            summary_parts.append("📊 SMALL CHANGE")

        # Special flags
        if context.affects_security:
            summary_parts.append("🔒 SECURITY IMPACT")

        if context.affects_database:
            summary_parts.append("🗄️ DATABASE IMPACT")

        if context.is_hotfix:
            summary_parts.append("🚨 HOTFIX")

        return "CONTEXT SUMMARY: " + " | ".join(summary_parts)


class ContextAnalyzer:
    """Analyzes code changes to determine context for enhanced prompts"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Import helper methods
        from context_analyzer_helpers import ContextAnalyzerHelpers
        self.helpers = ContextAnalyzerHelpers()

    def analyze_change_context(self, document: DocumentRecord,
                             changed_files: Optional[List[str]] = None,
                             commit_message: Optional[str] = None,
                             diff_content: Optional[str] = None) -> ChangeContext:
        """Analyze a change and determine its context"""

        # Get basic information
        repository_name = document.repository_name or "Unknown"
        repository_type = self.helpers.detect_repository_type(document)

        # Analyze changed files
        if changed_files is None:
            changed_files = self.helpers.extract_changed_files(document, diff_content)

        # Ensure we have a valid list
        if changed_files is None:
            changed_files = []

        # Detect change characteristics
        change_type = self.helpers.detect_change_type(changed_files, commit_message, diff_content)
        programming_languages = self.helpers.detect_programming_languages(changed_files)
        project_type = self.helpers.detect_project_type(changed_files, repository_name)

        # Analyze change magnitude
        lines_added, lines_removed = self.helpers.analyze_change_magnitude(diff_content)

        # Detect impact areas
        affects_core_logic = self.helpers.affects_core_logic(changed_files, commit_message)
        affects_security = self.helpers.affects_security(changed_files, commit_message, diff_content)
        affects_user_interface = self.helpers.affects_ui(changed_files)
        affects_database = self.helpers.affects_database(changed_files, commit_message)
        affects_api = self.helpers.affects_api(changed_files, commit_message)
        affects_configuration = self.helpers.affects_configuration(changed_files)

        # Determine risk context
        risk_context = self._determine_risk_context(document, change_type)

        # Analyze author and historical context
        author_experience_level = self._analyze_author_experience(document.author)
        similar_changes_count = 0  # TODO: Implement historical analysis
        recent_issues_in_area = 0  # TODO: Implement issue tracking integration

        # Analyze timing context
        is_pre_release = self._is_pre_release_timing()
        is_hotfix = self._is_hotfix(commit_message)
        is_maintenance_window = self._is_maintenance_window()

        # Categorize files
        critical_files_changed = self._identify_critical_files(changed_files)
        test_files_changed = self._identify_test_files(changed_files)
        config_files_changed = self._identify_config_files(changed_files)

        # Extract product documentation context using RAG
        product_doc_context = self.helpers.extract_product_documentation_context(document)

        return ChangeContext(
            change_type=change_type,
            risk_context=risk_context,
            files_changed=len(changed_files),
            lines_added=lines_added,
            lines_removed=lines_removed,
            repository_name=repository_name,
            repository_type=repository_type,
            programming_languages=programming_languages,
            project_type=project_type,
            affects_core_logic=affects_core_logic,
            affects_security=affects_security,
            affects_user_interface=affects_user_interface,
            affects_database=affects_database,
            affects_api=affects_api,
            affects_configuration=affects_configuration,
            author_experience_level=author_experience_level,
            similar_changes_count=similar_changes_count,
            recent_issues_in_area=recent_issues_in_area,
            is_pre_release=is_pre_release,
            is_hotfix=is_hotfix,
            is_maintenance_window=is_maintenance_window,
            critical_files_changed=critical_files_changed,
            test_files_changed=test_files_changed,
            config_files_changed=config_files_changed,
            # Product documentation context
            product_overview=product_doc_context.get('product_overview', ''),
            key_features=product_doc_context.get('key_features', ''),
            changelog_structure=product_doc_context.get('changelog_structure', ''),
            documentation_style=product_doc_context.get('documentation_style', ''),
            project_goals=product_doc_context.get('project_goals', ''),
            user_audience=product_doc_context.get('user_audience', ''),
            technical_stack=product_doc_context.get('technical_stack', ''),
            deployment_info=product_doc_context.get('deployment_info', '')
        )

    def _determine_risk_context(self, document: DocumentRecord, change_type: ChangeType) -> RiskContext:
        """Determine risk context based on document and change type"""

        # Check if this is a production system
        repo_name = getattr(document, 'repository_name', '').lower()

        if any(indicator in repo_name for indicator in ['prod', 'production', 'live']):
            if change_type in [ChangeType.SECURITY, ChangeType.DATABASE]:
                return RiskContext.PRODUCTION_CRITICAL
            else:
                return RiskContext.PRODUCTION_STANDARD

        if any(indicator in repo_name for indicator in ['staging', 'stage', 'test']):
            return RiskContext.STAGING

        if any(indicator in repo_name for indicator in ['dev', 'development', 'experimental']):
            return RiskContext.DEVELOPMENT

        # Default to production standard for unknown environments
        return RiskContext.PRODUCTION_STANDARD

    def _analyze_author_experience(self, author: Optional[str]) -> str:
        """Analyze author experience level (placeholder implementation)"""
        if not author:
            return "unknown"

        # This is a placeholder - in a real implementation, you would:
        # 1. Look up author in a database of team members
        # 2. Analyze their commit history
        # 3. Check their role/seniority level

        # For now, return unknown
        return "unknown"

    def _is_pre_release_timing(self) -> bool:
        """Check if we're in a pre-release period (placeholder)"""
        # This would check against release schedules, sprint timelines, etc.
        return False

    def _is_hotfix(self, commit_message: Optional[str]) -> bool:
        """Check if this is a hotfix based on commit message"""
        if not commit_message:
            return False

        hotfix_indicators = ['hotfix', 'urgent', 'critical', 'emergency', 'fix']
        message_lower = commit_message.lower()

        return any(indicator in message_lower for indicator in hotfix_indicators)

    def _is_maintenance_window(self) -> bool:
        """Check if we're in a maintenance window (placeholder)"""
        # This would check against scheduled maintenance windows
        return False

    def _identify_critical_files(self, changed_files: List[str]) -> List[str]:
        """Identify critical files from the changed files list"""
        critical_files = []
        critical_patterns = [
            r'auth', r'login', r'password', r'payment', r'billing',
            r'core', r'main', r'index', r'app', r'server',
            r'database', r'migration', r'schema'
        ]

        for file_path in changed_files:
            file_lower = file_path.lower()
            if any(re.search(pattern, file_lower) for pattern in critical_patterns):
                critical_files.append(file_path)

        return critical_files

    def _identify_test_files(self, changed_files: List[str]) -> List[str]:
        """Identify test files from the changed files list"""
        test_files = []
        test_patterns = [r'test', r'spec', r'\.test\.', r'\.spec\.', r'mock', r'stub']

        for file_path in changed_files:
            file_lower = file_path.lower()
            if any(re.search(pattern, file_lower) for pattern in test_patterns):
                test_files.append(file_path)

        return test_files

    def _identify_config_files(self, changed_files: List[str]) -> List[str]:
        """Identify configuration files from the changed files list"""
        config_files = []
        config_extensions = ['.json', '.yml', '.yaml', '.ini', '.conf', '.env', '.properties']
        config_patterns = [r'config', r'settings', r'properties']

        for file_path in changed_files:
            file_lower = file_path.lower()

            # Check extensions
            if any(file_lower.endswith(ext) for ext in config_extensions):
                config_files.append(file_path)
            # Check patterns
            elif any(re.search(pattern, file_lower) for pattern in config_patterns):
                config_files.append(file_path)

        return config_files
