#!/usr/bin/env python3
"""
Test document generation with proper Ollama client
"""

import sys
import os
sys.path.append('/app')

from config_manager import ConfigManager
from unified_document_processor import UnifiedDocumentProcessor
from ollama_client import OllamaClient
from repository_backends.svn_backend import SVNBackend
from models import CommitInfo
from datetime import datetime

def test_document_generation():
    """Test document generation with proper setup"""
    print("🧪 Testing Document Generation")
    print("=" * 60)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Create Ollama client (same as web interface)
        print("1. Creating Ollama client...")
        ollama_client = OllamaClient(config)
        
        # Test Ollama connectivity
        try:
            models = ollama_client.get_available_models()
            print(f"   ✅ Ollama client working: {len(models)} models available")
            print(f"   Models: {models[:3]}...")  # Show first 3 models
        except Exception as e:
            print(f"   ❌ Ollama client error: {e}")
            return False
        
        # Create unified processor with proper Ollama client
        print("2. Creating UnifiedDocumentProcessor with Ollama client...")
        processor = UnifiedDocumentProcessor(
            output_dir=config.output_dir,
            db_path="/app/data/documents.db",
            ollama_client=ollama_client,  # Pass the working Ollama client
            config_manager=config_manager,
            max_concurrent_tasks=1  # Single thread for testing
        )
        
        print("3. Starting processor...")
        processor.start()
        
        # Check processor state
        stats = processor.get_stats()
        print(f"   Running: {stats['running']}")
        print(f"   Has Ollama client: {processor.ollama_client is not None}")
        print(f"   Metadata extractor has Ollama client: {processor.metadata_extractor.ollama_client is not None}")
        
        # Get repository and commit info
        print("4. Getting repository and commit info...")
        repo_config = config.get_repository_by_id('c42f3018-3ffc-4434-91df-e1d1d892bb9e')
        if not repo_config:
            print("   ❌ Repository not found")
            return False
        
        backend = SVNBackend(repo_config)
        commit_info = backend.get_commit_info(repo_config, '17')
        if not commit_info:
            print("   ❌ Commit info not found")
            return False
        
        print(f"   ✅ Repository: {repo_config.name}")
        print(f"   ✅ Commit: {commit_info.author} - {commit_info.message}")
        
        # Queue the processing task
        print("5. Queuing processing task...")
        success = processor.process_commit(
            commit_info=commit_info,
            repository_config=repo_config,
            documentation="",  # Will be regenerated
            priority=10  # High priority
        )
        
        print(f"   Queue result: {success}")
        
        if success:
            # Monitor processing
            print("6. Monitoring processing...")
            
            import time
            for i in range(30):  # Monitor for 60 seconds
                time.sleep(2)
                stats = processor.get_stats()
                print(f"   [{i*2:2d}s] Queue: {stats['queue_size']}, Active: {stats['active_threads']}, Processed: {stats['processed_count']}, Errors: {stats['error_count']}")
                
                # Check if document was created
                # repo_config.name now includes branch path (e.g., "reposense_cpp_test/trunk")
                expected_file = f"/app/data/output/repositories/{repo_config.name}/docs/revision_17.md"
                if os.path.exists(expected_file):
                    file_size = os.path.getsize(expected_file)
                    if file_size > 0:
                        print(f"   ✅ Document created! Size: {file_size} bytes")
                        
                        # Show first few lines of the document
                        with open(expected_file, 'r') as f:
                            content = f.read(500)  # First 500 chars
                        print(f"   Content preview:")
                        print(f"   {content[:200]}...")
                        return True
                    else:
                        print(f"   ⚠️  Document file exists but is empty")
                
                # If queue is empty and no active threads, check for completion
                if stats['queue_size'] == 0 and stats['active_threads'] == 0 and i > 5:
                    if stats['error_count'] > 0:
                        print(f"   ❌ Processing completed with {stats['error_count']} errors")
                        break
                    elif stats['processed_count'] > 0:
                        print(f"   ⚠️  Processing completed but no document file found")
                        break
            
            print("   ❌ Processing did not complete successfully")
            return False
        else:
            print("   ❌ Failed to queue task")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_document_generation()
    
    if success:
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Document generation is working properly")
        print(f"✅ The issue was likely missing Ollama client in previous tests")
    else:
        print(f"\n❌ FAILED!")
        print(f"❌ Document generation is not working")
        print(f"💡 Check Ollama connectivity and processor configuration")
    
    exit(0 if success else 1)
