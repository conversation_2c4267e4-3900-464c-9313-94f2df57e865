#!/usr/bin/env python3
"""
Comprehensive test for the enhanced metadata extraction keywords and voting system.
Tests all categories: protocol communications, multi-threading, memory management, and existing keywords.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from reposense_ai.metadata_extractor import MetadataExtractor
import logging

# Set up logging to see detailed analysis
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def test_scenario(name, documentation, expected_risk=None, expected_doc_impact=None):
    """Test a specific scenario and print detailed results"""
    print(f"\n{'='*80}")
    print(f"TEST: {name}")
    print(f"{'='*80}")
    print(f"Documentation: {documentation[:200]}...")

    # Create extractor (without Ollama for pure heuristic testing)
    extractor = MetadataExtractor(ollama_client=None)

    # Test the heuristic context gathering directly
    heuristic_context = extractor._gather_heuristic_context(documentation)
    indicators = heuristic_context.get('indicators', {})

    print(f"\n🔍 ENHANCED HEURISTIC ANALYSIS:")
    print(f"  Complexity: {indicators.get('complexity', 'Unknown')}")
    print(f"  Risk Assessment: {indicators.get('risk_assessment', 'Unknown')}")
    print(f"  Doc Assessment: {indicators.get('doc_assessment', 'Unknown')}")

    # Show detected keywords with confidence scores
    if 'risk_keywords' in indicators:
        print(f"  Risk Keywords: {indicators['risk_keywords']}")
        if 'risk_confidence' in indicators:
            print(f"  Risk Confidence: {indicators['risk_confidence']}")
    if 'low_risk_keywords' in indicators:
        print(f"  Low Risk Keywords: {indicators['low_risk_keywords']}")
    if 'doc_keywords' in indicators:
        print(f"  Doc Keywords: {indicators['doc_keywords']}")
        if 'doc_confidence' in indicators:
            print(f"  Doc Confidence: {indicators['doc_confidence']}")
    if 'no_doc_keywords' in indicators:
        print(f"  No Doc Keywords: {indicators['no_doc_keywords']}")
    if 'protocol_keywords' in indicators:
        print(f"  Protocol Keywords: {indicators['protocol_keywords']}")
    if 'threading_keywords' in indicators:
        print(f"  Threading Keywords: {indicators['threading_keywords']}")
    if 'memory_keywords' in indicators:
        print(f"  Memory Keywords: {indicators['memory_keywords']}")

    # Show specialized assessments
    if 'protocol_assessment' in indicators:
        print(f"  Protocol Assessment: {indicators['protocol_assessment']}")
    if 'threading_assessment' in indicators:
        print(f"  Threading Assessment: {indicators['threading_assessment']}")
    if 'memory_assessment' in indicators:
        print(f"  Memory Assessment: {indicators['memory_assessment']}")

    # Show reasoning
    reasoning = heuristic_context.get('reasoning', [])
    if reasoning:
        print(f"\n💭 REASONING:")
        for reason in reasoning:
            print(f"  - {reason}")

    # Also test full metadata extraction for comparison
    metadata = extractor.extract_all_metadata(documentation)
    print(f"\n📊 FULL METADATA RESULTS:")
    print(f"  Code Review Recommended: {metadata.get('code_review_recommended', 'Not determined')}")
    print(f"  Code Review Priority: {metadata.get('code_review_priority', 'Not determined')}")
    print(f"  Risk Level: {metadata.get('risk_level', 'Not determined')}")
    print(f"  Documentation Impact: {metadata.get('documentation_impact', 'Not determined')}")

    # Validate expectations against heuristic analysis
    heuristic_risk = indicators.get('risk_assessment', '')
    if expected_risk:
        if expected_risk == "CRITICAL" and "CRITICAL" not in heuristic_risk:
            print(f"⚠️  HEURISTIC EXPECTATION MISMATCH: Expected CRITICAL risk, got {heuristic_risk}")
        elif expected_risk == "HIGH" and "HIGH" not in heuristic_risk and "CRITICAL" not in heuristic_risk:
            print(f"⚠️  HEURISTIC EXPECTATION MISMATCH: Expected HIGH risk, got {heuristic_risk}")
        elif expected_risk == "LOW" and "LOW" not in heuristic_risk:
            print(f"⚠️  HEURISTIC EXPECTATION MISMATCH: Expected LOW risk, got {heuristic_risk}")

    heuristic_doc = indicators.get('doc_assessment', '')
    if expected_doc_impact is not None:
        if expected_doc_impact and "LIKELY" not in heuristic_doc and "POSSIBLE" not in heuristic_doc:
            print(f"⚠️  HEURISTIC EXPECTATION MISMATCH: Expected doc impact, got {heuristic_doc}")
        elif not expected_doc_impact and "UNLIKELY" not in heuristic_doc:
            print(f"⚠️  HEURISTIC EXPECTATION MISMATCH: Expected no doc impact, got {heuristic_doc}")

    return metadata

def run_comprehensive_tests():
    """Run comprehensive tests covering all keyword categories"""
    
    print("🚀 COMPREHENSIVE METADATA EXTRACTION KEYWORD TESTING")
    print("Testing enhanced heuristic analysis with probabilistic confidence system")

    # Test 1: CRITICAL Risk - Multiple High-Confidence Critical Keywords
    test_scenario(
        "CRITICAL Risk - Security + Memory + Threading",
        """
        ## Summary
        Emergency hotfix for critical security vulnerability in authentication system with memory corruption and race conditions.

        ## Changes
        - Fixed buffer overflow vulnerability in password validation
        - Resolved race condition in authentication token generation
        - Patched memory leak in crypto library usage
        - Added critical section protection for database migration
        - Fixed segfault in production SSL/TLS handshake
        - Urgent deadlock prevention in concurrent user sessions

        ## Security Impact
        CRITICAL - Multiple security vulnerabilities affecting production systems.
        Memory corruption and race conditions pose immediate risk.
        """,
        expected_risk="CRITICAL"
    )

    # Test 2: Protocol Communications
    test_scenario(
        "Protocol Communications - High Risk",
        """
        ## Summary
        Implemented new TCP protocol handler with custom packet parsing and state machine management.
        
        ## Changes
        - Added protocol specification parser for custom message format
        - Implemented handshake sequence with acknowledgment handling
        - Created frame header validation with checksum verification
        - Added connection timeout management and socket error handling
        - Implemented serialization/deserialization for payload data
        
        ## Technical Details
        The protocol stack now supports full duplex communication with sequence number tracking.
        Message encoding follows RFC-style specification with backward compatibility.
        """,
        expected_risk="HIGH"
    )
    
    # Test 2: Multi-Threading - Critical
    test_scenario(
        "Multi-Threading - Critical Risk",
        """
        ## Summary
        Refactored connection manager to use thread pool for concurrent request handling.
        
        ## Changes
        - Implemented producer-consumer pattern with thread-safe queue
        - Added mutex locks for critical section protection
        - Created atomic counters for connection tracking
        - Fixed race condition in session management
        - Added deadlock detection and prevention mechanisms
        - Implemented barrier synchronization for startup sequence
        
        ## Risk Assessment
        Threading changes require careful review due to potential concurrency issues.
        """,
        expected_risk="HIGH"
    )
    
    # Test 3: Memory Management - Critical
    test_scenario(
        "Memory Management - Critical Risk",
        """
        ## Summary
        Optimized buffer management system to prevent memory leaks and improve performance.
        
        ## Changes
        - Replaced malloc/free with custom memory pool allocator
        - Implemented RAII pattern with smart pointers (shared_ptr, unique_ptr)
        - Fixed buffer overflow vulnerability in packet processing
        - Added garbage collection for unused connection objects
        - Optimized heap allocation patterns to reduce fragmentation
        - Implemented reference counting for shared resources
        
        ## Security Impact
        Memory management changes address potential segfault issues.
        """,
        expected_risk="HIGH"
    )
    
    # Test 4: Combined High-Risk Areas
    test_scenario(
        "Combined Protocol + Threading + Memory",
        """
        ## Summary
        Major refactoring of protocol engine with concurrent processing and optimized memory usage.
        
        ## Changes
        - Implemented multi-threaded protocol parser with lock-free queues
        - Added atomic operations for packet sequence tracking
        - Created thread-safe memory pool for buffer management
        - Implemented producer-consumer pattern for message processing
        - Added mutex protection for shared protocol state machine
        - Optimized memory allocation with smart pointer usage
        - Fixed race conditions in connection handshake logic
        
        ## API Changes
        Public interface now supports concurrent connections with thread safety guarantees.
        Breaking changes to message format require documentation updates.
        """,
        expected_risk="HIGH",
        expected_doc_impact=True
    )
    
    # Test 5: Security + Database - Traditional High Risk
    test_scenario(
        "Security + Database - Traditional High Risk",
        """
        ## Summary
        Updated authentication system with database migration for enhanced security.
        
        ## Changes
        - Implemented new password hashing with crypto library
        - Added database schema migration for user tokens
        - Updated SSL/TLS configuration for production deployment
        - Enhanced login security with authentication tokens
        - Modified database connection settings for environment config
        
        ## Production Impact
        Requires careful deployment due to security and database changes.
        """,
        expected_risk="HIGH"
    )
    
    # Test 6: Low Risk - Documentation Only
    test_scenario(
        "Low Risk - Documentation Only",
        """
        ## Summary
        Updated README and code comments for better clarity.
        
        ## Changes
        - Fixed typos in documentation
        - Added code comments for better readability
        - Updated example usage in README
        - Formatted code style consistently
        - Added unit tests for existing functionality
        
        ## Impact
        Simple documentation and style improvements only.
        """,
        expected_risk="LOW"
    )
    
    # Test 7: Mixed Indicators - Should be Medium
    test_scenario(
        "Mixed Risk Indicators",
        """
        ## Summary
        Added new feature with some protocol elements but mostly internal changes.
        
        ## Changes
        - Added new internal function for message validation
        - Updated configuration parsing logic
        - Implemented simple protocol constant definitions
        - Added test cases for new functionality
        - Refactored internal helper methods
        
        ## Notes
        Mostly internal changes with some protocol-related constants.
        """,
        expected_risk="MEDIUM"
    )
    
    # Test 8: API + Breaking Changes - Documentation Impact
    test_scenario(
        "API Breaking Changes - Documentation Impact",
        """
        ## Summary
        Major API refactoring with breaking changes to public interface.
        
        ## Changes
        - Removed deprecated public methods from client interface
        - Added new endpoint for user configuration management
        - Breaking changes to message format specification
        - Updated public API with new parameter requirements
        - Modified installation and setup procedures
        
        ## Compatibility
        Breaking changes require version bump and user migration guide.
        """,
        expected_doc_impact=True
    )
    
    # Test 9: Internal Refactoring - No Documentation Impact
    test_scenario(
        "Internal Refactoring - No Documentation Impact",
        """
        ## Summary
        Internal code cleanup and performance improvements.
        
        ## Changes
        - Refactored private helper methods for better maintainability
        - Optimized internal data structures for performance
        - Added debug logging for troubleshooting
        - Cleaned up unused internal variables
        - Improved error handling in internal functions
        
        ## Impact
        Internal changes only, no user-facing modifications.
        """,
        expected_doc_impact=False
    )
    
    # Test 10: Edge Case - No Clear Indicators
    test_scenario(
        "Edge Case - Minimal Information",
        """
        ## Summary
        Minor update to improve system reliability.
        
        ## Changes
        - Updated version number
        - Minor bug fix
        - Code cleanup
        
        ## Notes
        Standard maintenance update.
        """,
        expected_risk="MEDIUM"  # Should default to medium with low confidence
    )

if __name__ == "__main__":
    run_comprehensive_tests()
    print(f"\n{'='*80}")
    print("🎯 COMPREHENSIVE TESTING COMPLETED")
    print("Review the results above to validate keyword detection and risk assessment logic.")
    print(f"{'='*80}")
