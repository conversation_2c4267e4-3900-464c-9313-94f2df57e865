#!/usr/bin/env python3
"""
Debug the unified processor to see why it's not processing tasks
"""

import sys
import os
import time
sys.path.append('/app')

from config_manager import ConfigManager
from unified_document_processor import UnifiedDocumentProcessor
from repository_backends.svn_backend import SVNBackend
from models import CommitInfo
from datetime import datetime

def debug_unified_processor():
    """Debug the unified processor to see why tasks aren't being processed"""
    print("🔍 Debugging Unified Processor")
    print("=" * 60)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Create unified processor (same as web interface)
        print("1. Creating UnifiedDocumentProcessor (same as web interface)...")
        processor = UnifiedDocumentProcessor(
            output_dir=config.output_dir,
            db_path="/app/data/documents.db",
            ollama_client=None,  # Will be created internally
            config_manager=config_manager,
            max_concurrent_tasks=3
        )
        
        print("2. Checking processor initial state...")
        stats = processor.get_stats()
        print(f"   Running: {stats['running']}")
        print(f"   Queue Size: {stats['queue_size']}")
        print(f"   Active Threads: {stats['active_threads']}")
        print(f"   Processed Count: {stats['processed_count']}")
        print(f"   Error Count: {stats['error_count']}")
        
        if not stats['running']:
            print("3. Starting processor...")
            processor.start()
            time.sleep(1)  # Give it time to start
            
            stats = processor.get_stats()
            print(f"   After start - Running: {stats['running']}")
            print(f"   After start - Active Threads: {stats['active_threads']}")
        
        # Check if Ollama client is working
        print("4. Checking Ollama client...")
        if hasattr(processor, 'ollama_client') and processor.ollama_client:
            try:
                models = processor.ollama_client.get_available_models()
                print(f"   ✅ Ollama client working, {len(models)} models available")
                print(f"   Models: {models[:3]}...")  # Show first 3 models
            except Exception as e:
                print(f"   ❌ Ollama client error: {e}")
        else:
            print("   ❌ No Ollama client found")
        
        # Test queuing a simple task
        print("5. Testing task queuing...")
        
        # Get repository config
        repo_config = config.get_repository_by_id('c42f3018-3ffc-4434-91df-e1d1d892bb9e')
        if not repo_config:
            print("   ❌ Repository not found")
            return False
        
        # Get commit info from repository
        backend = SVNBackend(repo_config)
        commit_info = backend.get_commit_info(repo_config, '17')
        if not commit_info:
            print("   ❌ Commit info not found")
            return False
        
        print(f"   ✅ Repository: {repo_config.name}")
        print(f"   ✅ Commit info: {commit_info.author} - {commit_info.message}")
        
        # Queue the task
        print("6. Queuing processing task...")
        success = processor.process_commit(
            commit_info=commit_info,
            repository_config=repo_config,
            documentation="",  # Will be regenerated
            priority=10  # High priority
        )
        
        print(f"   Queue result: {success}")
        
        if success:
            # Monitor the queue for a short time
            print("7. Monitoring queue processing...")
            
            for i in range(10):  # Monitor for 20 seconds
                time.sleep(2)
                stats = processor.get_stats()
                print(f"   [{i*2:2d}s] Queue: {stats['queue_size']}, Active: {stats['active_threads']}, Processed: {stats['processed_count']}, Errors: {stats['error_count']}")
                
                # Check if document was created
                expected_file = f"/app/data/output/repositories/{repo_config.name}/docs/revision_17.md"
                if os.path.exists(expected_file) and os.path.getsize(expected_file) > 0:
                    file_size = os.path.getsize(expected_file)
                    print(f"   ✅ Document created! Size: {file_size} bytes")
                    return True
                
                # If queue is empty and no active threads, something went wrong
                if stats['queue_size'] == 0 and stats['active_threads'] == 0 and i > 2:
                    print(f"   ❌ Queue empty but no document created")
                    break
            
            print("   ❌ Processing did not complete in monitoring period")
            return False
        else:
            print("   ❌ Failed to queue task")
            return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_existing_processor():
    """Check if there's already a processor running in the web interface"""
    print("\n" + "=" * 60)
    print("🔍 CHECKING EXISTING PROCESSOR")
    print("=" * 60)
    
    try:
        # Try to connect to the web interface's processor via API
        import requests
        
        print("1. Checking web interface processor status...")
        try:
            response = requests.get('http://localhost:5001/api/processing-status', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Web interface processor found:")
                print(f"      Running: {data.get('running', 'unknown')}")
                print(f"      Queue Size: {data.get('queue_size', 'unknown')}")
                print(f"      Active Threads: {data.get('active_threads', 'unknown')}")
                print(f"      Processed: {data.get('processed_count', 'unknown')}")
                print(f"      Errors: {data.get('error_count', 'unknown')}")
                return data
            else:
                print(f"   ❌ Web interface not responding: {response.status_code}")
                return None
        except Exception as e:
            print(f"   ❌ Cannot connect to web interface: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Error checking existing processor: {e}")
        return None

if __name__ == "__main__":
    print("🔍 UNIFIED PROCESSOR DEBUG")
    print("=" * 60)
    
    # First check if web interface processor is running
    web_stats = check_existing_processor()
    
    if web_stats and web_stats.get('running'):
        print(f"\n💡 DIAGNOSIS:")
        print(f"✅ Web interface processor is running")
        if web_stats.get('queue_size', 0) > 0:
            print(f"⚠️  Queue has {web_stats['queue_size']} items but they're not being processed")
            print(f"🔧 This suggests worker threads are not functioning")
        else:
            print(f"ℹ️  Queue is empty - tasks may be processed immediately or failing")
        
        if web_stats.get('active_threads', 0) == 0:
            print(f"❌ No active worker threads - this is the problem!")
            print(f"🔧 Worker threads may have crashed or never started")
        else:
            print(f"✅ {web_stats['active_threads']} active worker threads")
    else:
        print(f"\n💡 DIAGNOSIS:")
        print(f"❌ Web interface processor is not running")
        print(f"🔧 This explains why rescan tasks are not being processed")
    
    print(f"\n🧪 TESTING STANDALONE PROCESSOR:")
    print(f"=" * 60)
    
    # Test with standalone processor
    success = debug_unified_processor()
    
    if success:
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Standalone processor can create documents")
        print(f"💡 Issue is with web interface processor initialization")
    else:
        print(f"\n❌ FAILED!")
        print(f"❌ Even standalone processor cannot create documents")
        print(f"💡 Issue is deeper - possibly Ollama or database problems")
    
    exit(0 if success else 1)
