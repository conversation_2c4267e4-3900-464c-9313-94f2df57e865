# MailHog Email Testing Setup

MailHog is included in the RepoSense AI Docker Compose setup for easy email testing during development and production use.

## What is MailHog?

MailHog is an email testing tool that:
- **Captures emails** instead of sending them to real recipients
- **Provides a web interface** to view captured emails
- **Perfect for development** and testing email functionality
- **Safe for production** testing without sending real emails

## Quick Start

### 1. Start the Services
```bash
docker-compose up -d
```

This starts both RepoSense AI and MailHog automatically.

### 2. Access MailHog Web Interface
Open your browser and go to:
```
http://localhost:8025
```

### 3. Configure RepoSense AI
The system is pre-configured to use MailHog, but you can verify/update settings:

1. Go to **Configuration** page in RepoSense AI
2. In **Email Settings**, click **"MailHog (Development)"** button
3. Settings will be automatically configured:
   - **SMTP Host**: `mailhog`
   - **SMTP Port**: `1025`
   - **Username/Password**: (empty - not needed)
   - **From Address**: `reposense-ai@localhost`

### 4. Test Email Functionality
1. Enable **"Send Email Notifications"** in Configuration
2. Add some email addresses to **"Global Recipients"**
3. Save configuration
4. Trigger an email (e.g., by processing a repository)
5. Check MailHog web interface at `http://localhost:8025`

## MailHog Web Interface

### Features:
- **Email List**: See all captured emails
- **Email Preview**: View email content (HTML and text)
- **Search**: Find specific emails
- **Delete**: Remove emails from the interface
- **API Access**: Programmatic access to emails

### Screenshots:
- Main interface shows list of captured emails
- Click any email to view full content
- Emails show sender, recipient, subject, and timestamp

## Docker Compose Configuration

MailHog is configured in `docker-compose.yml`:

```yaml
mailhog:
  image: mailhog/mailhog:latest
  container_name: reposense-ai-mailhog
  restart: unless-stopped
  ports:
    - "1025:1025"  # SMTP port
    - "8025:8025"  # Web UI port
  networks:
    - reposense-ai-network
```

## Port Configuration

- **Port 1025**: SMTP server (for RepoSense AI to send emails)
- **Port 8025**: Web interface (for you to view emails)

## Production Use

### For Testing in Production:
MailHog is safe to use in production for testing purposes:
- Emails are captured, not sent to real recipients
- Perfect for testing email templates and functionality
- No risk of sending test emails to real users

### For Real Email Sending:
When ready to send real emails, switch to a real email provider:
1. Use the **"Gmail"** or **"Outlook"** preset buttons
2. Or configure custom SMTP settings
3. Disable MailHog by commenting it out in `docker-compose.yml`

## Troubleshooting

### MailHog Web Interface Not Accessible
```bash
# Check if MailHog container is running
docker ps | grep mailhog

# Check MailHog logs
docker logs reposense-ai-mailhog

# Restart MailHog
docker-compose restart mailhog
```

### Emails Not Appearing in MailHog
1. **Check RepoSense AI configuration**:
   - SMTP Host should be `mailhog`
   - SMTP Port should be `1025`
   - Email notifications should be enabled

2. **Check RepoSense AI logs**:
   ```bash
   docker logs reposense-ai
   ```

3. **Test email configuration** in RepoSense AI Configuration page

### Port Conflicts
If ports 1025 or 8025 are already in use:

1. **Edit `docker-compose.yml`**:
   ```yaml
   ports:
     - "2025:1025"  # Use port 2025 instead
     - "9025:8025"  # Use port 9025 instead
   ```

2. **Update RepoSense AI configuration**:
   - Change SMTP Port to `2025`
   - Access web interface at `http://localhost:9025`

## Advanced Configuration

### Custom MailHog Settings
You can customize MailHog behavior by adding environment variables:

```yaml
mailhog:
  image: mailhog/mailhog:latest
  environment:
    - MH_STORAGE=maildir
    - MH_MAILDIR_PATH=/maildir
  volumes:
    - ./mailhog-data:/maildir
```

### API Access
MailHog provides a REST API for programmatic access:
- `GET http://localhost:8025/api/v1/messages` - List messages
- `GET http://localhost:8025/api/v1/messages/{id}` - Get specific message
- `DELETE http://localhost:8025/api/v1/messages/{id}` - Delete message

## Benefits of Using MailHog

### Development Benefits:
- **Safe Testing**: No risk of sending emails to real users
- **Easy Debugging**: View exact email content and formatting
- **No External Dependencies**: Works offline, no email provider needed
- **Fast Setup**: No email account configuration required

### Production Benefits:
- **Email Template Testing**: Verify email formatting before going live
- **Integration Testing**: Test email workflows without external services
- **Debugging**: Troubleshoot email issues without affecting users
- **Compliance**: Test email content for regulatory compliance

## Migration to Real Email

When ready to use real email:

### 1. Choose Email Provider
- **Gmail**: Easy setup with App Passwords
- **Outlook**: Business email integration
- **SendGrid**: Professional email service
- **Custom SMTP**: Your organization's email server

### 2. Update Configuration
Use the preset buttons in RepoSense AI Configuration:
- Click **"Gmail"** for Gmail setup
- Click **"Outlook"** for Outlook setup
- Click **"Custom"** for other providers

### 3. Test Gradually
- Start with test email addresses
- Verify email delivery and formatting
- Gradually add real recipients

MailHog provides a perfect bridge between development and production email functionality!
