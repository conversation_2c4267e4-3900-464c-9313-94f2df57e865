#!/usr/bin/env python3
"""
Test that DocumentProcessor now has access to Ollama client for specialized models
"""

import logging
from datetime import datetime
from pathlib import Path
from document_processor import DocumentProcessor
from monitor_service import MonitorService

def setup_logging():
    """Setup logging to see model usage"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_document_processor_ollama_access():
    """Test that DocumentProcessor now has Ollama client access"""
    print("🧪 Testing DocumentProcessor Ollama Client Access")
    print("=" * 55)
    
    setup_logging()
    
    # Initialize monitor service (this provides the Ollama client)
    monitor_service = MonitorService("data/config.json")
    
    print(f"📋 Monitor Service Configuration:")
    print(f"  Default model: {monitor_service.config.ollama_model}")
    print(f"  Documentation model: {monitor_service.config.ollama_model_documentation}")
    print(f"  Code review model: {monitor_service.config.ollama_model_code_review}")
    print(f"  Risk assessment model: {monitor_service.config.ollama_model_risk_assessment}")
    
    # Test old way (without Ollama client)
    print(f"\n🔍 Testing OLD DocumentProcessor (without Ollama client):")
    old_processor = DocumentProcessor(
        monitor_service.config.output_dir,
        "/app/data/documents.db",
        monitor_service.config_manager
        # No ollama_client parameter
    )
    
    print(f"  Processor has Ollama client: {old_processor.ollama_client is not None}")
    print(f"  MetadataExtractor has Ollama client: {old_processor.metadata_extractor.ollama_client is not None}")
    
    # Test new way (with Ollama client)
    print(f"\n✅ Testing NEW DocumentProcessor (with Ollama client):")
    new_processor = DocumentProcessor(
        monitor_service.config.output_dir,
        "/app/data/documents.db",
        monitor_service.config_manager,
        monitor_service.ollama_client  # Pass Ollama client
    )
    
    print(f"  Processor has Ollama client: {new_processor.ollama_client is not None}")
    print(f"  MetadataExtractor has Ollama client: {new_processor.metadata_extractor.ollama_client is not None}")
    
    if new_processor.metadata_extractor.ollama_client:
        print(f"  ✅ SUCCESS: MetadataExtractor can now use specialized models!")
        
        # Test that the Ollama client has the right config
        ollama_config = new_processor.metadata_extractor.ollama_client.config
        print(f"  Ollama client config models:")
        print(f"    Default: {ollama_config.ollama_model}")
        print(f"    Risk assessment: {ollama_config.ollama_model_risk_assessment}")
        print(f"    Code review: {ollama_config.ollama_model_code_review}")
        
        return True
    else:
        print(f"  ❌ FAILED: MetadataExtractor still doesn't have Ollama client")
        return False

if __name__ == "__main__":
    success = test_document_processor_ollama_access()
    
    print("\n" + "=" * 55)
    if success:
        print("🎉 DocumentProcessor fix is working!")
        print("📝 New documents should now use specialized models.")
        print("🌐 Restart the web application to apply the fix.")
    else:
        print("❌ DocumentProcessor fix needs more work.")
