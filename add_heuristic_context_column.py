#!/usr/bin/env python3
"""
Manually add heuristic_context column to existing database
"""

import sqlite3
import os

def add_heuristic_context_column():
    """Manually add heuristic_context column to documents table"""
    
    db_path = "/app/data/documents.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        print("🔄 Adding heuristic_context column to documents table...")
        
        with sqlite3.connect(db_path) as conn:
            # Check if column already exists
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(documents)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'heuristic_context' in columns:
                print("✅ heuristic_context column already exists!")
                return True
            
            # Add the column
            cursor.execute("ALTER TABLE documents ADD COLUMN heuristic_context TEXT")
            conn.commit()
            
            print("✅ Successfully added heuristic_context column!")
            
            # Verify the column was added
            cursor.execute("PRAGMA table_info(documents)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'heuristic_context' in columns:
                print("✅ Column verified in database schema")
                return True
            else:
                print("❌ Column not found after adding")
                return False
                
    except Exception as e:
        print(f"❌ Error adding column: {e}")
        return False

if __name__ == "__main__":
    success = add_heuristic_context_column()
    if success:
        print("\n🎉 Database updated! You can now create documents with heuristic context.")
    else:
        print("\n❌ Failed to update database.")
