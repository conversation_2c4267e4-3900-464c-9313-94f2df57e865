# RepoSense AI - Unified Dockerfile
# Supports both development and production with build arguments
# Usage:
#   Development: docker build --build-arg MODE=development -t reposense-ai:dev .
#   Production:  docker build --build-arg MODE=production -t reposense-ai:prod .
#   Default:     docker build -t reposense-ai .

FROM python:3.11-slim

# Build arguments
ARG MODE=development
ARG PYTHON_VERSION=3.11

# Environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV REPOSENSE_AI_MODE=${MODE}

# Set working directory
WORKDIR /app

# Install system dependencies
# Base dependencies needed for all modes
RUN apt-get update && apt-get install -y \
    # Core dependencies
    subversion \
    ca-certificates \
    curl \
    # Document processing dependencies
    antiword \
    poppler-utils \
    # Development tools (only installed in dev mode)
    $(if [ "$MODE" = "development" ]; then echo "git vim nano procps htop"; fi) \
    # Production build tools (only for production builds)
    $(if [ "$MODE" = "production" ]; then echo "gcc g++ make libffi-dev libssl-dev"; fi) \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN useradd -m -u 1000 appuser && \
    mkdir -p /app/data /app/logs && \
    chown -R appuser:appuser /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy and set up entrypoint script and database initialization
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
COPY scripts/init_database.py /usr/local/bin/init_database.py
RUN chmod +x /usr/local/bin/entrypoint.sh && \
    chmod +x /usr/local/bin/init_database.py

# Set proper permissions
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/health', timeout=5)" || exit 1

# Expose port
EXPOSE 5000

# Set entrypoint and default command
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["python", "start_reposense_ai.py"]
