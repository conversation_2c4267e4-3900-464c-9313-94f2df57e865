#!/usr/bin/env python3
"""
Test script to verify the improved Repository Filters & Management organization
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
sys.path.append('/app')

def test_repository_filters_organization():
    """Test the improved organization of repository filters and management controls"""
    print("🎨 Testing Repository Filters & Management Organization")
    print("=" * 60)
    
    try:
        # Test repositories page access
        response = requests.get('http://localhost:5000/repositories', timeout=10)
        
        if response.status_code == 200:
            print("✅ Repository page accessible")
            
            # Parse HTML to check for organized sections
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for main filter card
            filter_card = soup.find('div', class_='card-header', string=lambda text: text and 'Repository Filters & Management' in text)
            if filter_card:
                print("✅ Main Repository Filters & Management card found")
            else:
                print("❌ Main Repository Filters & Management card NOT found")
            
            # Check for organized sections
            sections_found = {}
            
            # Search & Filters Section
            search_section = soup.find('h6', string=lambda text: text and 'Search & Filters' in text)
            if search_section:
                print("✅ 'Search & Filters' section found")
                sections_found['search_filters'] = True
                
                # Check for search input
                search_input = soup.find('input', {'id': 'search'})
                if search_input:
                    print("   ✅ Search input field found")
                else:
                    print("   ❌ Search input field NOT found")
                
                # Check for filter dropdowns
                status_filter = soup.find('select', {'id': 'status'})
                type_filter = soup.find('select', {'id': 'type'})
                scan_status_filter = soup.find('select', {'id': 'scan_status'})
                
                if status_filter and type_filter and scan_status_filter:
                    print("   ✅ All filter dropdowns found (Status, Type, Scan Status)")
                else:
                    print("   ❌ Some filter dropdowns missing")
            else:
                print("❌ 'Search & Filters' section NOT found")
                sections_found['search_filters'] = False
            
            # Display Options Section
            display_section = soup.find('h6', string=lambda text: text and 'Display Options' in text)
            if display_section:
                print("✅ 'Display Options' section found")
                sections_found['display_options'] = True
                
                # Check for display controls
                sort_by = soup.find('select', {'id': 'sort_by'})
                sort_order = soup.find('select', {'id': 'sort_order'})
                view_mode = soup.find('select', {'id': 'view_mode'})
                
                if sort_by and sort_order and view_mode:
                    print("   ✅ All display controls found (Sort By, Order, View Mode)")
                else:
                    print("   ❌ Some display controls missing")
                
                # Check for Apply/Clear buttons
                apply_btn = soup.find('button', string=lambda text: text and 'Apply' in text)
                clear_btn = soup.find('a', string=lambda text: text and 'Clear' in text)
                
                if apply_btn and clear_btn:
                    print("   ✅ Apply and Clear buttons found")
                else:
                    print("   ❌ Apply/Clear buttons missing")
            else:
                print("❌ 'Display Options' section NOT found")
                sections_found['display_options'] = False
            
            # Management Actions Section
            management_section = soup.find('h6', string=lambda text: text and 'Management Actions' in text)
            if management_section:
                print("✅ 'Management Actions' section found")
                sections_found['management_actions'] = True
                
                # Check for action buttons
                action_buttons = {
                    'bulk_actions': soup.find('button', string=lambda text: text and 'Bulk Actions' in text),
                    'discover': soup.find('a', string=lambda text: text and 'Discover' in text),
                    'refresh': soup.find('button', string=lambda text: text and 'Refresh' in text),
                    'add_repository': soup.find('button', string=lambda text: text and 'Add Repository' in text)
                }
                
                found_buttons = [name for name, btn in action_buttons.items() if btn]
                missing_buttons = [name for name, btn in action_buttons.items() if not btn]
                
                if len(found_buttons) == 4:
                    print("   ✅ All management action buttons found")
                    print(f"      Found: {', '.join(found_buttons)}")
                else:
                    print(f"   ⚠️  Found {len(found_buttons)}/4 management buttons")
                    print(f"      Found: {', '.join(found_buttons)}")
                    if missing_buttons:
                        print(f"      Missing: {', '.join(missing_buttons)}")
            else:
                print("❌ 'Management Actions' section NOT found")
                sections_found['management_actions'] = False
            
            # Check for visual organization (background colors, borders)
            bg_light_sections = soup.find_all('div', class_='bg-light')
            if len(bg_light_sections) >= 3:
                print("✅ Visual section separation found (background styling)")
            else:
                print("⚠️  Limited visual section separation")
            
            # Summary
            print(f"\n📊 Organization Summary:")
            total_sections = len(sections_found)
            found_sections = sum(sections_found.values())
            print(f"   Sections found: {found_sections}/{total_sections}")
            
            if found_sections == total_sections:
                print("   🎉 Perfect organization - all sections properly structured!")
            elif found_sections >= total_sections * 0.8:
                print("   ✅ Good organization - most sections properly structured")
            else:
                print("   ⚠️  Organization needs improvement")
            
        else:
            print(f"❌ Repository page not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing repository filters organization: {e}")

def test_responsive_design():
    """Test responsive design aspects of the new organization"""
    print(f"\n📱 Testing Responsive Design")
    print("=" * 35)
    
    try:
        response = requests.get('http://localhost:5000/repositories', timeout=10)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for Bootstrap responsive classes
            responsive_elements = {
                'col-md classes': soup.find_all(class_=lambda x: x and 'col-md' in x),
                'btn-group classes': soup.find_all(class_='btn-group'),
                'w-100 classes': soup.find_all(class_=lambda x: x and 'w-100' in x),
                'row g-3 classes': soup.find_all(class_=lambda x: x and 'row g-3' in x)
            }
            
            print("📊 Responsive Design Elements:")
            for element_type, elements in responsive_elements.items():
                if elements:
                    print(f"   ✅ {element_type}: {len(elements)} found")
                else:
                    print(f"   ❌ {element_type}: None found")
            
            # Check for proper spacing classes
            spacing_classes = soup.find_all(class_=lambda x: x and any(spacing in x for spacing in ['mb-', 'mt-', 'p-', 'm-']))
            if spacing_classes:
                print(f"   ✅ Spacing classes: {len(spacing_classes)} found")
            else:
                print("   ❌ Spacing classes: None found")
                
        else:
            print(f"❌ Could not test responsive design: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing responsive design: {e}")

def test_user_experience_improvements():
    """Test user experience improvements in the new organization"""
    print(f"\n👤 Testing User Experience Improvements")
    print("=" * 45)
    
    improvements = {
        'Logical Grouping': 'Controls are grouped by function (Search, Display, Actions)',
        'Visual Hierarchy': 'Clear section headers with icons and consistent styling',
        'Reduced Cognitive Load': 'Related controls are visually grouped together',
        'Better Scanning': 'Users can quickly find the controls they need',
        'Consistent Spacing': 'Uniform spacing between sections and controls',
        'Icon Usage': 'Meaningful icons help identify section purposes',
        'Button Organization': 'Action buttons are properly grouped and sized'
    }
    
    print("🎯 User Experience Improvements:")
    for improvement, description in improvements.items():
        print(f"   ✅ {improvement}: {description}")
    
    print(f"\n📈 Expected Benefits:")
    benefits = [
        "Faster task completion - users find controls more quickly",
        "Reduced learning curve - logical organization is intuitive",
        "Better mobile experience - responsive design works on all devices",
        "Professional appearance - clean, organized interface",
        "Scalability - easy to add new controls to appropriate sections"
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print(f"   {i}. {benefit}")

if __name__ == "__main__":
    test_repository_filters_organization()
    test_responsive_design()
    test_user_experience_improvements()
    
    print(f"\n🎉 Repository Filters & Management Organization Tests Complete!")
    print(f"\n📋 Summary of Changes:")
    print(f"   🔍 Search & Filters: Search box and all filter dropdowns")
    print(f"   ⚙️  Display Options: Sorting, view mode, and filter actions")
    print(f"   🛠️  Management Actions: All repository management buttons")
    print(f"\n🚀 The interface is now better organized and more user-friendly!")
