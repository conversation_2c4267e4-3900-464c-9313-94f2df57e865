#!/usr/bin/env python3
"""
Configure RepoSense AI for email testing with users
"""

import sys
import os
sys.path.append('/app')

from config_manager import Config<PERSON><PERSON>ger

def configure_email_for_testing():
    """Configure email settings for testing with users"""
    print("⚙️ Configuring Email Settings for User Testing")
    print("=" * 50)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print("1. Current email configuration:")
        print(f"   📧 SMTP Host: {config.smtp_host}")
        print(f"   🔌 SMTP Port: {config.smtp_port}")
        print(f"   📤 From Email: {config.email_from}")
        print(f"   🔔 Send Emails: {config.send_emails}")
        print(f"   📮 Email Recipients: {config.email_recipients}")

        print(f"\n2. Current users:")
        for user in config.users:
            notifications = "🔔 All notifications" if user.receive_all_notifications else "🔕 No notifications"
            print(f"   - {user.username} ({user.email}) - {user.role.value} - {notifications}")

        # Configure email settings for testing
        print(f"\n3. Configuring email for testing...")

        # Enable email notifications
        config.send_emails = True

        # Add user emails to global recipients (in addition to user notification settings)
        user_emails = [user.email for user in config.users if user.enabled]
        config.email_recipients = user_emails
        
        # Ensure MailHog settings are correct
        config.smtp_host = "mailhog"
        config.smtp_port = 1025
        config.email_from = "reposense-ai@localhost"
        
        print(f"   ✅ Email notifications enabled")
        print(f"   ✅ Email recipients set to: {', '.join(user_emails)}")
        print(f"   ✅ MailHog SMTP configured")
        
        # Save configuration
        config_manager.save_config(config)
        print(f"   ✅ Configuration saved")
        
        print(f"\n4. Email configuration complete!")
        print(f"   📧 Recipients: {len(user_emails)} users")
        print(f"   🎯 Ready for testing: Process a repository to trigger email notifications")
        print(f"   🌐 View emails at: http://localhost:8025")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = configure_email_for_testing()
    exit(0 if success else 1)
