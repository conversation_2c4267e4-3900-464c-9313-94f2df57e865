#!/usr/bin/env python3
"""
Test the current API to see if the fix is applied
"""

import requests
import json

def test_api_response_format():
    """Test if the API returns the correct format"""
    print("🧪 Testing /api/check response format...")
    
    try:
        response = requests.post('http://localhost:5000/api/check', timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response JSON: {json.dumps(data, indent=2)}")
            
            # Check if the response has the required fields
            has_success = 'success' in data
            has_status = 'status' in data
            has_repo_count = 'repositories_checked' in data
            
            print(f"\n📊 Response Analysis:")
            print(f"  Has 'success' field: {'✅' if has_success else '❌'}")
            print(f"  Has 'status' field: {'✅' if has_status else '❌'}")
            print(f"  Has 'repositories_checked' field: {'✅' if has_repo_count else '❌'}")
            
            if has_success:
                print(f"  Success value: {data['success']}")
                print(f"  ✅ API fix is applied!")
            else:
                print(f"  ❌ API fix is NOT applied - web application needs restart")
                
            if has_repo_count:
                repo_count = data['repositories_checked']
                print(f"  Repositories checked: {repo_count}")
                if repo_count > 0:
                    print(f"  ✅ Repositories are configured and being checked")
                else:
                    print(f"  ❌ No repositories configured")
            
        else:
            print(f"❌ API call failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error response (text): {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to http://localhost:5000")
        print("   The web application might not be running")
    except Exception as e:
        print(f"❌ Error testing API: {e}")

def test_javascript_compatibility():
    """Test what the JavaScript code expects"""
    print(f"\n🔧 JavaScript Compatibility Test...")
    
    try:
        response = requests.post('http://localhost:5000/api/check', timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Simulate what the JavaScript code does
            print(f"JavaScript code expects:")
            print(f"  data.success: {data.get('success', 'MISSING')}")
            
            if 'success' in data:
                if data['success']:
                    print(f"  ✅ Would show: 'Check completed successfully'")
                else:
                    print(f"  ❌ Would show error: {data.get('error', 'Unknown error')}")
            else:
                print(f"  ❌ Would show: 'Unknown error' (because success field is missing)")
                print(f"  This is why the web interface shows 'Check failed: Unknown error'")
                
    except Exception as e:
        print(f"❌ Error in compatibility test: {e}")

if __name__ == "__main__":
    test_api_response_format()
    test_javascript_compatibility()
    
    print(f"\n💡 Summary:")
    print(f"  1. The monitoring system IS working (2 repositories being checked)")
    print(f"  2. Periodic scanning IS active (every 5 minutes)")
    print(f"  3. The API fix needs the web application to be restarted")
    print(f"  4. After restart, the 'Check Now' button should work properly")
