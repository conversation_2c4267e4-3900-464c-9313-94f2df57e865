#!/usr/bin/env python3
"""
Test the web interface's unified processor
"""

import sys
import requests
import json
sys.path.append('/app')

def test_web_processor():
    """Test the web interface's unified processor via API"""
    print("🔍 Testing Web Interface Unified Processor")
    print("=" * 60)
    
    try:
        # Check processing status
        print("1. Checking processing status...")
        response = requests.get('http://localhost:5000/api/processing-status', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            processing = data.get('processing', {})
            print(f"   ✅ Web interface responding")
            print(f"   Running: {processing.get('running', 'unknown')}")
            print(f"   Queue Size: {processing.get('queue_size', 'unknown')}")
            print(f"   Active Threads: {processing.get('active_threads', 'unknown')}")
            print(f"   Processed: {processing.get('processed_count', 'unknown')}")
            print(f"   Errors: {processing.get('error_count', 'unknown')}")

            if not processing.get('running', False):
                print("   ❌ Processor not running!")
                return False

            # Note: active_threads can be 0 when no tasks are being processed
            print("   ✅ Processor is running and ready")
                
        else:
            print(f"   ❌ Web interface not responding: {response.status_code}")
            return False
        
        # Test document recovery (which uses the unified processor)
        print("\n2. Testing document recovery...")
        doc_id = "c42f3018-3ffc-4434-91df-e1d1d892bb9e_17"
        
        recovery_data = {
            "aggressiveness": "BALANCED"
        }
        
        response = requests.post(
            f'http://localhost:5000/api/documents/{doc_id}/recover',
            json=recovery_data,
            timeout=120  # 2 minute timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ Recovery successful: {result.get('message')}")
                return True
            else:
                print(f"   ❌ Recovery failed: {result.get('message')}")
                return False
        else:
            try:
                error_data = response.json()
                print(f"   ❌ Recovery request failed ({response.status_code}): {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   ❌ Recovery request failed ({response.status_code}): {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_web_processor()
    
    if success:
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Web interface unified processor is working")
        print(f"✅ Document recovery completed successfully")
    else:
        print(f"\n❌ FAILED!")
        print(f"❌ Web interface unified processor has issues")
        print(f"🔧 Check logs for more details")
    
    exit(0 if success else 1)
