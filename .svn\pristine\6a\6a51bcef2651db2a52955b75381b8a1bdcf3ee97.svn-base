"""
Document Database Service for RepoSense AI

High-performance document metadata storage and retrieval using SQLite.
Replaces file-based scanning with indexed database queries.
"""

import sqlite3
import json
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass, asdict
from contextlib import contextmanager

from database_migration import DatabaseMigration

@dataclass
class DocumentRecord:
    """Database record for document metadata"""
    id: str
    repository_id: str
    repository_name: str
    revision: int
    date: datetime
    filename: str
    filepath: str
    size: int
    author: str
    commit_message: str
    changed_paths: Optional[List[str]] = None  # List of files changed in this commit
    code_review_recommended: Optional[bool] = None
    code_review_priority: Optional[str] = None
    documentation_impact: Optional[bool] = None
    risk_level: Optional[str] = None
    file_modified_time: Optional[float] = None
    processed_time: Optional[datetime] = None
    ai_model_used: Optional[str] = None  # Track which AI model was actually used for analysis
    risk_aggressiveness_used: Optional[str] = None  # Track which aggressiveness level was used for risk assessment
    # Metadata for recreating diff on-demand
    repository_url: Optional[str] = None  # Repository URL (SVN/Git/etc)
    repository_type: Optional[str] = None  # 'svn' or 'git'
    # User feedback fields for code review process
    user_code_review_status: Optional[str] = None  # 'approved', 'rejected', 'needs_changes', 'in_progress'
    user_code_review_comments: Optional[str] = None
    user_code_review_reviewer: Optional[str] = None
    user_code_review_date: Optional[datetime] = None
    # User feedback fields for documentation quality
    user_documentation_rating: Optional[int] = None  # 1-5 rating
    user_documentation_comments: Optional[str] = None
    user_documentation_updated_by: Optional[str] = None
    user_documentation_updated_date: Optional[datetime] = None
    # User feedback fields for risk assessment override
    user_risk_assessment_override: Optional[str] = None  # User can override AI risk assessment
    user_risk_assessment_comments: Optional[str] = None
    user_risk_assessment_updated_by: Optional[str] = None
    user_risk_assessment_updated_date: Optional[datetime] = None

    # User feedback fields for documentation input/augmentation
    user_documentation_input: Optional[str] = None  # User's additional documentation content
    user_documentation_suggestions: Optional[str] = None  # User's suggestions for improving AI documentation
    user_documentation_input_by: Optional[str] = None  # Who provided the documentation input
    user_documentation_input_date: Optional[datetime] = None  # When documentation input was provided

    # AI-generated summary field
    ai_summary: Optional[str] = None  # AI-generated summary of the document changes

    # Heuristic analysis context (JSON stored as string)
    heuristic_context: Optional[Dict[str, Any]] = None  # Heuristic analysis indicators and confidence scores

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        if isinstance(data['date'], datetime):
            data['date'] = data['date'].isoformat()
        if isinstance(data['processed_time'], datetime):
            data['processed_time'] = data['processed_time'].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DocumentRecord':
        """Create from dictionary with proper type conversion"""
        # Convert ISO strings back to datetime objects
        if isinstance(data['date'], str):
            data['date'] = datetime.fromisoformat(data['date'])
        if data.get('processed_time') and isinstance(data['processed_time'], str):
            data['processed_time'] = datetime.fromisoformat(data['processed_time'])
        return cls(**data)


class DocumentDatabase:
    """High-performance document metadata database"""
    
    def __init__(self, db_path: str = "/app/data/documents.db", cleanup_orphaned: bool = True):
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()

        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize database with migrations
        self._initialize_database()

        # Ensure proper file permissions for the database file
        self._ensure_database_permissions()

        # Clean up orphaned documents on startup (configurable)
        if cleanup_orphaned:
            self._cleanup_orphaned_documents()
        else:
            self.logger.debug("Orphaned document cleanup skipped (disabled in configuration)")
    
    def _ensure_database_permissions(self):
        """Ensure database file has proper permissions for the application user"""
        try:
            import os
            import stat

            if self.db_path.exists():
                # Set read/write permissions for owner and group
                # This ensures the database is writable by the application user
                current_permissions = self.db_path.stat().st_mode
                new_permissions = current_permissions | stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IWGRP

                if current_permissions != new_permissions:
                    os.chmod(self.db_path, new_permissions)
                    self.logger.debug(f"Updated database file permissions: {oct(new_permissions)}")
                else:
                    self.logger.debug("Database file permissions are already correct")
            else:
                self.logger.warning("Database file does not exist, cannot set permissions")

        except Exception as e:
            # Don't fail initialization if permission setting fails
            self.logger.warning(f"Could not set database file permissions: {e}")

    def _cleanup_orphaned_documents(self):
        """Remove documents from database where the associated files no longer exist"""
        try:
            with self._get_connection() as conn:
                # Get all documents with their file paths
                cursor = conn.execute("SELECT id, filepath FROM documents")
                documents = cursor.fetchall()

                orphaned_count = 0
                checked_count = 0
                for doc_id, filepath in documents:
                    checked_count += 1
                    if filepath:
                        file_path = Path(filepath)
                        if not file_path.exists():
                            # File doesn't exist, remove from database
                            conn.execute("DELETE FROM documents WHERE id = ?", (doc_id,))
                            orphaned_count += 1
                            self.logger.debug(f"Removed orphaned document: {doc_id} (file: {filepath})")
                        else:
                            self.logger.debug(f"Document file exists: {doc_id} (file: {filepath})")
                    else:
                        # No filepath specified, this is likely an orphaned record
                        conn.execute("DELETE FROM documents WHERE id = ?", (doc_id,))
                        orphaned_count += 1
                        self.logger.debug(f"Removed document with no filepath: {doc_id}")

                if orphaned_count > 0:
                    conn.commit()
                    self.logger.info(f"🧹 Cleaned up {orphaned_count} orphaned documents on startup (checked {checked_count} total)")
                else:
                    self.logger.info(f"✅ No orphaned documents found during startup cleanup (checked {checked_count} documents)")

        except Exception as e:
            self.logger.error(f"Error during orphaned document cleanup: {e}")

    def _initialize_database(self):
        """Initialize database schema directly (no migrations needed for pre-production)"""
        try:
            self._create_basic_schema()
            self.logger.info("Database initialized successfully")
        except Exception as e:
            self.logger.error(f"Error during database initialization: {e}")
            raise

    def _create_basic_schema(self):
        """Fallback basic schema creation"""
        with self._get_connection() as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id TEXT PRIMARY KEY,
                    repository_id TEXT NOT NULL,
                    repository_name TEXT NOT NULL,
                    revision INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    filepath TEXT NOT NULL UNIQUE,
                    size INTEGER NOT NULL,
                    author TEXT NOT NULL,
                    commit_message TEXT NOT NULL,
                    changed_paths TEXT,  -- JSON array of changed file paths
                    code_review_recommended INTEGER,
                    code_review_priority TEXT,
                    documentation_impact INTEGER,
                    risk_level TEXT,
                    file_modified_time REAL,
                    processed_time TEXT,
                    ai_model_used TEXT,
                    risk_aggressiveness_used TEXT,
                    repository_url TEXT,
                    repository_type TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    -- User feedback fields for code review process
                    user_code_review_status TEXT,
                    user_code_review_comments TEXT,
                    user_code_review_reviewer TEXT,
                    user_code_review_date TEXT,
                    -- User feedback fields for documentation quality
                    user_documentation_rating INTEGER,
                    user_documentation_comments TEXT,
                    user_documentation_updated_by TEXT,
                    user_documentation_updated_date TEXT,
                    -- User feedback fields for risk assessment override
                    user_risk_assessment_override TEXT,
                    user_risk_assessment_comments TEXT,
                    user_risk_assessment_updated_by TEXT,
                    user_risk_assessment_updated_date TEXT,
                    -- User feedback fields for documentation input/augmentation
                    user_documentation_input TEXT,
                    user_documentation_suggestions TEXT,
                    user_documentation_input_by TEXT,
                    user_documentation_input_date TEXT,
                    -- AI-generated summary field
                    ai_summary TEXT,
                    -- Heuristic analysis context (confidence ratings, analysis indicators)
                    heuristic_context TEXT
                )
            """)

            # Add missing columns if they don't exist (for existing databases)
            self._add_missing_columns(conn)

            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_repository_id ON documents(repository_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_revision ON documents(revision)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_date ON documents(date)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_code_review ON documents(code_review_recommended)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_doc_impact ON documents(documentation_impact)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_filepath ON documents(filepath)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_file_modified ON documents(file_modified_time)")

            # Create indexes for user feedback fields only if columns exist
            self._create_user_feedback_indexes(conn)

            conn.commit()

    def _add_missing_columns(self, conn):
        """Add missing columns to existing databases"""
        # Get existing columns
        cursor = conn.execute("PRAGMA table_info(documents)")
        existing_columns = {row[1] for row in cursor.fetchall()}

        # Define all columns that should exist (including core and user feedback columns)
        required_columns = [
            # Core columns that might be missing in older databases
            ("changed_paths", "TEXT"),  # JSON array of changed file paths
            ("ai_summary", "TEXT"),  # AI-generated summary field
            ("ai_model_used", "TEXT"),  # AI model used for analysis
            ("risk_aggressiveness_used", "TEXT"),  # Risk aggressiveness level used for analysis
            ("heuristic_context", "TEXT"),  # Heuristic analysis context (confidence ratings, analysis indicators)
            # User feedback columns
        ]
        user_feedback_columns = [
            ("user_code_review_status", "TEXT"),
            ("user_code_review_comments", "TEXT"),
            ("user_code_review_reviewer", "TEXT"),
            ("user_code_review_date", "TEXT"),
            ("user_documentation_rating", "INTEGER"),
            ("user_documentation_comments", "TEXT"),
            ("user_documentation_updated_by", "TEXT"),
            ("user_documentation_updated_date", "TEXT"),
            ("user_risk_assessment_override", "TEXT"),
            ("user_risk_assessment_comments", "TEXT"),
            ("user_risk_assessment_updated_by", "TEXT"),
            ("user_risk_assessment_updated_date", "TEXT"),
            ("user_documentation_input", "TEXT"),
            ("user_documentation_suggestions", "TEXT"),
            ("user_documentation_input_by", "TEXT"),
            ("user_documentation_input_date", "TEXT")
        ]

        # Combine all columns that need to be checked
        all_columns = required_columns + user_feedback_columns

        # Add missing columns
        for column_name, column_type in all_columns:
            if column_name not in existing_columns:
                try:
                    conn.execute(f"ALTER TABLE documents ADD COLUMN {column_name} {column_type}")
                    self.logger.info(f"Added missing column: {column_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e).lower():
                        self.logger.warning(f"Could not add column {column_name}: {e}")

    def _create_user_feedback_indexes(self, conn):
        """Create indexes for user feedback fields only if columns exist"""
        # Get existing columns
        cursor = conn.execute("PRAGMA table_info(documents)")
        existing_columns = {row[1] for row in cursor.fetchall()}

        # Define indexes to create if columns exist
        user_feedback_indexes = [
            ("idx_user_code_review_status", "user_code_review_status"),
            ("idx_user_code_review_reviewer", "user_code_review_reviewer"),
            ("idx_user_documentation_rating", "user_documentation_rating"),
            ("idx_user_risk_override", "user_risk_assessment_override")
        ]

        # Create indexes only for existing columns
        for index_name, column_name in user_feedback_indexes:
            if column_name in existing_columns:
                try:
                    conn.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON documents({column_name})")
                except sqlite3.OperationalError as e:
                    self.logger.warning(f"Could not create index {index_name}: {e}")

    @contextmanager
    def _get_connection(self):
        """Get database connection with proper locking"""
        with self._lock:
            conn = sqlite3.connect(str(self.db_path), timeout=30.0)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            try:
                yield conn
            finally:
                conn.close()
    
    def upsert_document(self, doc: DocumentRecord) -> bool:
        """Insert or update document record"""
        try:
            with self._get_connection() as conn:
                # Convert boolean values to integers for SQLite
                code_review_rec = None if doc.code_review_recommended is None else int(doc.code_review_recommended)
                doc_impact = None if doc.documentation_impact is None else int(doc.documentation_impact)
                
                # Convert changed_paths list to JSON string for storage
                changed_paths_json = json.dumps(doc.changed_paths) if doc.changed_paths else None

                # Convert heuristic_context dict to JSON string for storage
                heuristic_context_json = json.dumps(doc.heuristic_context) if doc.heuristic_context else None

                conn.execute("""
                    INSERT OR REPLACE INTO documents (
                        id, repository_id, repository_name, revision, date, filename, filepath,
                        size, author, commit_message, changed_paths, code_review_recommended, code_review_priority,
                        documentation_impact, risk_level, file_modified_time, processed_time, ai_model_used,
                        risk_aggressiveness_used, repository_url, repository_type, updated_at,
                        user_code_review_status, user_code_review_comments, user_code_review_reviewer, user_code_review_date,
                        user_documentation_rating, user_documentation_comments, user_documentation_updated_by, user_documentation_updated_date,
                        user_risk_assessment_override, user_risk_assessment_comments, user_risk_assessment_updated_by, user_risk_assessment_updated_date,
                        user_documentation_input, user_documentation_suggestions, user_documentation_input_by, user_documentation_input_date,
                        ai_summary, heuristic_context
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    doc.id, doc.repository_id, doc.repository_name, doc.revision,
                    doc.date.isoformat(), doc.filename, doc.filepath, doc.size,
                    doc.author, doc.commit_message, changed_paths_json, code_review_rec, doc.code_review_priority,
                    doc_impact, doc.risk_level, doc.file_modified_time,
                    doc.processed_time.isoformat() if doc.processed_time else None,
                    doc.ai_model_used,
                    doc.risk_aggressiveness_used,
                    doc.repository_url, doc.repository_type,
                    # User feedback fields
                    doc.user_code_review_status, doc.user_code_review_comments, doc.user_code_review_reviewer,
                    doc.user_code_review_date.isoformat() if doc.user_code_review_date else None,
                    doc.user_documentation_rating, doc.user_documentation_comments, doc.user_documentation_updated_by,
                    doc.user_documentation_updated_date.isoformat() if doc.user_documentation_updated_date else None,
                    doc.user_risk_assessment_override, doc.user_risk_assessment_comments, doc.user_risk_assessment_updated_by,
                    doc.user_risk_assessment_updated_date.isoformat() if doc.user_risk_assessment_updated_date else None,
                    doc.user_documentation_input, doc.user_documentation_suggestions, doc.user_documentation_input_by,
                    doc.user_documentation_input_date.isoformat() if doc.user_documentation_input_date else None,
                    doc.ai_summary, heuristic_context_json
                ))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error upserting document {doc.id}: {e}")
            return False

    def update_user_feedback(self, doc_id: str, user_feedback_data: dict) -> bool:
        """Update user feedback fields for a document"""
        try:
            # Get the existing document
            document_record = self.get_document_by_id(doc_id)
            if not document_record:
                return False

            # Update user feedback fields from the provided data
            for field_name, field_value in user_feedback_data.items():
                if hasattr(document_record, field_name) and field_value is not None:
                    setattr(document_record, field_name, field_value)

            # Save the updated document
            return self.upsert_document(document_record)

        except Exception as e:
            self.logger.error(f"Error updating user feedback for document {doc_id}: {e}")
            return False
    
    def get_documents(self, limit: int = 50, offset: int = 0,
                     repository_id: Optional[str] = None,
                     code_review_filter: Optional[bool] = None,
                     doc_impact_filter: Optional[bool] = None,
                     risk_level_filter: Optional[str] = None,
                     author_filter: Optional[str] = None,
                     date_from: Optional[str] = None,
                     date_to: Optional[str] = None,
                     search_query: Optional[str] = None,
                     sort_by: str = 'date',
                     sort_order: str = 'desc') -> List[DocumentRecord]:
        """Get documents with enhanced filtering, sorting, and search capabilities"""
        try:
            with self._get_connection() as conn:
                query = "SELECT * FROM documents WHERE 1=1"
                params: List[Union[str, int]] = []

                # Repository filter - handle both repository_id and repository_name
                if repository_id:
                    # Check if it looks like a UUID (repository_id) or a name (repository_name)
                    if len(repository_id) == 36 and repository_id.count('-') == 4:
                        # Looks like a UUID, filter by repository_id
                        query += " AND repository_id = ?"
                        params.append(repository_id)
                    else:
                        # Treat as repository_name
                        query += " AND repository_name = ?"
                        params.append(repository_id)

                # Code review filter
                if code_review_filter is not None:
                    query += " AND code_review_recommended = ?"
                    params.append(int(code_review_filter))

                # Documentation impact filter
                if doc_impact_filter is not None:
                    query += " AND documentation_impact = ?"
                    params.append(int(doc_impact_filter))

                # Risk level filter
                if risk_level_filter:
                    query += " AND risk_level = ?"
                    params.append(risk_level_filter)

                # Author filter
                if author_filter:
                    query += " AND author LIKE ?"
                    params.append(f"%{author_filter}%")

                # Date range filters
                if date_from:
                    query += " AND date >= ?"
                    params.append(date_from)

                if date_to:
                    query += " AND date <= ?"
                    params.append(date_to)

                # Search query (searches in commit message, author, and repository name)
                if search_query:
                    query += " AND (commit_message LIKE ? OR author LIKE ? OR repository_name LIKE ?)"
                    search_param = f"%{search_query}%"
                    params.extend([search_param, search_param, search_param])

                # Sorting
                valid_sort_columns = {
                    'date': 'date',
                    'repository': 'repository_name',
                    'author': 'author',
                    'revision': 'revision',
                    'size': 'size'
                }

                sort_column = valid_sort_columns.get(sort_by, 'date')
                sort_direction = 'ASC' if sort_order.lower() == 'asc' else 'DESC'
                query += f" ORDER BY {sort_column} {sort_direction}"

                # Pagination
                query += " LIMIT ? OFFSET ?"
                params.extend([limit, offset])

                # Debug logging for repository filtering
                if repository_id:
                    self.logger.debug(f"Filtering documents by repository_id: {repository_id}")
                    self.logger.debug(f"Query: {query}")
                    self.logger.debug(f"Params: {params}")

                cursor = conn.execute(query, params)
                rows = cursor.fetchall()

                # Debug logging for results
                if repository_id:
                    self.logger.debug(f"Found {len(rows)} documents for repository_id: {repository_id}")

                documents = []
                for row in rows:
                    doc = self._row_to_document(row)
                    if doc:
                        documents.append(doc)

                return documents
        except Exception as e:
            self.logger.error(f"Error getting documents: {e}")
            return []



    def get_available_authors(self, repository_id: Optional[str] = None) -> List[str]:
        """Get list of available authors for filtering"""
        try:
            with self._get_connection() as conn:
                query = "SELECT DISTINCT author FROM documents WHERE author IS NOT NULL AND author != ''"
                params: List[str] = []

                if repository_id:
                    query += " AND repository_id = ?"
                    params.append(repository_id)

                query += " ORDER BY author"

                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                return [row[0] for row in rows]
        except Exception as e:
            self.logger.error(f"Error getting available authors: {e}")
            return []

    def get_available_repositories(self) -> List[dict]:
        """Get list of available repositories with document counts"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("""
                    SELECT repository_name, COUNT(*) as doc_count
                    FROM documents
                    GROUP BY repository_name
                    ORDER BY repository_name
                """)
                rows = cursor.fetchall()
                return [
                    {
                        'id': row[0],  # Use repository_name as ID for filtering
                        'name': row[0],
                        'doc_count': row[1]
                    }
                    for row in rows
                ]
        except Exception as e:
            self.logger.error(f"Error getting available repositories: {e}")
            return []

    def get_document_by_id(self, doc_id: str) -> Optional[DocumentRecord]:
        """Get single document by ID"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT * FROM documents WHERE id = ?", (doc_id,))
                row = cursor.fetchone()
                return self._row_to_document(row) if row else None
        except Exception as e:
            self.logger.error(f"Error getting document {doc_id}: {e}")
            return None

    def get_documents_by_filepath(self, filepath: str) -> List[DocumentRecord]:
        """Get all documents with the specified filepath"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT * FROM documents WHERE filepath = ?", (filepath,))
                rows = cursor.fetchall()

                documents = []
                for row in rows:
                    doc = self._row_to_document(row)
                    if doc:
                        documents.append(doc)
                return documents
        except Exception as e:
            self.logger.error(f"Error getting documents by filepath {filepath}: {e}")
            return []
    
    def get_documents_needing_processing(self) -> List[Tuple[str, float]]:
        """Get documents that need processing (filepath, file_modified_time)"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("""
                    SELECT filepath, file_modified_time FROM documents 
                    WHERE processed_time IS NULL OR file_modified_time > 
                        (SELECT strftime('%s', processed_time) FROM documents d2 WHERE d2.id = documents.id)
                """)
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Error getting documents needing processing: {e}")
            return []
    
    def get_document_count(self, repository_id: Optional[str] = None,
                          code_review_filter: Optional[bool] = None,
                          doc_impact_filter: Optional[bool] = None,
                          risk_level_filter: Optional[str] = None,
                          author_filter: Optional[str] = None,
                          date_from: Optional[str] = None,
                          date_to: Optional[str] = None,
                          search_query: Optional[str] = None) -> int:
        """Get total document count with enhanced filtering"""
        try:
            with self._get_connection() as conn:
                query = "SELECT COUNT(*) FROM documents WHERE 1=1"
                params: List[Union[str, int]] = []

                # Apply same filters as get_documents
                if repository_id:
                    # Check if it looks like a UUID (repository_id) or a name (repository_name)
                    if len(repository_id) == 36 and repository_id.count('-') == 4:
                        # Looks like a UUID, filter by repository_id
                        query += " AND repository_id = ?"
                        params.append(repository_id)
                    else:
                        # Treat as repository_name
                        query += " AND repository_name = ?"
                        params.append(repository_id)

                if code_review_filter is not None:
                    query += " AND code_review_recommended = ?"
                    params.append(int(code_review_filter))

                if doc_impact_filter is not None:
                    query += " AND documentation_impact = ?"
                    params.append(int(doc_impact_filter))

                if risk_level_filter:
                    query += " AND risk_level = ?"
                    params.append(risk_level_filter)

                if author_filter:
                    query += " AND author LIKE ?"
                    params.append(f"%{author_filter}%")

                if date_from:
                    query += " AND date >= ?"
                    params.append(date_from)

                if date_to:
                    query += " AND date <= ?"
                    params.append(date_to)

                if search_query:
                    query += " AND (commit_message LIKE ? OR author LIKE ? OR repository_name LIKE ?)"
                    search_param = f"%{search_query}%"
                    params.extend([search_param, search_param, search_param])

                cursor = conn.execute(query, params)
                result = cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            self.logger.error(f"Error getting document count: {e}")
            return 0

    def get_migration_status(self) -> Dict[str, Any]:
        """Get database migration status"""
        try:
            migration = DatabaseMigration(str(self.db_path))
            return migration.get_migration_status()
        except Exception as e:
            self.logger.error(f"Error getting migration status: {e}")
            return {'error': str(e)}
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete document by ID"""
        try:
            with self._get_connection() as conn:
                conn.execute("DELETE FROM documents WHERE id = ?", (doc_id,))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error deleting document {doc_id}: {e}")
            return False

    def clear_all_documents(self) -> bool:
        """Clear ALL documents from database"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("DELETE FROM documents")
                deleted_count = cursor.rowcount
                conn.commit()
                self.logger.info(f"Cleared {deleted_count} documents from database")
                return True
        except Exception as e:
            self.logger.error(f"Error clearing all documents: {e}")
            return False
    
    def _row_to_document(self, row: sqlite3.Row) -> Optional[DocumentRecord]:
        """Convert database row to DocumentRecord"""
        try:
            # Convert integer boolean values back to Python booleans
            code_review_rec = None if row['code_review_recommended'] is None else bool(row['code_review_recommended'])
            doc_impact = None if row['documentation_impact'] is None else bool(row['documentation_impact'])

            # Handle changed_paths field for backward compatibility
            try:
                changed_paths_json = row['changed_paths']
                changed_paths = json.loads(changed_paths_json) if changed_paths_json else None
            except (KeyError, IndexError, json.JSONDecodeError):
                changed_paths = None

            # Handle heuristic_context field for backward compatibility
            try:
                heuristic_context_json = row['heuristic_context']
                heuristic_context = json.loads(heuristic_context_json) if heuristic_context_json else None
            except (KeyError, IndexError, json.JSONDecodeError):
                heuristic_context = None

            # Handle repository metadata fields for backward compatibility
            try:
                repository_url = row['repository_url']
                repository_type = row['repository_type']
            except (KeyError, IndexError):
                repository_url = None
                repository_type = None

            # Handle user feedback fields for backward compatibility
            def safe_get_field(field_name, default=None):
                try:
                    return row[field_name]
                except (KeyError, IndexError):
                    return default

            def safe_get_datetime_field(field_name):
                try:
                    value = row[field_name]
                    return datetime.fromisoformat(value) if value else None
                except (KeyError, IndexError, ValueError):
                    return None

            return DocumentRecord(
                id=row['id'],
                repository_id=row['repository_id'],
                repository_name=row['repository_name'],
                revision=row['revision'],
                date=datetime.fromisoformat(row['date']),
                filename=row['filename'],
                filepath=row['filepath'],
                size=row['size'],
                author=row['author'],
                commit_message=row['commit_message'],
                changed_paths=changed_paths,
                code_review_recommended=code_review_rec,
                code_review_priority=row['code_review_priority'],
                documentation_impact=doc_impact,
                risk_level=row['risk_level'],
                file_modified_time=row['file_modified_time'],
                processed_time=datetime.fromisoformat(row['processed_time']) if row['processed_time'] else None,
                ai_model_used=safe_get_field('ai_model_used'),
                risk_aggressiveness_used=safe_get_field('risk_aggressiveness_used'),
                repository_url=repository_url,
                repository_type=repository_type,
                # User feedback fields
                user_code_review_status=safe_get_field('user_code_review_status'),
                user_code_review_comments=safe_get_field('user_code_review_comments'),
                user_code_review_reviewer=safe_get_field('user_code_review_reviewer'),
                user_code_review_date=safe_get_datetime_field('user_code_review_date'),
                user_documentation_rating=safe_get_field('user_documentation_rating'),
                user_documentation_comments=safe_get_field('user_documentation_comments'),
                user_documentation_updated_by=safe_get_field('user_documentation_updated_by'),
                user_documentation_updated_date=safe_get_datetime_field('user_documentation_updated_date'),
                user_risk_assessment_override=safe_get_field('user_risk_assessment_override'),
                user_risk_assessment_comments=safe_get_field('user_risk_assessment_comments'),
                user_risk_assessment_updated_by=safe_get_field('user_risk_assessment_updated_by'),
                user_risk_assessment_updated_date=safe_get_datetime_field('user_risk_assessment_updated_date'),
                user_documentation_input=safe_get_field('user_documentation_input'),
                user_documentation_suggestions=safe_get_field('user_documentation_suggestions'),
                user_documentation_input_by=safe_get_field('user_documentation_input_by'),
                user_documentation_input_date=safe_get_datetime_field('user_documentation_input_date'),
                ai_summary=safe_get_field('ai_summary'),
                heuristic_context=heuristic_context
            )
        except Exception as e:
            self.logger.error(f"Error converting row to document: {e}")
            return None
