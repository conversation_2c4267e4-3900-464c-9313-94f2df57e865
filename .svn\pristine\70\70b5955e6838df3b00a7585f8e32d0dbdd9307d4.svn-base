#!/bin/bash
# Docker Permissions Fix Script for RepoSense AI
# This script fixes common Docker volume permission issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔧 RepoSense AI Docker Permissions Fix${NC}"
echo "This script fixes Docker volume permission issues for RepoSense AI."
echo

# Function to log messages
log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "reposense_ai" ]; then
    log_error "reposense_ai directory not found. Please run this script from the parent directory."
    exit 1
fi

log_step "1. Checking current permissions..."

# Check current ownership
if [ -d "reposense_ai/data" ]; then
    DATA_OWNER=$(stat -c '%u:%g' reposense_ai/data 2>/dev/null || echo "unknown")
    log_info "Current data directory ownership: $DATA_OWNER"
else
    log_warn "Data directory does not exist - will be created"
fi

if [ -d "reposense_ai/logs" ]; then
    LOGS_OWNER=$(stat -c '%u:%g' reposense_ai/logs 2>/dev/null || echo "unknown")
    log_info "Current logs directory ownership: $LOGS_OWNER"
else
    log_warn "Logs directory does not exist - will be created"
fi

log_step "2. Stopping RepoSense AI container (if running)..."

# Stop container if running
if docker-compose ps reposense-ai | grep -q "Up"; then
    log_info "Stopping RepoSense AI container..."
    docker-compose stop reposense-ai
    CONTAINER_WAS_RUNNING=true
else
    log_info "Container is not running"
    CONTAINER_WAS_RUNNING=false
fi

log_step "3. Creating directories and fixing permissions..."

# Create directories if they don't exist
mkdir -p reposense_ai/data
mkdir -p reposense_ai/logs
mkdir -p reposense_ai/data/output
mkdir -p reposense_ai/data/cache

log_info "Created necessary directories"

# Fix ownership - Docker container runs as uid 1000 (appuser)
log_info "Setting ownership to uid 1000 (Docker appuser)..."

if command -v sudo >/dev/null 2>&1; then
    sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs
    sudo chmod -R 755 reposense_ai/data reposense_ai/logs
    log_info "Set ownership to 1000:1000 and permissions to 755"
else
    log_error "sudo not available - you need to run these commands manually:"
    echo "  chown -R 1000:1000 reposense_ai/data reposense_ai/logs"
    echo "  chmod -R 755 reposense_ai/data reposense_ai/logs"
    exit 1
fi

log_step "4. Verifying permissions..."

# Verify the fix
DATA_OWNER_NEW=$(stat -c '%u:%g' reposense_ai/data)
LOGS_OWNER_NEW=$(stat -c '%u:%g' reposense_ai/logs)

if [ "$DATA_OWNER_NEW" = "1000:1000" ] && [ "$LOGS_OWNER_NEW" = "1000:1000" ]; then
    log_info "Permissions fixed successfully!"
    log_info "Data directory: $DATA_OWNER_NEW"
    log_info "Logs directory: $LOGS_OWNER_NEW"
else
    log_error "Permission fix failed!"
    log_error "Data directory: $DATA_OWNER_NEW (should be 1000:1000)"
    log_error "Logs directory: $LOGS_OWNER_NEW (should be 1000:1000)"
    exit 1
fi

log_step "5. Restarting container (if it was running)..."

if [ "$CONTAINER_WAS_RUNNING" = true ]; then
    log_info "Restarting RepoSense AI container..."
    docker-compose up -d reposense-ai
    
    # Wait a moment for container to start
    sleep 5
    
    # Check if container started successfully
    if docker-compose ps reposense-ai | grep -q "Up"; then
        log_info "Container restarted successfully"
    else
        log_warn "Container may have issues starting - check logs with: docker-compose logs reposense-ai"
    fi
else
    log_info "Container was not running - you can start it with: docker-compose up -d reposense-ai"
fi

echo
echo -e "${GREEN}✅ Docker permissions fix completed!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Check container logs: docker-compose logs reposense-ai"
echo "2. Verify web interface: http://localhost:5000"
echo "3. If issues persist, check the container logs for other errors"
echo
echo -e "${YELLOW}💡 To prevent this issue in the future:${NC}"
echo "Always set proper ownership after copying files:"
echo "  sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs"
