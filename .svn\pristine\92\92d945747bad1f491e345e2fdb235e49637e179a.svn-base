{% extends "base.html" %}

{% block title %}{{ document.display_name }} - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">{{ document.display_name }}</h1>
            <p class="page-subtitle">{{ document.repository_name }} - {{ document.commit_message or 'No commit message' }}</p>
        </div>
        <div>
            <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Documents
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download"></i> Download
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="downloadDocument('markdown')">
                        <i class="fab fa-markdown me-2"></i>Markdown (.md)
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="downloadDocument('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF (.pdf)
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="testSimpleDownload(); return false;">
                        <i class="fas fa-bug me-2"></i>Test Simple Download
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Document Metadata -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-info" style="border-width: 2px;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Revision Summary Document</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Repository:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-primary">{{ document.repository_name }}</span>
                            </dd>
                            <dt class="col-sm-4">Revision:</dt>
                            <dd class="col-sm-8"><strong>{{ document.revision }}</strong></dd>
                            <dt class="col-sm-4">Author:</dt>
                            <dd class="col-sm-8">{{ document.author }}</dd>
                            <dt class="col-sm-4">Date:</dt>
                            <dd class="col-sm-8">{{ document.date.strftime('%Y-%m-%d %H:%M:%S UTC') }}</dd>

                            {% if document.processed_time %}
                            <dt class="col-sm-4">Processed:</dt>
                            <dd class="col-sm-8">
                                <small class="text-muted">
                                    <i class="fas fa-robot me-1"></i>
                                    {{ document.processed_time.strftime('%Y-%m-%d %H:%M:%S UTC') }}
                                </small>
                            </dd>
                            {% endif %}
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Filename:</dt>
                            <dd class="col-sm-8"><code>{{ document.filename }}</code></dd>
                            <dt class="col-sm-4">Size:</dt>
                            <dd class="col-sm-8">{{ (document.size / 1024)|round(1) }} KB</dd>
                            <dt class="col-sm-4">Path:</dt>
                            <dd class="col-sm-8"><small class="text-muted">{{ document.relative_path }}</small></dd>
                            <dt class="col-sm-4">Commit Message:</dt>
                            <dd class="col-sm-8">
                                <div class="commit-message">
                                    {% if document.commit_message and document.commit_message.strip() %}
                                        {% set lines = document.commit_message.split('\n') %}
                                        {% for line in lines %}
                                            {% if line.strip() %}
                                                {% if line.strip().startswith('-') %}
                                                    <div class="commit-bullet">{{ line.strip() }}</div>
                                                {% else %}
                                                    <div class="commit-line">{{ line.strip() }}</div>
                                                {% endif %}
                                            {% else %}
                                                <div class="commit-spacer"></div>
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            No commit message available
                                        </div>
                                    {% endif %}
                                </div>
                            </dd>

                            <dt class="col-sm-4">Files Changed:</dt>
                            <dd class="col-sm-8">
                                <div class="changed-files">
                                    {% if document.changed_paths and document.changed_paths|length > 0 %}
                                        {% if document.changed_paths|length <= 10 %}
                                            <!-- Show all files if 10 or fewer -->
                                            {% for file_path in document.changed_paths %}
                                                <div class="file-path mb-1">
                                                    <i class="fas fa-file-code text-muted me-1"></i>
                                                    <code class="text-primary">{{ file_path }}</code>
                                                </div>
                                            {% endfor %}
                                        {% else %}
                                            <!-- Show first 8 files, then summary -->
                                            {% for file_path in document.changed_paths[:8] %}
                                                <div class="file-path mb-1">
                                                    <i class="fas fa-file-code text-muted me-1"></i>
                                                    <code class="text-primary">{{ file_path }}</code>
                                                </div>
                                            {% endfor %}
                                            <div class="file-path-summary text-muted mt-2 mb-2">
                                                <i class="fas fa-ellipsis-h me-1"></i>
                                                and {{ document.changed_paths|length - 8 }} more files
                                                <button class="btn btn-link btn-sm p-0 ms-2" onclick="toggleAllFiles()" id="toggle-files-btn">
                                                    <small>Show all</small>
                                                </button>
                                            </div>
                                            <div id="remaining-files" style="display: none;">
                                            {% for file_path in document.changed_paths[8:] %}
                                                <div class="file-path mb-1">
                                                    <i class="fas fa-file-code text-muted me-1"></i>
                                                    <code class="text-primary">{{ file_path }}</code>
                                                </div>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                {{ document.changed_paths|length }} file{{ 's' if document.changed_paths|length != 1 else '' }} changed in this commit
                                            </small>
                                        </div>
                                    {% else %}
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            No file change information available
                                        </div>
                                    {% endif %}
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Processing Information Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-success" style="border-width: 2px;">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-robot me-2"></i> AI Processing Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">AI Model:</dt>
                            <dd class="col-sm-7">
                                {% if document.ai_model_used %}
                                    <span class="badge bg-success">{{ document.ai_model_used }}</span>
                                {% else %}
                                    <span class="badge bg-warning text-dark">Not recorded (processed before AI tracking)</span>
                                {% endif %}
                            </dd>
                            <dt class="col-sm-5">AI Host:</dt>
                            <dd class="col-sm-7">
                                <small class="text-muted">{{ config.ollama_host if config.ollama_host else 'Not configured' }}</small>
                            </dd>
                            {% if document.processed_time %}
                            <dt class="col-sm-5">Processing Time:</dt>
                            <dd class="col-sm-7">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ document.processed_time.strftime('%Y-%m-%d %H:%M:%S UTC') }}
                                </small>
                            </dd>
                            {% endif %}
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            {% if document.code_review_recommended is not none %}
                            <dt class="col-sm-5">Code Review:</dt>
                            <dd class="col-sm-7">
                                {% if document.code_review_recommended %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-eye me-1"></i>Recommended
                                    </span>
                                    {% if document.code_review_priority %}
                                        <span class="badge bg-{{ 'danger' if document.code_review_priority == 'HIGH' else 'warning' if document.code_review_priority == 'MEDIUM' else 'info' }} ms-1">
                                            {{ document.code_review_priority }}
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Not Required
                                    </span>
                                {% endif %}
                            </dd>
                            {% endif %}

                            {% if document.risk_level %}
                            <dt class="col-sm-5">Risk Level:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-{{ 'danger' if document.risk_level == 'HIGH' else 'warning' if document.risk_level == 'MEDIUM' else 'success' }}">
                                    <i class="fas fa-{{ 'exclamation-triangle' if document.risk_level == 'HIGH' else 'exclamation-circle' if document.risk_level == 'MEDIUM' else 'shield-alt' }} me-1"></i>
                                    {{ document.risk_level }}
                                </span>
                            </dd>
                            {% endif %}

                            {% if document.documentation_impact is not none %}
                            <dt class="col-sm-5">Doc Impact:</dt>
                            <dd class="col-sm-7">
                                {% if document.documentation_impact %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-book me-1"></i>Update Needed
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-check me-1"></i>No Update
                                    </span>
                                {% endif %}
                            </dd>
                            {% endif %}
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Content -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <button class="btn btn-link text-white p-0 text-decoration-none"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#documentContent"
                            aria-expanded="true"
                            aria-controls="documentContent"
                            id="collapseToggle">
                        <i class="fas fa-file-alt me-2"></i>
                        <span>Document Content</span>
                        <i class="fas fa-chevron-up ms-2" id="collapseIcon"></i>
                    </button>
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-light" onclick="toggleRawView()" id="toggleRawBtn">
                        <i class="fas fa-code"></i> Raw View
                    </button>
                    <button class="btn btn-sm btn-light" onclick="copyToClipboard()">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
            </div>
            <div class="collapse show" id="documentContent">
                <div class="card-body p-0">
                    <!-- Rendered Markdown View -->
                    <div id="renderedView" class="p-4">
                        <div class="markdown-content">
                            {{ content|markdown }}
                        </div>
                    </div>

                    <!-- Raw Text View -->
                    <div id="rawView" class="p-0" style="display: none;">
                        <pre class="mb-0 p-4" style="background-color: #f8f9fa; border: none; border-radius: 0;"><code id="rawContent">{{ content }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if include_diff and diff_content %}
<!-- Diff Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-warning" style="border-width: 2px;">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <button class="btn btn-link text-dark p-0 text-decoration-none"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#diffContent"
                            aria-expanded="true"
                            aria-controls="diffContent"
                            id="diffCollapseToggle">
                        <i class="fas fa-code me-2"></i>
                        <span>Code Changes (Diff)</span>
                        <i class="fas fa-chevron-up ms-2" id="diffCollapseIcon"></i>
                    </button>
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-dark" onclick="copyDiffToClipboard()">
                        <i class="fas fa-copy"></i> Copy Diff
                    </button>
                </div>
            </div>
            <div class="collapse show" id="diffContent">
                <div class="card-body p-0">
                    {% if diff_format == 'side-by-side' %}
                        <div id="diffContentCode" style="max-height: 600px; overflow-y: auto;">
                            {{ diff_content|safe }}
                        </div>
                        <!-- Hidden element with raw diff content for copying -->
                        <div id="rawDiffContent" style="display: none;">{{ raw_diff_content or diff_content }}</div>
                    {% else %}
                        <div id="diffContentCode" class="unified-diff" style="max-height: 600px; overflow-y: auto;">
                            {% set lines = diff_content.split('\n') %}
                            {% for line in lines %}
                                {% if line.strip() %}
                                    {% if line.startswith('Index:') or line.startswith('===') or line.startswith('---') or line.startswith('+++') %}
                                        <div class="diff-file-header">{{ line }}</div>
                                    {% elif line.startswith('@@') %}
                                        <div class="diff-hunk-header">{{ line }}</div>
                                    {% elif line.startswith('+') and not line.startswith('+++') %}
                                        <div class="diff-line-added">{{ line }}</div>
                                    {% elif line.startswith('-') and not line.startswith('---') %}
                                        <div class="diff-line-removed">{{ line }}</div>
                                    {% else %}
                                        <div class="diff-line-context">{{ line }}</div>
                                    {% endif %}
                                {% else %}
                                    <div class="diff-line-empty">&nbsp;</div>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <!-- Hidden element with raw diff content for copying and markdown download -->
                        <div id="rawDiffContent" style="display: none;">{{ raw_diff_content or diff_content }}</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- User Feedback Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-info" style="border-width: 2px;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>User Feedback & Review
                </h5>
            </div>
            <div class="card-body">
                <!-- Code Review Feedback -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <h6 class="text-primary">
                            <i class="fas fa-code-branch me-2"></i>Code Review Status
                        </h6>
                        <div class="mb-3">
                            <label class="form-label">AI Recommendation:</label>
                            <div>
                                {% if document.code_review_recommended %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-exclamation-triangle"></i> Review Recommended
                                    </span>
                                    {% if document.code_review_priority %}
                                        <span class="badge bg-{{ 'danger' if document.code_review_priority == 'HIGH' else 'warning' if document.code_review_priority == 'MEDIUM' else 'info' }}">
                                            {{ document.code_review_priority }} Priority
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> No Review Needed
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="codeReviewStatus" class="form-label">Your Review Status:</label>
                            <select class="form-select" id="codeReviewStatus">
                                <option value="">Select status...</option>
                                <option value="approved" {{ 'selected' if document.user_code_review_status == 'approved' else '' }}>✅ Approved</option>
                                <option value="needs_changes" {{ 'selected' if document.user_code_review_status == 'needs_changes' else '' }}>🔄 Needs Changes</option>
                                <option value="rejected" {{ 'selected' if document.user_code_review_status == 'rejected' else '' }}>❌ Rejected</option>
                                <option value="in_progress" {{ 'selected' if document.user_code_review_status == 'in_progress' else '' }}>⏳ In Progress</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="codeReviewReviewer" class="form-label">Reviewer:</label>
                            <input type="text" class="form-control" id="codeReviewReviewer"
                                   value="{{ document.user_code_review_reviewer or '' }}"
                                   placeholder="Enter reviewer name">
                        </div>

                        <div class="mb-3">
                            <label for="codeReviewComments" class="form-label">Comments:</label>
                            <textarea class="form-control" id="codeReviewComments" rows="3"
                                      placeholder="Enter review comments...">{{ document.user_code_review_comments or '' }}</textarea>
                        </div>

                        <button class="btn btn-primary btn-sm" onclick="updateCodeReviewFeedback()">
                            <i class="fas fa-save"></i> Save Review
                        </button>

                        {% if document.user_code_review_date %}
                        <div class="mt-2">
                            <small class="text-muted">
                                Last updated: {{ document.user_code_review_date.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Documentation Quality Feedback -->
                    <div class="col-md-4">
                        <h6 class="text-success">
                            <i class="fas fa-file-alt me-2"></i>Documentation Quality
                        </h6>
                        <div class="mb-3">
                            <label class="form-label">AI Assessment:</label>
                            <div>
                                {% if document.documentation_impact %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-edit"></i> Updates Needed
                                    </span>
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> No Updates Needed
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="documentationRating" class="form-label">Your Rating (1-5):</label>
                            <select class="form-select" id="documentationRating">
                                <option value="">Select rating...</option>
                                <option value="1" {{ 'selected' if document.user_documentation_rating == 1 else '' }}>⭐ 1 - Poor</option>
                                <option value="2" {{ 'selected' if document.user_documentation_rating == 2 else '' }}>⭐⭐ 2 - Fair</option>
                                <option value="3" {{ 'selected' if document.user_documentation_rating == 3 else '' }}>⭐⭐⭐ 3 - Good</option>
                                <option value="4" {{ 'selected' if document.user_documentation_rating == 4 else '' }}>⭐⭐⭐⭐ 4 - Very Good</option>
                                <option value="5" {{ 'selected' if document.user_documentation_rating == 5 else '' }}>⭐⭐⭐⭐⭐ 5 - Excellent</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="documentationUpdatedBy" class="form-label">Updated By:</label>
                            <input type="text" class="form-control" id="documentationUpdatedBy"
                                   value="{{ document.user_documentation_updated_by or '' }}"
                                   placeholder="Enter your name">
                        </div>

                        <div class="mb-3">
                            <label for="documentationComments" class="form-label">Comments:</label>
                            <textarea class="form-control" id="documentationComments" rows="3"
                                      placeholder="Enter feedback on documentation quality...">{{ document.user_documentation_comments or '' }}</textarea>
                        </div>

                        <button class="btn btn-success btn-sm" onclick="updateDocumentationFeedback()">
                            <i class="fas fa-save"></i> Save Feedback
                        </button>

                        {% if document.user_documentation_updated_date %}
                        <div class="mt-2">
                            <small class="text-muted">
                                Last updated: {{ document.user_documentation_updated_date.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Risk Assessment Override -->
                    <div class="col-md-4">
                        <h6 class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Risk Assessment
                        </h6>
                        <div class="mb-3">
                            <label class="form-label">AI Assessment:</label>
                            <div>
                                {% if document.risk_level %}
                                    <span class="badge bg-{{ 'danger' if document.risk_level == 'HIGH' else 'warning' if document.risk_level == 'MEDIUM' else 'info' }}">
                                        {{ document.risk_level }} Risk
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">Unknown</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="riskAssessmentOverride" class="form-label">Your Assessment:</label>
                            <select class="form-select" id="riskAssessmentOverride">
                                <option value="">Use AI assessment...</option>
                                <option value="LOW" {{ 'selected' if document.user_risk_assessment_override == 'LOW' else '' }}>🟢 LOW Risk</option>
                                <option value="MEDIUM" {{ 'selected' if document.user_risk_assessment_override == 'MEDIUM' else '' }}>🟡 MEDIUM Risk</option>
                                <option value="HIGH" {{ 'selected' if document.user_risk_assessment_override == 'HIGH' else '' }}>🔴 HIGH Risk</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="riskAssessmentUpdatedBy" class="form-label">Assessed By:</label>
                            <input type="text" class="form-control" id="riskAssessmentUpdatedBy"
                                   value="{{ document.user_risk_assessment_updated_by or '' }}"
                                   placeholder="Enter your name">
                        </div>

                        <div class="mb-3">
                            <label for="riskAssessmentComments" class="form-label">Comments:</label>
                            <textarea class="form-control" id="riskAssessmentComments" rows="3"
                                      placeholder="Explain your risk assessment...">{{ document.user_risk_assessment_comments or '' }}</textarea>
                        </div>

                        <button class="btn btn-danger btn-sm" onclick="updateRiskAssessmentFeedback()">
                            <i class="fas fa-save"></i> Save Assessment
                        </button>

                        {% if document.user_risk_assessment_updated_date %}
                        <div class="mt-2">
                            <small class="text-muted">
                                Last updated: {{ document.user_risk_assessment_updated_date.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documentation Input Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-warning" style="border-width: 2px;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Product Documentation Feedback
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-warning">
                            <i class="fas fa-file-alt me-2"></i>Product Documentation Content
                        </h6>
                        <div class="mb-3">
                            <label class="form-label">AI Recommendation:</label>
                            <div>
                                {% if document.documentation_impact %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-edit"></i> Product Documentation Updates Recommended
                                    </span>
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Product Documentation Complete
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="documentationInput" class="form-label">Product Documentation Content:</label>
                            <textarea class="form-control" id="documentationInput" rows="6"
                                      placeholder="Enter product documentation content for this change. This will be included in user-facing documentation...">{{ document.user_documentation_input or '' }}</textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i> This content will be included in product documentation and user-facing materials.
                                <div id="ai-suggestions-status" style="display: none;">
                                    <br><i class="fas fa-robot text-info"></i> <strong>AI suggestions loaded</strong> - you can edit, expand, or replace this content.
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="documentationInputBy" class="form-label">Documented By:</label>
                            <input type="text" class="form-control" id="documentationInputBy"
                                   value="{{ document.user_documentation_input_by or '' }}"
                                   placeholder="Enter your name">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h6 class="text-info">
                            <i class="fas fa-lightbulb me-2"></i>Documentation Feedback
                        </h6>
                        <div class="mb-3">
                            <label for="documentationSuggestions" class="form-label">Feedback & Suggestions:</label>
                            <textarea class="form-control" id="documentationSuggestions" rows="6"
                                      placeholder="Provide feedback on the AI-generated content, suggestions for improvement, or additional context needed...">{{ document.user_documentation_suggestions or '' }}</textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i> This feedback helps improve AI documentation generation and provides context for the development team.
                            </div>
                        </div>

                        <div class="mb-3">
                            <button class="btn btn-warning btn-sm" onclick="updateDocumentationInput()">
                                <i class="fas fa-save"></i> Save Product Documentation
                            </button>
                            <button class="btn btn-outline-info btn-sm ms-2" id="loadAISuggestionsBtn" onclick="loadAISuggestions()">
                                <i class="fas fa-robot"></i> Load AI Suggestions
                            </button>
                        </div>

                        {% if document.user_documentation_input_date %}
                        <div class="mt-2">
                            <small class="text-muted">
                                Last updated: {{ document.user_documentation_input_date.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm border-secondary" style="border-width: 2px;">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-tools me-2"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-danger" onclick="deleteDocument()">
                        <i class="fas fa-trash"></i> Delete Document
                    </button>
                    <a href="{{ url_for('documents_page') }}?repository={{ document.repository_id }}" class="btn btn-outline-info">
                        <i class="fas fa-folder"></i> View Repository Documents
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this document?</p>
                <p><strong>{{ document.display_name }}</strong></p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> Delete Document
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.markdown-content {
    line-height: 1.6;
    color: #333;
}

.markdown-content h1 {
    border-bottom: 2px solid #e1e4e8;
    padding-bottom: 0.3em;
    margin-bottom: 1em;
    color: #24292e;
}

.markdown-content h2 {
    border-bottom: 1px solid #e1e4e8;
    padding-bottom: 0.3em;
    margin-top: 1.5em;
    margin-bottom: 1em;
    color: #24292e;
}

.markdown-content h3, .markdown-content h4 {
    margin-top: 1.25em;
    margin-bottom: 0.75em;
    color: #24292e;
}

.markdown-content pre {
    background-color: #f6f8fa;
    border-radius: 6px;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
}

.markdown-content code {
    background-color: rgba(175, 184, 193, 0.2);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 85%;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
}

.markdown-content ul {
    padding-left: 2em;
    margin-bottom: 1em;
}

.markdown-content li {
    margin-bottom: 0.25em;
}

.markdown-content strong {
    font-weight: 600;
}

.markdown-content p {
    margin-bottom: 1em;
}

.markdown-content blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin-bottom: 1em;
}

.commit-message {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    max-width: 100%;
}

.commit-line {
    margin-bottom: 8px;
    line-height: 1.4;
}

.commit-bullet {
    margin-bottom: 4px;
    margin-left: 16px;
    line-height: 1.4;
    color: #495057;
    position: relative;
}

.commit-bullet:before {
    content: "•";
    color: #007bff;
    font-weight: bold;
    position: absolute;
    left: -12px;
}

.commit-spacer {
    height: 8px;
}

/* Enhanced Unified Diff Styles */
.unified-diff {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.4;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow-x: auto;
}

.diff-file-header {
    background-color: #e9ecef;
    color: #495057;
    font-weight: bold;
    padding: 8px 12px;
    border-bottom: 1px solid #dee2e6;
    margin: 0;
}

.diff-hunk-header {
    background-color: #cce5ff;
    color: #0056b3;
    font-weight: bold;
    padding: 6px 12px;
    margin: 0;
    border-top: 1px solid #b3d9ff;
    border-bottom: 1px solid #b3d9ff;
}

.diff-line-added {
    background-color: #d4edda;
    color: #155724;
    padding: 2px 12px;
    margin: 0;
    border-left: 3px solid #28a745;
}

.diff-line-removed {
    background-color: #f8d7da;
    color: #721c24;
    padding: 2px 12px;
    margin: 0;
    border-left: 3px solid #dc3545;
}

.diff-line-context {
    background-color: #ffffff;
    color: #495057;
    padding: 2px 12px;
    margin: 0;
}

.diff-line-empty {
    background-color: #ffffff;
    padding: 2px 12px;
    margin: 0;
    height: 1.4em;
}

/* Hover effects for better interactivity */
.diff-line-added:hover {
    background-color: #c3e6cb;
}

.diff-line-removed:hover {
    background-color: #f1b0b7;
}

.diff-line-context:hover {
    background-color: #f8f9fa;
}

/* Additional visual enhancements */
.unified-diff .diff-line-added::before {
    content: "+";
    color: #28a745;
    font-weight: bold;
    margin-right: 8px;
}

.unified-diff .diff-line-removed::before {
    content: "-";
    color: #dc3545;
    font-weight: bold;
    margin-right: 8px;
}

.unified-diff .diff-line-context::before {
    content: " ";
    margin-right: 8px;
}

/* Make the diff more compact and readable */
.unified-diff div {
    white-space: pre-wrap;
    word-break: break-all;
    overflow-wrap: break-word;
}

/* Improve readability on smaller screens */
@media (max-width: 768px) {
    .unified-diff {
        font-size: 12px;
    }

    .diff-line-added,
    .diff-line-removed,
    .diff-line-context {
        padding: 2px 8px;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
let rawViewVisible = false;

// Document data for downloads (properly escaped JSON)
{% set changed_files = document.changed_paths or [] %}
{% set files_count = changed_files|length %}
{% set files_text = files_count|string + " file" + ("s" if files_count != 1 else "") + " changed in this commit" %}
const documentData = {{ {
    'id': document.id,
    'displayName': document.display_name,
    'repositoryName': document.repository_name,
    'revision': document.revision,
    'author': document.author,
    'date': document.date.strftime("%Y-%m-%d %H:%M:%S"),
    'filename': document.filename,
    'size': "%.1f"|format(document.size / 1024),
    'relativePath': document.relative_path,
    'commitMessage': document.commit_message or 'No commit message available',
    'changedFiles': changed_files,
    'filesChangedCount': files_text if changed_files else "No file change information available",
    'userFeedback': {
        'codeReviewRecommended': document.code_review_recommended or False,
        'codeReviewPriority': document.code_review_priority or '',
        'codeReviewStatus': document.user_code_review_status or '',
        'codeReviewComments': document.user_code_review_comments or '',
        'codeReviewReviewer': document.user_code_review_reviewer or '',
        'documentationRating': document.user_documentation_rating or None,
        'documentationComments': document.user_documentation_comments or '',
        'documentationUpdatedBy': document.user_documentation_updated_by or '',
        'documentationInput': document.user_documentation_input or '',
        'documentationSuggestions': document.user_documentation_suggestions or '',
        'documentationInputBy': document.user_documentation_input_by or ''
    }
} | tojson }};

// Raw diff content for markdown downloads
const rawDiffContent = {{ (raw_diff_content or '')|tojson|safe }};

// Handle collapse/expand icon changes
document.addEventListener('DOMContentLoaded', function() {
    const documentContent = document.getElementById('documentContent');
    const collapseIcon = document.getElementById('collapseIcon');

    documentContent.addEventListener('shown.bs.collapse', function () {
        collapseIcon.classList.remove('fa-chevron-down');
        collapseIcon.classList.add('fa-chevron-up');
    });

    documentContent.addEventListener('hidden.bs.collapse', function () {
        collapseIcon.classList.remove('fa-chevron-up');
        collapseIcon.classList.add('fa-chevron-down');
    });

    // Handle diff collapse/expand icon changes
    const diffContent = document.getElementById('diffContent');
    const diffCollapseIcon = document.getElementById('diffCollapseIcon');

    if (diffContent && diffCollapseIcon) {
        diffContent.addEventListener('shown.bs.collapse', function () {
            diffCollapseIcon.classList.remove('fa-chevron-down');
            diffCollapseIcon.classList.add('fa-chevron-up');
        });

        diffContent.addEventListener('hidden.bs.collapse', function () {
            diffCollapseIcon.classList.remove('fa-chevron-up');
            diffCollapseIcon.classList.add('fa-chevron-down');
        });
    }
});

function toggleRawView() {
    const renderedView = document.getElementById('renderedView');
    const rawView = document.getElementById('rawView');
    const toggleBtn = document.getElementById('toggleRawBtn');
    
    if (rawViewVisible) {
        renderedView.style.display = 'block';
        rawView.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-code"></i> Raw View';
        rawViewVisible = false;
    } else {
        renderedView.style.display = 'none';
        rawView.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i> Rendered View';
        rawViewVisible = true;
    }
}

function copyToClipboard() {
    const content = document.getElementById('rawContent').textContent;
    const btn = event.target.closest('button');
    const originalHTML = btn.innerHTML;

    // Function to show success feedback
    function showSuccess() {
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.classList.remove('btn-light', 'btn-outline-primary');
        btn.classList.add('btn-success');

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-light');
        }, 2000);
    }

    // Function to show error feedback
    function showError(message) {
        console.error('Copy failed:', message);
        btn.innerHTML = '<i class="fas fa-times"></i> Failed';
        btn.classList.remove('btn-light', 'btn-outline-primary');
        btn.classList.add('btn-danger');

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-danger');
            btn.classList.add('btn-light');
        }, 2000);

        // Also show a more user-friendly message
        alert('Failed to copy to clipboard. Please try selecting and copying the text manually.');
    }

    // Try modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(content)
            .then(showSuccess)
            .catch(err => {
                console.warn('Modern clipboard API failed, trying fallback:', err);
                fallbackCopyToClipboard(content, showSuccess, showError);
            });
    } else {
        // Use fallback method
        fallbackCopyToClipboard(content, showSuccess, showError);
    }
}

function fallbackCopyToClipboard(text, onSuccess, onError) {
    // Create a temporary textarea element
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);

    try {
        textArea.focus();
        textArea.select();

        // Try to copy using execCommand
        const successful = document.execCommand('copy');
        if (successful) {
            onSuccess();
        } else {
            onError('execCommand copy failed');
        }
    } catch (err) {
        onError('execCommand not supported: ' + err.message);
    } finally {
        document.body.removeChild(textArea);
    }
}

function copyDiffToClipboard() {
    // Try to get raw diff content first (for side-by-side format)
    let diffElement = document.getElementById('rawDiffContent');

    // Fallback to regular diff content (for unified format)
    if (!diffElement || !diffElement.textContent.trim()) {
        diffElement = document.getElementById('diffContentCode');
    }

    if (!diffElement) {
        alert('No diff content available');
        return;
    }

    const content = diffElement.textContent.trim();
    const btn = event.target.closest('button');
    const originalHTML = btn.innerHTML;

    // Function to show success feedback
    function showSuccess() {
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.classList.remove('btn-outline-dark');
        btn.classList.add('btn-success');

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-dark');
        }, 2000);
    }

    // Function to show error feedback
    function showError(message) {
        console.error('Copy diff failed:', message);
        btn.innerHTML = '<i class="fas fa-times"></i> Failed';
        btn.classList.remove('btn-outline-dark');
        btn.classList.add('btn-danger');

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-danger');
            btn.classList.add('btn-outline-dark');
        }, 2000);

        alert('Failed to copy diff to clipboard. Please try selecting and copying the text manually.');
    }

    // Try modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(content)
            .then(showSuccess)
            .catch(err => {
                console.warn('Modern clipboard API failed for diff, trying fallback:', err);
                fallbackCopyToClipboard(content, showSuccess, showError);
            });
    } else {
        // Use fallback method
        fallbackCopyToClipboard(content, showSuccess, showError);
    }
}

function testSimpleDownload() {
    console.log('Testing simple download...');
    try {
        const content = 'Hello World!\nThis is a simple test download.\nTimestamp: ' + new Date().toISOString();
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'simple-test.txt';
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        console.log('Simple download completed');
        alert('Simple download should have worked! Check your downloads folder.');
    } catch (error) {
        console.error('Simple download failed:', error);
        alert('Simple download failed: ' + error.message);
    }
}

function downloadDocument(format = 'markdown') {
    console.log('Download requested:', format);
    console.log('Document data:', documentData);
    console.log('Document data keys:', Object.keys(documentData));
    console.log('Document data values:', Object.values(documentData));

    try {
        if (format === 'pdf') {
            downloadAsPDF();
        } else {
            downloadAsMarkdown();
        }
    } catch (error) {
        console.error('Download failed:', error);
        alert('Download failed: ' + error.message);
    }
}

function downloadAsMarkdown() {
    console.log('=== MARKDOWN DOWNLOAD DEBUG ===');
    console.log('Document data:', documentData);
    console.log('User feedback data:', documentData.userFeedback);
    console.log('All userFeedback keys:', Object.keys(documentData.userFeedback || {}));
    console.log('All userFeedback values:', Object.values(documentData.userFeedback || {}));

    if (!documentData) {
        console.error('Document data not available');
        alert('Document data not available for download');
        return;
    }

    const baseFilename = documentData.filename || 'document';
    const filename = baseFilename.replace(/\.[^/.]+$/, '') + '.md';
    console.log('Download filename:', filename);

    // Build complete markdown content with professional formatting
    let content = '# ' + (documentData.displayName || 'Document') + '\n\n';

    // Add document metadata in a clean table format
    content += '## 📋 Document Information\n\n';
    content += '| Field | Value |\n';
    content += '|-------|-------|\n';
    content += '| **Repository** | `' + documentData.repositoryName + '` |\n';
    content += '| **Revision** | `' + documentData.revision + '` |\n';
    content += '| **Author** | ' + documentData.author + ' |\n';
    content += '| **Date** | ' + documentData.date + ' UTC |\n';
    content += '| **Filename** | `' + documentData.filename + '` |\n';
    content += '| **Size** | ' + documentData.size + ' KB |\n';
    content += '| **Path** | `' + documentData.relativePath + '` |\n\n';

    // Format commit message with proper markdown structure
    content += '## 📝 Commit Message\n\n';
    if (documentData.commitMessage && documentData.commitMessage !== 'No commit message available') {
        const commitLines = documentData.commitMessage.split('\n');
        let inCodeBlock = false;

        commitLines.forEach(function(line) {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                // Handle markdown headers in commit messages
                if (trimmedLine.startsWith('###')) {
                    content += '\n' + trimmedLine + '\n\n';
                } else if (trimmedLine.startsWith('##')) {
                    content += '\n' + trimmedLine + '\n\n';
                } else if (trimmedLine.startsWith('#')) {
                    content += '\n' + trimmedLine + '\n\n';
                } else if (trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
                    // Handle bullet points
                    content += trimmedLine + '\n';
                } else {
                    // Regular text
                    content += trimmedLine + '\n';
                }
            } else {
                content += '\n';
            }
        });
    } else {
        content += '*No commit message available*\n';
    }
    content += '\n';

    // Files changed section with better formatting
    content += '## 📁 Files Changed\n\n';
    if (documentData.changedFiles && documentData.changedFiles.length > 0) {
        documentData.changedFiles.forEach(function(filePath) {
            content += '- `' + filePath + '`\n';
        });
        content += '\n> ' + documentData.filesChangedCount + '\n\n';
    } else {
        content += '*No file change information available*\n\n';
    }

    // Add AI processing information section with better formatting
    content += '## 🤖 AI Processing Information\n\n';

    // AI Configuration table
    content += '### Configuration\n\n';
    content += '| Setting | Value |\n';
    content += '|---------|-------|\n';
    {% if document.ai_model_used %}
    content += '| **AI Model** | `{{ document.ai_model_used }}` |\n';
    {% else %}
    content += '| **AI Model** | `Not recorded (processed before AI tracking)` |\n';
    {% endif %}
    content += '| **AI Host** | `{{ config.ollama_host if config.ollama_host else "Not configured" }}` |\n';
    {% if document.processed_time %}
    content += '| **Processing Time** | {{ document.processed_time.strftime("%Y-%m-%d %H:%M:%S UTC") }} |\n';
    {% endif %}
    content += '\n';

    // AI Analysis Results with status badges
    content += '### Analysis Results\n\n';
    {% if document.code_review_recommended is not none %}
    {% if document.code_review_recommended %}
    content += '**Code Review:** 🔍 Recommended';
    {% if document.code_review_priority %}
    content += ' ({{ document.code_review_priority }} Priority)';
    {% endif %}
    {% else %}
    content += '**Code Review:** ✅ Not Required';
    {% endif %}
    content += '\n\n';
    {% endif %}

    {% if document.risk_level %}
    let riskIcon = '';
    switch('{{ document.risk_level }}'.toUpperCase()) {
        case 'HIGH': riskIcon = '🔴'; break;
        case 'MEDIUM': riskIcon = '🟡'; break;
        case 'LOW': riskIcon = '🟢'; break;
        default: riskIcon = '⚪'; break;
    }
    content += '**Risk Level:** ' + riskIcon + ' {{ document.risk_level }}\n\n';
    {% endif %}

    {% if document.documentation_impact is not none %}
    {% if document.documentation_impact %}
    content += '**Documentation Impact:** 📝 Update Needed\n\n';
    {% else %}
    content += '**Documentation Impact:** ✅ No Update Required\n\n';
    {% endif %}
    {% endif %}

    // Add product documentation feedback section if available
    console.log('Checking product documentation feedback...');
    console.log('documentationInput:', documentData.userFeedback.documentationInput);
    console.log('documentationSuggestions:', documentData.userFeedback.documentationSuggestions);

    if (documentData.userFeedback.documentationInput || documentData.userFeedback.documentationSuggestions) {
        console.log('Adding product documentation feedback section');
        content += '## 📚 Product Documentation Feedback\n\n';

        if (documentData.userFeedback.documentationInput) {
            content += '### 📄 Product Documentation Content\n\n';
            content += '```markdown\n' + documentData.userFeedback.documentationInput + '\n```\n\n';
        }

        if (documentData.userFeedback.documentationSuggestions) {
            content += '### 💡 Documentation Feedback & Suggestions\n\n';
            content += documentData.userFeedback.documentationSuggestions + '\n\n';
        }

        if (documentData.userFeedback.documentationInputBy) {
            content += '> **Added by:** ' + documentData.userFeedback.documentationInputBy + '\n\n';
        }
    } else {
        console.log('No product documentation feedback to add');
    }

    // Add user feedback & review section if available
    console.log('Checking user feedback & review...');
    console.log('codeReviewRecommended:', documentData.userFeedback.codeReviewRecommended);
    console.log('codeReviewStatus:', documentData.userFeedback.codeReviewStatus);
    console.log('codeReviewComments:', documentData.userFeedback.codeReviewComments);
    console.log('codeReviewReviewer:', documentData.userFeedback.codeReviewReviewer);
    console.log('documentationRating:', documentData.userFeedback.documentationRating);
    console.log('documentationComments:', documentData.userFeedback.documentationComments);

    let hasFeedback = false;
    let feedbackContent = '';

    // Check for code review feedback
    if (documentData.userFeedback.codeReviewRecommended || documentData.userFeedback.codeReviewComments || documentData.userFeedback.codeReviewReviewer || documentData.userFeedback.codeReviewStatus) {
        console.log('Adding code review feedback section');
        if (!hasFeedback) {
            feedbackContent += '## 👥 User Feedback & Review\n\n';
            hasFeedback = true;
        }
        feedbackContent += '### 🔍 Code Review\n\n';

        // Create a review status table
        feedbackContent += '| Field | Value |\n';
        feedbackContent += '|-------|-------|\n';

        if (documentData.userFeedback.codeReviewRecommended) {
            feedbackContent += '| **AI Recommendation** | ⚠️ Review Recommended |\n';
            if (documentData.userFeedback.codeReviewPriority) {
                feedbackContent += '| **Priority** | ' + documentData.userFeedback.codeReviewPriority + ' |\n';
            }
        }

        if (documentData.userFeedback.codeReviewStatus) {
            const statusMap = {
                'approved': '✅ Approved',
                'needs_changes': '🔄 Needs Changes',
                'rejected': '❌ Rejected',
                'in_progress': '⏳ In Progress'
            };
            feedbackContent += '| **Status** | ' + (statusMap[documentData.userFeedback.codeReviewStatus] || documentData.userFeedback.codeReviewStatus) + ' |\n';
        }

        if (documentData.userFeedback.codeReviewReviewer) {
            feedbackContent += '| **Reviewer** | ' + documentData.userFeedback.codeReviewReviewer + ' |\n';
        }

        feedbackContent += '\n';

        if (documentData.userFeedback.codeReviewComments) {
            feedbackContent += '#### 💬 Review Comments\n\n';
            feedbackContent += '> ' + documentData.userFeedback.codeReviewComments.replace(/\n/g, '\n> ') + '\n\n';
        }

        if (documentData.userFeedback.codeReviewReviewer) {
            feedbackContent += '**Reviewer:** ' + documentData.userFeedback.codeReviewReviewer + '\n\n';
        }
    }

    // Check for documentation quality feedback
    if (documentData.userFeedback.documentationRating || documentData.userFeedback.documentationComments || documentData.userFeedback.documentationUpdatedBy) {
        if (!hasFeedback) {
            feedbackContent += '## User Feedback & Review\n\n';
            hasFeedback = true;
        }
        feedbackContent += '### Documentation Quality\n\n';

        if (documentData.userFeedback.documentationRating) {
            feedbackContent += '**Rating:** ';
            const stars = '⭐'.repeat(documentData.userFeedback.documentationRating);
            feedbackContent += stars + ' (' + documentData.userFeedback.documentationRating + '/5)\n\n';
        }

        if (documentData.userFeedback.documentationUpdatedBy) {
            feedbackContent += '**Updated By:** ' + documentData.userFeedback.documentationUpdatedBy + '\n\n';
        }

        if (documentData.userFeedback.documentationComments) {
            feedbackContent += '**Comments:** ' + documentData.userFeedback.documentationComments + '\n\n';
        }
    }

    // Add feedback content to main content if any feedback exists
    if (hasFeedback) {
        content += feedbackContent;
    }

    content += '---\n\n';

    // Add main document content
    content += '## Document Content\n\n';
    content += document.getElementById('rawContent').textContent;

    // If diff content is available, append it using the raw diff content from server
    if (rawDiffContent && rawDiffContent.trim()) {
        content += '\n\n---\n\n## 🔄 Code Changes (Diff)\n\n';

        // Use HTML pre block to preserve exact formatting and prevent markdown interpretation
        // This ensures that markdown syntax within the diff is displayed literally
        content += '<pre><code>' +
                   rawDiffContent.trim()
                       .replace(/&/g, '&amp;')   // Escape HTML entities first
                       .replace(/</g, '&lt;')    // Escape less than
                       .replace(/>/g, '&gt;')    // Escape greater than
                   + '</code></pre>\n\n';
    }

    console.log('=== FINAL CONTENT DEBUG ===');
    console.log('Content length:', content.length);
    console.log('Full content:', content);
    console.log('Content preview (first 500 chars):', content.substring(0, 500) + '...');
    console.log('Content includes "Product Documentation Feedback":', content.includes('Product Documentation Feedback'));
    console.log('Content includes "User Feedback & Review":', content.includes('User Feedback & Review'));

    const blob = new Blob([content], { type: 'text/markdown' });
    console.log('Blob created:', blob.size, 'bytes');

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;

    console.log('Triggering download:', filename);
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    console.log('Download triggered successfully');
}

function downloadAsPDF() {
    // Show loading indicator
    const originalButtons = document.querySelectorAll('.btn-group .dropdown-toggle');
    originalButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
        btn.disabled = true;
    });

    // Prepare content for PDF generation
    let content = document.getElementById('rawContent').textContent;

    // Build complete document info including user feedback
    const documentInfo = {
        title: documentData.displayName,
        repository: documentData.repositoryName,
        revision: documentData.revision,
        author: documentData.author,
        date: documentData.date + ' UTC',
        commit_message: documentData.commitMessage,
        filename: documentData.filename,
        size: documentData.size + ' KB',
        path: documentData.relativePath,
        changed_files: documentData.changedFiles,
        files_changed_count: documentData.filesChangedCount,
        // AI processing information
        ai_model: '{% if document.ai_model_used %}{{ document.ai_model_used }}{% else %}Not recorded (processed before AI tracking){% endif %}',
        ai_host: '{{ config.ollama_host if config.ollama_host else "Not configured" }}',
        // User feedback data (flattened for PDF generator)
        user_code_review_status: documentData.userFeedback.codeReviewStatus,
        user_code_review_comments: documentData.userFeedback.codeReviewComments,
        user_code_review_reviewer: documentData.userFeedback.codeReviewReviewer,
        user_documentation_rating: documentData.userFeedback.documentationRating,
        user_documentation_comments: documentData.userFeedback.documentationComments,
        user_documentation_updated_by: documentData.userFeedback.documentationUpdatedBy,
        user_documentation_input: documentData.userFeedback.documentationInput,
        user_documentation_suggestions: documentData.userFeedback.documentationSuggestions,
        user_documentation_input_by: documentData.userFeedback.documentationInputBy,
        // AI recommendation data
        code_review_recommended: documentData.userFeedback.codeReviewRecommended,
        code_review_priority: documentData.userFeedback.codeReviewPriority
        {% if document.processed_time %}
        ,processing_time: '{{ document.processed_time.strftime("%Y-%m-%d %H:%M:%S UTC") }}'
        {% endif %}
    };

    // Add diff content if available - use raw diff content for PDF generation
    let diffContent = '';
    if (rawDiffContent && rawDiffContent.trim()) {
        // Use the raw diff content directly from server (same as markdown downloads)
        diffContent = rawDiffContent.trim();
    } else {
        // Fallback to DOM extraction if raw content not available
        let diffElement = document.getElementById('rawDiffContent');
        if (!diffElement || !diffElement.textContent.trim()) {
            diffElement = document.getElementById('diffContentCode');
        }
        if (diffElement && diffElement.textContent.trim()) {
            diffContent = diffElement.textContent.trim();
        }
    }

    // Send to server for PDF generation
    const apiUrl = '/api/documents/' + documentData.id + '/download/pdf';
    console.log('PDF API URL:', apiUrl);
    console.log('PDF request data:', {
        content: content.substring(0, 100) + '...',
        diff_content: diffContent.substring(0, 100) + '...',
        document_info: documentInfo
    });

    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content: content,
            diff_content: diffContent,
            document_info: documentInfo
        })
    })
    .then(response => {
        console.log('PDF API response status:', response.status);
        console.log('PDF API response headers:', response.headers);
        if (!response.ok) {
            return response.text().then(text => {
                console.error('PDF API error response:', text);
                throw new Error('PDF generation failed: ' + response.status + ' - ' + text);
            });
        }
        return response.blob();
    })
    .then(blob => {
        // Download the PDF
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = documentData.displayName.replace(/[^a-zA-Z0-9-_]/g, '_') + '.pdf';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    })
    .catch(error => {
        console.error('PDF download failed:', error);
        alert('Failed to generate PDF. Please try again or download as Markdown.');
    })
    .finally(() => {
        // Restore buttons
        originalButtons.forEach(btn => {
            btn.innerHTML = '<i class="fas fa-download"></i> Download';
            btn.disabled = false;
        });
    });
}

// User Feedback Functions
function updateCodeReviewFeedback() {
    const status = document.getElementById('codeReviewStatus').value;
    const comments = document.getElementById('codeReviewComments').value;
    const reviewer = document.getElementById('codeReviewReviewer').value;

    if (!status) {
        alert('Please select a review status');
        return;
    }

    const data = {
        status: status,
        comments: comments || null,
        reviewer: reviewer || null
    };

    fetch(`/api/documents/{{ document.id }}/feedback/code-review`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Code review feedback saved successfully!');
            location.reload(); // Refresh to show updated timestamp
        } else {
            alert('Error: ' + (data.error || 'Failed to save feedback'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving code review feedback');
    });
}

function updateDocumentationFeedback() {
    const rating = document.getElementById('documentationRating').value;
    const comments = document.getElementById('documentationComments').value;
    const updatedBy = document.getElementById('documentationUpdatedBy').value;

    const data = {
        rating: rating ? parseInt(rating) : null,
        comments: comments || null,
        updated_by: updatedBy || null
    };

    fetch(`/api/documents/{{ document.id }}/feedback/documentation`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Documentation feedback saved successfully!');
            location.reload(); // Refresh to show updated timestamp
        } else {
            alert('Error: ' + (data.error || 'Failed to save feedback'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving documentation feedback');
    });
}

function updateRiskAssessmentFeedback() {
    const riskOverride = document.getElementById('riskAssessmentOverride').value;
    const comments = document.getElementById('riskAssessmentComments').value;
    const updatedBy = document.getElementById('riskAssessmentUpdatedBy').value;

    if (!riskOverride) {
        alert('Please select a risk assessment');
        return;
    }

    const data = {
        risk_override: riskOverride,
        comments: comments || null,
        updated_by: updatedBy || null
    };

    fetch(`/api/documents/{{ document.id }}/feedback/risk-assessment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Risk assessment saved successfully!');
            location.reload(); // Refresh to show updated timestamp
        } else {
            alert('Error: ' + (data.error || 'Failed to save assessment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving risk assessment');
    });
}

function updateDocumentationInput() {
    const documentationInput = document.getElementById('documentationInput').value;
    const suggestions = document.getElementById('documentationSuggestions').value;
    const inputBy = document.getElementById('documentationInputBy').value;

    if (!documentationInput && !suggestions) {
        alert('Please provide either additional documentation or suggestions');
        return;
    }

    const data = {
        documentation_input: documentationInput || null,
        suggestions: suggestions || null,
        input_by: inputBy || null
    };

    fetch(`/api/documents/{{ document.id }}/feedback/documentation-input`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Documentation input saved successfully!');
            location.reload(); // Refresh to show updated timestamp
        } else {
            alert('Error: ' + (data.error || 'Failed to save documentation input'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving documentation input');
    });
}

function loadAISuggestions() {
    const btn = document.getElementById('loadAISuggestionsBtn');
    const originalHTML = btn.innerHTML;

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading AI Suggestions...';
    btn.disabled = true;

    // Make API call to get AI suggestions
    fetch(`/api/documents/{{ document.id }}/ai-suggestions`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.suggestions) {
                const currentContent = document.getElementById('documentationInput').value;

                // If there's existing content, ask for confirmation
                if (currentContent.trim() && !confirm('This will replace the current content with AI suggestions. Continue?')) {
                    return;
                }

                // Load the AI suggestions
                document.getElementById('documentationInput').value = data.suggestions;

                // Show success status
                const statusDiv = document.getElementById('ai-suggestions-status');
                statusDiv.style.display = 'block';

                // Update button to show success
                btn.innerHTML = '<i class="fas fa-check"></i> AI Suggestions Loaded';
                btn.classList.remove('btn-outline-info');
                btn.classList.add('btn-success');

                // Reset button after 3 seconds
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-info');
                    btn.disabled = false;
                }, 3000);

            } else {
                alert('No AI suggestions available for this document: ' + (data.error || 'Unknown error'));
                btn.innerHTML = originalHTML;
                btn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error loading AI suggestions:', error);
            alert('Failed to load AI suggestions. Please try again.');
            btn.innerHTML = originalHTML;
            btn.disabled = false;
        });
}

function deleteDocument() {
    new bootstrap.Modal(document.getElementById('deleteDocumentModal')).show();
}

function confirmDelete() {
    fetch('/api/documents/{{ document.id }}/delete', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ url_for("documents_page") }}';
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the document.');
    });
}

// Toggle showing all changed files
function toggleAllFiles() {
    const remainingFiles = document.getElementById('remaining-files');
    const toggleBtn = document.getElementById('toggle-files-btn');

    if (remainingFiles.style.display === 'none') {
        remainingFiles.style.display = 'block';
        toggleBtn.innerHTML = '<small>Show less</small>';
    } else {
        remainingFiles.style.display = 'none';
        toggleBtn.innerHTML = '<small>Show all</small>';
    }
}
</script>
{% endblock %}
