#!/usr/bin/env python3
"""
Simple test to verify the repository page organization is working
"""

import sys
import os
import requests
sys.path.append('/app')

def test_simple_organization():
    """Simple test of the repository page organization"""
    print("🔍 Simple Repository Organization Test")
    print("=" * 45)
    
    try:
        # Test repositories page access
        response = requests.get('http://localhost:5000/repositories', timeout=10)
        
        if response.status_code == 200:
            print("✅ Repository page accessible")
            print(f"   Page size: {len(response.text)} characters")
            
            # Check for key elements in the HTML
            html_content = response.text
            
            checks = {
                'Repository Filters & Management': 'Repository Filters & Management' in html_content,
                'Search & Filters': 'Search & Filters' in html_content,
                'Display Options': 'Display Options' in html_content,
                'Management Actions': 'Management Actions' in html_content,
                'bg-light styling': 'bg-light' in html_content,
                'Search input': 'id="search"' in html_content,
                'Status filter': 'id="status"' in html_content,
                'Sort by': 'id="sort_by"' in html_content,
                'View mode': 'id="view_mode"' in html_content,
                'Bulk Actions button': 'Bulk Actions' in html_content,
                'Discover button': 'Discover' in html_content,
                'Add Repository button': 'Add Repository' in html_content
            }
            
            print(f"\n📋 Content Checks:")
            passed = 0
            total = len(checks)
            
            for check_name, result in checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
                if result:
                    passed += 1
            
            print(f"\n📊 Results: {passed}/{total} checks passed ({passed/total*100:.1f}%)")
            
            if passed >= total * 0.9:
                print("🎉 Excellent! Organization is working properly")
            elif passed >= total * 0.7:
                print("✅ Good! Most organization elements are present")
            else:
                print("⚠️  Some organization elements may be missing")
            
            # Check for specific HTML structure improvements
            structure_checks = {
                'Grouped sections': html_content.count('border rounded p-3 bg-light') >= 3,
                'Icon usage': 'fas fa-search' in html_content and 'fas fa-cog' in html_content and 'fas fa-tools' in html_content,
                'Responsive columns': 'col-md-' in html_content,
                'Button groups': 'btn-group' in html_content,
                'Form structure': 'filtersForm' in html_content
            }
            
            print(f"\n🏗️  Structure Checks:")
            for check_name, result in structure_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
            
        else:
            print(f"❌ Repository page not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing repository organization: {e}")

def test_page_functionality():
    """Test that the page functionality still works after reorganization"""
    print(f"\n⚙️  Testing Page Functionality")
    print("=" * 35)
    
    try:
        # Test with query parameters to ensure filters still work
        test_urls = [
            'http://localhost:5000/repositories',
            'http://localhost:5000/repositories?search=test',
            'http://localhost:5000/repositories?status=enabled',
            'http://localhost:5000/repositories?sort_by=name&sort_order=asc'
        ]
        
        for url in test_urls:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url.split('?')[1] if '?' in url else 'Base page'} - Working")
            else:
                print(f"❌ {url.split('?')[1] if '?' in url else 'Base page'} - Failed ({response.status_code})")
                
    except Exception as e:
        print(f"❌ Error testing page functionality: {e}")

if __name__ == "__main__":
    test_simple_organization()
    test_page_functionality()
    
    print(f"\n🎯 Organization Summary:")
    print(f"   📋 The Repository Filters & Management section has been reorganized into:")
    print(f"      🔍 Search & Filters - Search box and filter dropdowns")
    print(f"      ⚙️  Display Options - Sorting, view mode, and filter actions")
    print(f"      🛠️  Management Actions - Repository management buttons")
    print(f"   🎨 Visual improvements include:")
    print(f"      • Clear section headers with icons")
    print(f"      • Consistent background styling")
    print(f"      • Better spacing and alignment")
    print(f"      • Responsive design for all screen sizes")
    print(f"\n🚀 The interface is now more organized and user-friendly!")
