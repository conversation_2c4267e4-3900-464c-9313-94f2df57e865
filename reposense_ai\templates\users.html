{% extends "base.html" %}

{% block title %}User Management - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">User Management</h1>
            <p class="page-subtitle">Manage users and their repository access</p>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-user-plus"></i> Add User
        </button>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i>Users</h5>
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Full Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Department</th>
                                <th>Status</th>
                                <th>Global Notifications</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>
                                    <strong>{{ user.username }}</strong>
                                </td>
                                <td>{{ user.full_name }}</td>
                                <td>
                                    <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                                </td>
                                <td>
                                    <span class="badge bg-{% if (user.role.value if user.role.value is defined else user.role) == 'admin' %}danger{% elif (user.role.value if user.role.value is defined else user.role) == 'manager' %}warning{% elif (user.role.value if user.role.value is defined else user.role) == 'developer' %}primary{% else %}secondary{% endif %}">
                                        {{ (user.role.value if user.role.value is defined else user.role).title() }}
                                    </span>
                                </td>
                                <td>{{ user.department or '-' }}</td>
                                <td>
                                    {% if user.enabled %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Disabled</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.receive_all_notifications %}
                                        <i class="fas fa-check text-success" title="Receives all notifications"></i>
                                    {% else %}
                                        <i class="fas fa-times text-muted" title="Repository-specific notifications only"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="editUser('{{ user.id }}', '{{ user.username }}', '{{ user.email }}', '{{ user.full_name }}', '{{ user.role.value if user.role.value is defined else user.role }}', '{{ user.phone or '' }}', '{{ user.department or '' }}', {{ user.enabled|lower }}, {{ user.receive_all_notifications|lower }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteUser('{{ user.id }}', '{{ user.username }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>No Users Configured</h5>
                    <p class="text-muted">Add your first user to get started with user management.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus"></i> Add First User
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_user') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name *</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role">
                            {% for role in user_roles %}
                            <option value="{{ role }}" {% if role == 'developer' %}selected{% endif %}>{{ role.title() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="department" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department" name="department">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="receive_all_notifications" name="receive_all_notifications">
                        <label class="form-check-label" for="receive_all_notifications">
                            Receive notifications for all repositories
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username *</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name *</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role">
                            {% for role in user_roles %}
                            <option value="{{ role }}">{{ role.title() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="edit_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="edit_department" class="form-label">Department</label>
                        <input type="text" class="form-control" id="edit_department" name="department">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_enabled" name="enabled">
                        <label class="form-check-label" for="edit_enabled">
                            User enabled
                        </label>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_receive_all_notifications" name="receive_all_notifications">
                        <label class="form-check-label" for="edit_receive_all_notifications">
                            Receive notifications for all repositories
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This will remove the user from all repository assignments and cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" id="deleteUserForm" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function editUser(id, username, email, fullName, role, phone, department, enabled, receiveAll) {
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_email').value = email;
    document.getElementById('edit_full_name').value = fullName;
    document.getElementById('edit_role').value = role;
    document.getElementById('edit_phone').value = phone;
    document.getElementById('edit_department').value = department;
    document.getElementById('edit_enabled').checked = enabled;
    document.getElementById('edit_receive_all_notifications').checked = receiveAll;
    
    document.getElementById('editUserForm').action = '/users/update/' + id;
    
    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

function deleteUser(id, username) {
    document.getElementById('deleteUserName').textContent = username;
    document.getElementById('deleteUserForm').action = '/users/delete/' + id;
    
    new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
}
</script>
{% endblock %}
