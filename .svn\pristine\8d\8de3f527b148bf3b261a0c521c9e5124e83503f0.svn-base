services:
  reposense-ai:
    build:
      context: ./reposense_ai
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5001:5000"
    volumes:
      # Essential volumes - always mounted for data persistence
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
      # Development volumes - mount source code for hot reloads (excluding data)
      - ./reposense_ai:/app
      # Exclude data directory to prevent override of persistent data
      - /app/data
      # Exclude other build artifacts to avoid conflicts
      - /app/node_modules
      - /app/.git
    environment:
      # Web interface settings (minimal environment)
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      # Development settings (override via .env file)
      - REPOSENSE_AI_LOG_LEVEL=${REPOSENSE_AI_LOG_LEVEL:-INFO}
      - REPOSENSE_AI_DB_DEBUG=${REPOSENSE_AI_DB_DEBUG:-false}
      # Standalone mode - use external Ollama service
      - OLLAMA_BASE_URL=http://************:11434
      # Optional deployment overrides (uncomment if needed):
      # - OLLAMA_MODEL=qwen3
    networks:
      - reposense-ai-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "sundc:***********"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Network for standalone operation
networks:
  reposense-ai-network:
    driver: bridge
