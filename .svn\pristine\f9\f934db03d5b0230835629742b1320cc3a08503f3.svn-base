#!/usr/bin/env python3
"""
Ollama client for AI-powered content generation
Handles documentation and email content generation using Ollama API
"""

import logging
import requests
from typing import Tuple, Optional

from models import Config, CommitInfo


class OllamaClient:
    """Client for Ollama API interactions"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def call_ollama(self, prompt: str, system_prompt: str = "", model: Optional[str] = None) -> str:
        """Call Ollama API to generate content with optional model override"""
        try:
            # Use specified model or fall back to default
            selected_model = model or self.config.ollama_model

            # Check if model is available first
            if not self.is_model_available(selected_model):
                available_models = self.get_available_models()
                if available_models:
                    self.logger.warning(f"Model '{selected_model}' not available. Available models: {available_models}")
                    return f"Error: Model '{selected_model}' not available on server. Available models: {', '.join(available_models)}"
                else:
                    self.logger.error(f"No models available on Ollama server {self.config.ollama_host}")
                    return f"Error: No models available on Ollama server {self.config.ollama_host}"

            url = f"{self.config.ollama_host}/api/generate"

            payload = {
                "model": selected_model,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False,
                "options": self._get_optimized_parameters(prompt, system_prompt)
            }

            # Log model selection for visibility
            if model and model != self.config.ollama_model:
                self.logger.info(f"🎯 Using specialized model: {selected_model} (requested: {model}, default: {self.config.ollama_model})")
            else:
                self.logger.debug(f"Using default model: {selected_model}")

            self.logger.debug(f"Calling Ollama API at {url} with model {selected_model}")

            # Adjust timeout based on model size and complexity
            # Large models (20B+) need more time, especially for complex prompts
            timeout = self._get_timeout_for_model(selected_model)
            self.logger.debug(f"Using timeout of {timeout} seconds for model {selected_model}")

            response = requests.post(url, json=payload, timeout=timeout)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "")
            
        except requests.exceptions.Timeout as e:
            error_msg = f"Error: Ollama API timeout after {timeout} seconds with model '{selected_model}'. The model may be too large or the server may be overloaded. Try using a smaller/faster model or increasing timeout settings."
            self.logger.error(error_msg)
            return error_msg
        except requests.exceptions.ConnectionError as e:
            error_msg = f"Error: Cannot connect to Ollama server at {self.config.ollama_host}. Please verify that Ollama is running and accessible."
            self.logger.error(error_msg)
            return error_msg
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                error_msg = f"Error: Model '{selected_model}' not found on Ollama server. Please check model name and availability."
            elif e.response.status_code == 500:
                error_msg = f"Error: Ollama server internal error with model '{selected_model}'. The model may be corrupted or incompatible."
            else:
                error_msg = f"Error: Ollama API HTTP error {e.response.status_code} with model '{selected_model}': {e}"
            self.logger.error(error_msg)
            return error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"Error: Ollama API request failed with model '{selected_model}': {e}"
            self.logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"Error: Unexpected error calling Ollama with model '{selected_model}': {e}"
            self.logger.error(error_msg)
            return error_msg

    def _get_optimized_parameters(self, prompt: str, system_prompt: str) -> dict:
        """Get optimized LLM parameters based on the task type"""
        combined_text = (prompt + system_prompt).lower()

        # Detect different task types based on content analysis
        is_risk_assessment = any(keyword in combined_text for keyword in [
            'risk_level', 'risk assessment', 'critical', 'security', 'vulnerability',
            'priority', 'confidence', 'reasoning'
        ])

        is_documentation_generation = any(keyword in combined_text for keyword in [
            'generate documentation', 'technical documentation', 'markdown format',
            'comprehensive documentation', 'code changes', 'commit documentation'
        ])

        is_email_generation = any(keyword in combined_text for keyword in [
            'email subject', 'email body', 'email notification', 'professional email',
            'stakeholders', 'business impact'
        ])

        is_structured_output = any(keyword in combined_text for keyword in [
            'json format', 'respond in json', '{"', 'structure:', 'exactly:', 'must be:'
        ])

        is_content_scoring = any(keyword in combined_text for keyword in [
            'rate the relevance', 'score', 'relevance', 'batch scoring', 'section'
        ])

        is_documentation_suggestions = any(keyword in combined_text for keyword in [
            'documentation suggestions', 'improve documentation', 'documentation input',
            'user documentation', 'suggestions for'
        ])

        # Apply task-specific parameters
        if is_risk_assessment or is_structured_output:
            # Conservative parameters for consistent, structured analysis
            return {
                "temperature": 0.1,      # Very low for consistency and accuracy
                "top_p": 0.8,           # Focused sampling for reliability
                "top_k": 20,            # Limited vocabulary for consistency
                "repeat_penalty": 1.1,  # Slight penalty to avoid repetition
                "num_predict": 2048,    # Reasonable response length for analysis
            }
        elif is_documentation_generation:
            # Balanced parameters for comprehensive, readable documentation
            return {
                "temperature": 0.4,      # Low-medium for consistency with some creativity
                "top_p": 0.9,           # Good sampling for varied expression
                "top_k": 50,            # Broader vocabulary for technical writing
                "repeat_penalty": 1.05, # Light penalty for natural flow
                "num_predict": 4096,    # Longer responses for detailed documentation
            }
        elif is_email_generation:
            # Parameters for professional, concise communication
            return {
                "temperature": 0.3,      # Low for professional consistency
                "top_p": 0.85,          # Focused but not too narrow
                "top_k": 40,            # Good vocabulary for business communication
                "repeat_penalty": 1.1,  # Standard penalty
                "num_predict": 1024,    # Shorter responses for concise emails
            }
        elif is_content_scoring:
            # Very conservative for consistent scoring
            return {
                "temperature": 0.05,     # Extremely low for consistent scoring
                "top_p": 0.7,           # Very focused sampling
                "top_k": 15,            # Limited vocabulary for scoring consistency
                "repeat_penalty": 1.0,  # No penalty for scoring tasks
                "num_predict": 512,     # Short responses for scores
            }
        elif is_documentation_suggestions:
            # Creative but focused parameters for helpful suggestions
            return {
                "temperature": 0.6,      # Medium creativity for useful suggestions
                "top_p": 0.9,           # Good sampling diversity
                "top_k": 60,            # Broader vocabulary for suggestions
                "repeat_penalty": 1.05, # Light penalty
                "num_predict": 2048,    # Good length for suggestions
            }
        else:
            # Default balanced parameters for general content generation
            return {
                "temperature": 0.5,      # Balanced creativity/consistency
                "top_p": 0.9,           # Good sampling diversity
                "top_k": 40,            # Standard vocabulary
                "repeat_penalty": 1.1,  # Standard repetition penalty
                "num_predict": 3072,    # Good length for general content
            }

    def test_connection(self, timeout: Optional[int] = None) -> bool:
        """Test connection to Ollama server (not model-specific)"""
        try:
            if timeout is None:
                timeout = getattr(self.config, 'ollama_timeout_connection', 30)

            url = f"{self.config.ollama_host}/api/tags"
            self.logger.debug(f"Testing Ollama connection to {url} with timeout {timeout}s")
            response = requests.get(url, timeout=timeout)
            success = response.status_code == 200
            self.logger.debug(f"Ollama connection test result: {success} (status: {response.status_code})")
            return success
        except Exception as e:
            self.logger.debug(f"Ollama connection test failed: {e}")
            return False

    def get_available_models(self, timeout: Optional[int] = None) -> list:
        """Get list of available models from Ollama server"""
        try:
            if timeout is None:
                timeout = getattr(self.config, 'ollama_timeout_connection', 30)

            url = f"{self.config.ollama_host}/api/tags"
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()

            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            self.logger.debug(f"Available models: {models}")
            return models
        except Exception as e:
            self.logger.debug(f"Failed to get available models: {e}")
            return []

    def is_model_available(self, model_name: str | None = None) -> bool:
        """Check if a specific model is available"""
        if model_name is None:
            model_name = self.config.ollama_model

        available_models = self.get_available_models()
        return model_name in available_models

    def _get_timeout_for_model(self, model_name: str) -> int:
        """Get appropriate timeout based on model size and type"""
        # Get base timeout from config, with fallback
        base_timeout = getattr(self.config, 'ollama_timeout_base', 180)

        # Adjust based on model characteristics
        model_lower = model_name.lower()

        # Very large models (20B+)
        if any(size in model_lower for size in ['20b', '33b', '70b', '180b']):
            return 300  # 5 minutes

        # Large models (7B-13B)
        elif any(size in model_lower for size in ['7b', '8b', '12b', '13b', '14b']):
            return 240  # 4 minutes

        # Medium models (3B-6B)
        elif any(size in model_lower for size in ['3b', '4b', '6b']):
            return 180  # 3 minutes

        # Small models (1B-2B)
        elif any(size in model_lower for size in ['1b', '2b', '1.7b']):
            return 120  # 2 minutes

        # Very small models (under 1B)
        elif any(size in model_lower for size in ['135m', '350m', '500m']):
            return 60   # 1 minute

        # Special cases for known slow models
        if 'deepseek' in model_lower or 'codestral' in model_lower:
            return base_timeout + 60  # Add extra time for complex models

        # Default for unknown models
        return base_timeout


    
    def generate_documentation(self, commit: CommitInfo) -> str:
        """Generate documentation for a commit using Ollama"""
        system_prompt = """You are a technical documentation generator and code review advisor. Your task is to create clear,
        comprehensive documentation based on code changes and provide development process feedback. Focus on:
        1. What changes were made
        2. Why the changes were made (based on commit message)
        3. Impact on the codebase
        4. Any important technical details
        5. Code review recommendations
        6. Documentation impact assessment

        Write in markdown format with appropriate headers and formatting."""
        
        prompt = f"""
        Generate comprehensive documentation and development process feedback for the following commit:

        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}

        Changed files:
        {chr(10).join(commit.changed_paths)}

        Diff:
        {commit.diff}

        Please provide your analysis in the following format:

        ## Summary
        [Brief summary of changes]

        ## Technical Details
        [Detailed technical analysis]

        ## Impact Assessment
        [Impact on codebase, users, and system functionality]

        ## Code Review Recommendation
        [Should this commit be code reviewed? Why or why not? Consider factors like:
        - Complexity of changes
        - Risk level (high/medium/low)
        - Areas affected (UI, backend, configuration, etc.)
        - Potential for introducing bugs
        - Security implications

        IMPORTANT: Start this section with a clear decision:
        - If review is needed: "Yes, this commit should undergo a code review..."
        - If review is not needed: "No, this commit does not require a code review..."
        Then provide your detailed reasoning.]

        ## Documentation Impact
        [Does this commit affect documentation? Consider:
        - Are user-facing features changed?
        - Are APIs or interfaces modified?
        - Are configuration options added/changed?
        - Are deployment procedures affected?
        - Should README, setup guides, or other docs be updated?

        IMPORTANT: Start this section with a clear decision:
        - If documentation updates are needed: "Yes, documentation updates are needed..."
        - If no updates are needed: "No, documentation updates are not required..."
        Then provide your detailed reasoning.]

        ## Recommendations
        [Any additional recommendations for follow-up actions]

        ## Heuristic Analysis
        [Include automated heuristic analysis that provides context indicators and preliminary assessments to help understand the AI's decision-making process]
        """

        try:
            self.logger.info(f"Generating documentation for revision {commit.revision}")

            # Validate input data
            if not commit:
                error_msg = "Error: Cannot generate documentation - commit information is missing"
                self.logger.error(error_msg)
                return error_msg

            if not commit.revision:
                error_msg = f"Error: Cannot generate documentation - revision number is missing for commit by {commit.author or 'unknown author'}"
                self.logger.error(error_msg)
                return error_msg

            if not commit.changed_paths:
                error_msg = f"Error: Cannot generate documentation for revision {commit.revision} - no changed files detected. This may indicate a repository access issue or an empty commit."
                self.logger.warning(error_msg)
                return error_msg

            # Test Ollama connection before attempting generation
            if not self.test_connection():
                error_msg = f"Error: Cannot generate documentation for revision {commit.revision} - Ollama service is not available. Please check that Ollama is running and accessible at {self.config.ollama_host}."
                self.logger.error(error_msg)
                return error_msg

            # Generate the main documentation using specialized model if configured
            doc_model = getattr(self.config, 'ollama_model_documentation', None)
            if doc_model:
                self.logger.info(f"🎯 Using specialized documentation model: {doc_model}")
            else:
                self.logger.debug(f"No specialized documentation model configured, using default: {self.config.ollama_model}")

            documentation = self.call_ollama(prompt, system_prompt, model=doc_model)

            # Check if generation was successful
            if not documentation:
                error_msg = f"Error: Documentation generation failed for revision {commit.revision} - Ollama returned empty response. This may indicate a model issue, context limit exceeded, or service overload."
                self.logger.error(error_msg)
                return error_msg

            if documentation.strip().startswith("Error:") or "error" in documentation.lower()[:100]:
                error_msg = f"Error: Documentation generation failed for revision {commit.revision} - AI model returned error: {documentation[:200]}..."
                self.logger.error(error_msg)
                return error_msg

            if len(documentation.strip()) < 50:
                error_msg = f"Error: Documentation generation failed for revision {commit.revision} - AI response too short ({len(documentation)} chars). This may indicate a model issue or insufficient context."
                self.logger.warning(error_msg)
                return error_msg

            # Fix any malformed tables in the generated documentation
            try:
                documentation = self._fix_ai_generated_tables(documentation)
            except Exception as e:
                self.logger.warning(f"Table fixing failed for revision {commit.revision}: {e}")
                # Continue with unfixed tables rather than failing completely

            # Add heuristic analysis section
            try:
                documentation_with_heuristics = self._add_heuristic_analysis_section(documentation, commit)
                return documentation_with_heuristics
            except Exception as e:
                self.logger.warning(f"Heuristic analysis addition failed for revision {commit.revision}: {e}")
                # Return documentation without heuristics rather than failing completely
                return documentation

        except Exception as e:
            error_msg = f"Error: Critical failure generating documentation for revision {commit.revision if commit else 'unknown'} - {str(e)}. Please check Ollama service status and model availability."
            self.logger.error(error_msg)
            return error_msg

    def _add_heuristic_analysis_section(self, documentation: str, commit: CommitInfo) -> str:
        """Add heuristic analysis section to the generated documentation"""
        try:
            # Import here to avoid circular imports
            from metadata_extractor import MetadataExtractor
            from config_manager import ConfigManager

            # Initialize metadata extractor for heuristic analysis
            config_manager = ConfigManager() if hasattr(self, 'config') else None
            extractor = MetadataExtractor(ollama_client=self, config_manager=config_manager)

            # Create a temporary document record for heuristic analysis using the proper DocumentRecord class
            from document_database import DocumentRecord
            from datetime import datetime

            temp_doc_record = DocumentRecord(
                id=f"temp_{commit.revision}",
                repository_id=getattr(commit, 'repository_id', 'unknown_repo'),
                repository_name=getattr(commit, 'repository_id', 'unknown_repo'),  # Use repository_id as name fallback
                revision=int(commit.revision),
                date=getattr(commit, 'date', datetime.now()),
                filename=commit.changed_paths[0] if commit.changed_paths else "unknown",
                filepath=f"/tmp/temp_{commit.revision}.md",  # Temporary path
                size=len(documentation),
                author=getattr(commit, 'author', 'unknown'),
                commit_message=commit.message,
                changed_paths=commit.changed_paths,
                repository_type='svn'  # Default assumption
            )

            # Gather heuristic context
            heuristic_context = extractor._gather_heuristic_context(documentation, temp_doc_record)

            # Build heuristic analysis section
            heuristic_section = self._format_heuristic_analysis_section(heuristic_context, commit)

            # Check if heuristic analysis already exists (prevent duplication)
            if "## Heuristic Analysis" in documentation:
                self.logger.debug("Heuristic Analysis section already exists, skipping addition")
                return documentation

            # Insert the heuristic analysis section before the Recommendations section
            if "## Recommendations" in documentation:
                parts = documentation.split("## Recommendations")
                if len(parts) == 2:
                    # Ensure proper spacing
                    before_part = parts[0].rstrip()
                    after_part = parts[1]
                    return f"{before_part}\n\n{heuristic_section}\n\n## Recommendations{after_part}"

            # If no Recommendations section, append at the end with proper spacing
            documentation = documentation.rstrip()
            return f"{documentation}\n\n{heuristic_section}"

        except Exception as e:
            self.logger.warning(f"Failed to add heuristic analysis section: {e}")
            return documentation

    def _format_heuristic_analysis_section(self, heuristic_context: dict, commit: CommitInfo) -> str:
        """Format the heuristic analysis into a readable section"""
        section_parts = ["## Heuristic Analysis"]
        section_parts.append("")
        section_parts.append("*This section shows the automated heuristic analysis that provides context indicators and preliminary assessments to help understand the AI's decision-making process.*")
        section_parts.append("")

        # Add context indicators
        indicators = heuristic_context.get('indicators', {})
        if indicators:
            section_parts.append("### Context Indicators")
            section_parts.append("")

            # Complexity assessment
            complexity = indicators.get('complexity', 'Unknown')
            section_parts.append(f"- **Complexity Assessment:** {complexity}")

            # Risk indicators
            risk_keywords = indicators.get('risk_keywords', [])
            if risk_keywords:
                section_parts.append(f"- **Risk Keywords Detected:** {', '.join(risk_keywords)}")
                section_parts.append(f"- **Risk Assessment:** {indicators.get('risk_assessment', 'Unknown')}")
            else:
                section_parts.append("- **Risk Assessment:** LOW - no high-risk keywords detected")

            # Documentation indicators
            doc_keywords = indicators.get('doc_keywords', [])
            if doc_keywords:
                section_parts.append(f"- **Documentation Keywords Detected:** {', '.join(doc_keywords)}")
                section_parts.append(f"- **Documentation Assessment:** {indicators.get('doc_assessment', 'Unknown')}")
            else:
                section_parts.append("- **Documentation Assessment:** UNLIKELY - no documentation-related keywords detected")

            # File type analysis
            file_type = indicators.get('file_type', 'Unknown')
            section_parts.append(f"- **File Type Analysis:** {file_type}")

            section_parts.append("")

        # Add preliminary decisions
        decisions = heuristic_context.get('decisions', {})
        if decisions:
            section_parts.append("### Preliminary Heuristic Decisions")
            section_parts.append("")

            code_review = decisions.get('code_review_recommended')
            if code_review is not None:
                review_text = "✅ Recommended" if code_review else "❌ Not Required"
                section_parts.append(f"- **Code Review:** {review_text}")

                priority = decisions.get('code_review_priority')
                if priority:
                    section_parts.append(f"- **Review Priority:** {priority}")

            doc_impact = decisions.get('documentation_impact')
            if doc_impact is not None:
                impact_text = "📝 Updates Needed" if doc_impact else "✅ No Updates Required"
                section_parts.append(f"- **Documentation Impact:** {impact_text}")

            risk_level = decisions.get('risk_level')
            if risk_level:
                risk_icon = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}.get(risk_level, '⚪')
                section_parts.append(f"- **Risk Level:** {risk_icon} {risk_level}")

            section_parts.append("")

        # Add reasoning
        reasoning = heuristic_context.get('reasoning', [])
        if reasoning:
            section_parts.append("### Heuristic Reasoning")
            section_parts.append("")
            for reason in reasoning:
                section_parts.append(f"- {reason}")
            section_parts.append("")

        # Add metadata about the analysis
        section_parts.append("### Analysis Metadata")
        section_parts.append("")
        section_parts.append(f"- **Files Analyzed:** {len(commit.changed_paths)} file(s)")
        if commit.changed_paths:
            section_parts.append(f"- **Primary File:** {commit.changed_paths[0]}")
        section_parts.append(f"- **Commit Message Length:** {len(commit.message)} characters")
        section_parts.append(f"- **Diff Size:** {len(commit.diff)} characters")

        return "\n".join(section_parts)

    def _fix_ai_generated_tables(self, content: str) -> str:
        """Fix malformed markdown tables generated by AI"""
        if not content:
            return content

        lines = content.split('\n')
        fixed_lines: list[str] = []
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Check if this line looks like a table row (contains | and has at least 2 cells)
            if '|' in line and len([cell for cell in line.split('|') if cell.strip()]) >= 2:
                # Found potential table start
                table_lines = []
                j = i

                # Collect all consecutive lines that look like table rows
                while j < len(lines):
                    current_line = lines[j].strip()
                    if '|' in current_line and len([cell for cell in current_line.split('|') if cell.strip()]) >= 2:
                        table_lines.append(current_line)
                        j += 1
                    elif current_line == '':
                        # Empty line - might be end of table
                        j += 1
                        break
                    else:
                        # Non-table line
                        break

                if table_lines:
                    # Ensure there's a blank line before the table (but not if previous line is a header)
                    if fixed_lines and fixed_lines[-1].strip() and not fixed_lines[-1].strip().startswith('#'):
                        fixed_lines.append('')

                    # Process and fix table structure
                    processed_table = self._process_ai_table_lines(table_lines)
                    fixed_lines.extend(processed_table)

                    # Ensure there's a blank line after the table
                    fixed_lines.append('')
                    i = j
                else:
                    fixed_lines.append(lines[i])
                    i += 1
            else:
                fixed_lines.append(lines[i])
                i += 1

        return '\n'.join(fixed_lines)

    def _process_ai_table_lines(self, table_lines: list) -> list:
        """Process AI-generated table lines to ensure proper markdown table format"""
        if not table_lines:
            return []

        processed_lines = []

        # Process first line as header
        header_line = table_lines[0]
        header_cells = [cell.strip() for cell in header_line.split('|') if cell.strip()]

        # Ensure proper header format
        formatted_header = '| ' + ' | '.join(header_cells) + ' |'
        processed_lines.append(formatted_header)

        # Add separator line
        separator = '| ' + ' | '.join(['-------'] * len(header_cells)) + ' |'
        processed_lines.append(separator)

        # Process remaining lines as data rows
        for i in range(1, len(table_lines)):
            line = table_lines[i]

            # Skip lines that look like separators
            if '-' in line and all(cell.strip() == '' or cell.strip().replace('-', '') == ''
                                 for cell in line.split('|')):
                continue

            # Process data row
            cells = [cell.strip() for cell in line.split('|') if cell.strip()]

            # Pad cells to match header count
            while len(cells) < len(header_cells):
                cells.append('')

            # Truncate if too many cells
            if len(cells) > len(header_cells):
                cells = cells[:len(header_cells)]

            formatted_row = '| ' + ' | '.join(cells) + ' |'
            processed_lines.append(formatted_row)

        return processed_lines

    def generate_email_content(self, commit: CommitInfo) -> Tuple[str, str]:
        """Generate email subject and body for a commit using Ollama"""
        system_prompt = """You are an email generator for code commit notifications. Create:
        1. A clear, concise subject line
        2. A professional email body that summarizes the changes
        
        The email should be informative but not overly technical. Focus on business impact 
        and key changes that stakeholders should know about."""
        
        prompt = f"""
        Generate an email notification for the following repository commit:
        
        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}
        
        Changed files:
        {chr(10).join(commit.changed_paths)}
        
        Diff (summary):
        {commit.diff[:2000]}{'...' if len(commit.diff) > 2000 else ''}
        
        Please generate:
        1. EMAIL SUBJECT: [subject line]
        2. EMAIL BODY: [email content]
        
        Keep the subject line under 60 characters and the email body professional and concise.
        """
        
        self.logger.info(f"Generating email content for revision {commit.revision}")

        # Use specialized documentation model for email generation if configured
        doc_model = getattr(self.config, 'ollama_model_documentation', None)
        if doc_model:
            self.logger.info(f"🎯 Using specialized documentation model for email: {doc_model}")

        response = self.call_ollama(prompt, system_prompt, model=doc_model)
        
        # Parse response to extract subject and body
        lines = response.split('\n')
        subject = "Repository Commit Notification"
        body = response
        
        for line in lines:
            if line.strip().startswith("EMAIL SUBJECT:"):
                subject = line.replace("EMAIL SUBJECT:", "").strip()
                break
        
        # Find email body section
        body_start = response.find("EMAIL BODY:")
        if body_start != -1:
            body = response[body_start + len("EMAIL BODY:"):].strip()
        
        return subject, body
