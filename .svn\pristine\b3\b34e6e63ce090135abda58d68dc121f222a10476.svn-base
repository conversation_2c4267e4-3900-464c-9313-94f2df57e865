#!/usr/bin/env python3
"""
Unified Document Processing Service for RepoSense AI

Consolidates all document processing into a single, coherent pipeline:
- Historical scanning (from repositories)
- File-based processing (from markdown files)
- Real-time processing (from web interface)

This replaces the fragmented approach of having separate DocumentProcessor,
HistoricalScanner processing, and DocumentService processing.
"""

import logging
import re
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from queue import Queue, Empty
from dataclasses import dataclass
from enum import Enum

from document_database import DocumentDatabase, DocumentRecord
from metadata_extractor import MetadataExtractor
from models import CommitInfo, RepositoryConfig
from ollama_client import OllamaClient


class ProcessingSource(Enum):
    """Source of document processing request"""
    HISTORICAL_SCAN = "historical_scan"
    FILE_SYSTEM = "file_system"
    WEB_INTERFACE = "web_interface"
    API_REQUEST = "api_request"


@dataclass
class ProcessingTask:
    """Unified task for document processing"""
    # Source information
    source: ProcessingSource
    priority: int = 0  # Higher numbers = higher priority
    
    # Content (one of these will be provided)
    filepath: Optional[str] = None  # For file-based processing
    commit_info: Optional[CommitInfo] = None  # For historical scanning
    content: Optional[str] = None  # For direct content processing
    
    # Context information
    repository_config: Optional[RepositoryConfig] = None
    document_record: Optional[DocumentRecord] = None
    
    def __lt__(self, other):
        return self.priority > other.priority  # Reverse for max-heap behavior


class UnifiedDocumentProcessor:
    """
    Unified document processing service that handles all document processing scenarios:
    1. Historical scanning from repositories
    2. File system scanning of existing documents
    3. Real-time processing from web interface
    4. API-driven processing
    """
    
    def __init__(self, 
                 output_dir: str = "/app/data/output",
                 db_path: str = "/app/data/documents.db",
                 ollama_client: Optional[OllamaClient] = None,
                 config_manager=None,
                 max_concurrent_tasks: int = 3):
        
        self.output_dir = Path(output_dir)
        self.db = DocumentDatabase(db_path)
        self.ollama_client = ollama_client
        self.config_manager = config_manager
        self.max_concurrent_tasks = max_concurrent_tasks
        self.logger = logging.getLogger(__name__)
        
        # Unified metadata extractor with specialized models
        self.metadata_extractor = MetadataExtractor(
            ollama_client=ollama_client,
            config_manager=config_manager
        )
        
        # Processing queue and control
        self.task_queue: Queue[ProcessingTask] = Queue()
        self.processing_threads: List[threading.Thread] = []
        self.running = False

        # Track which threads are actually busy processing (not just waiting)
        self.busy_threads: set = set()
        
        # Statistics and tracking
        self.stats = {
            'processed_count': 0,
            'error_count': 0,
            'processing_time_total': 0.0,
            'last_scan_time': 0.0
        }
        
        # File change tracking for file system scanning
        self.file_checksums: Dict[str, str] = {}
        self.file_mtimes: Dict[str, float] = {}

    def _get_metadata_extraction_timeout(self) -> int:
        """Get appropriate timeout for metadata extraction based on model complexity"""
        try:
            # Get base timeout from config
            base_timeout = 180  # Default 3 minutes
            if self.config_manager:
                config = self.config_manager.load_config()
                base_timeout = getattr(config, 'ollama_timeout_base', 180)

            # Check if we're using specialized models that might be slower
            specialized_models = []
            if self.config_manager:
                config = self.config_manager.load_config()
                specialized_models = [
                    getattr(config, 'ollama_model_risk_assessment', ''),
                    getattr(config, 'ollama_model_code_review', ''),
                    getattr(config, 'ollama_model_documentation', ''),
                    getattr(config, 'ollama_model', '')
                ]

            # Check for large models that need extra time
            for model in specialized_models:
                if model and any(size in model.lower() for size in ['20b', '33b', '70b', '180b']):
                    # Large models need much more time for metadata extraction
                    timeout = max(base_timeout * 2, 360)  # At least 6 minutes for 20B+ models
                    self.logger.info(f"🕐 Using extended timeout {timeout}s for large model: {model}")
                    return timeout
                elif model and any(size in model.lower() for size in ['7b', '8b', '12b', '13b', '14b']):
                    # Medium-large models need extra time
                    timeout = int(max(base_timeout * 1.5, 270))  # At least 4.5 minutes
                    self.logger.debug(f"🕐 Using extended timeout {timeout}s for medium-large model: {model}")
                    return timeout

            # Default timeout for smaller models
            timeout = max(base_timeout, 180)  # At least 3 minutes
            self.logger.debug(f"🕐 Using standard timeout {timeout}s for metadata extraction")
            return timeout

        except Exception as e:
            self.logger.warning(f"Error determining metadata extraction timeout: {e}")
            return 360  # Safe fallback: 6 minutes

    def start(self):
        """Start the unified processing service"""
        if self.running:
            return
        
        self.running = True
        
        # Start multiple processing threads for concurrent processing
        for i in range(self.max_concurrent_tasks):
            thread = threading.Thread(
                target=self._processing_loop,
                name=f"UnifiedProcessor-{i}",
                daemon=True
            )
            thread.start()
            self.processing_threads.append(thread)
        
        self.logger.info(f"Unified document processor started with {self.max_concurrent_tasks} threads")
    
    def stop(self):
        """Stop the processing service"""
        if not self.running:
            return
        
        self.running = False

        # Wait for threads to finish
        for thread in self.processing_threads:
            if thread.is_alive():
                thread.join(timeout=5.0)

        self.processing_threads.clear()
        self.busy_threads.clear()  # Clear busy threads tracking
        self.logger.info("Unified document processor stopped")
    
    def process_commit(self, commit_info: CommitInfo, repository_config: RepositoryConfig, 
                      documentation: str, priority: int = 5) -> bool:
        """
        Process a commit from historical scanning
        
        Args:
            commit_info: Commit information from repository
            repository_config: Repository configuration
            documentation: Generated documentation content
            priority: Processing priority (higher = more urgent)
        
        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.HISTORICAL_SCAN,
            priority=priority,
            commit_info=commit_info,
            content=documentation,
            repository_config=repository_config
        )
        
        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(f"Queued historical scan task for {commit_info.repository_name} rev {commit_info.revision} (queue size: {queue_size})")
        return True
    
    def process_file(self, filepath: str, priority: int = 3) -> bool:
        """
        Process a document file from the file system
        
        Args:
            filepath: Path to the document file
            priority: Processing priority
        
        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.FILE_SYSTEM,
            priority=priority,
            filepath=filepath
        )
        
        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(f"Queued file processing task for {filepath} (queue size: {queue_size})")
        return True
    
    def process_content(self, content: str, document_record: DocumentRecord, 
                       priority: int = 8) -> bool:
        """
        Process content directly (for web interface/API)
        
        Args:
            content: Document content to process
            document_record: Document record with metadata
            priority: Processing priority
        
        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.WEB_INTERFACE,
            priority=priority,
            content=content,
            document_record=document_record
        )
        
        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(f"Queued content processing task for {document_record.id} (queue size: {queue_size})")
        return True
    
    def scan_file_system(self, force_rescan: bool = False) -> int:
        """
        Scan file system for documents to process
        
        Args:
            force_rescan: Force processing of all files regardless of modification time
        
        Returns:
            int: Number of files queued for processing
        """
        queued_count = 0
        
        try:
            repositories_dir = self.output_dir / "repositories"
            if not repositories_dir.exists():
                return 0
            
            for repo_dir in repositories_dir.iterdir():
                if not repo_dir.is_dir():
                    continue
                
                docs_dir = repo_dir / "docs"
                if not docs_dir.exists():
                    continue
                
                for doc_file in docs_dir.glob("*.md"):
                    if not doc_file.is_file():
                        continue
                    
                    filepath = str(doc_file)
                    file_mtime = doc_file.stat().st_mtime
                    
                    # Check if file needs processing
                    if self._needs_processing(filepath, file_mtime, force_rescan):
                        # Higher priority for newer files
                        priority = int(file_mtime) % 10  # Keep priority reasonable
                        if self.process_file(filepath, priority):
                            queued_count += 1
            
            self.logger.info(f"File system scan queued {queued_count} documents for processing")
            return queued_count
            
        except Exception as e:
            self.logger.error(f"Error during file system scan: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        # Get queue size safely
        try:
            queue_size = self.task_queue.qsize()
        except Exception as e:
            self.logger.warning(f"Error getting queue size: {e}")
            queue_size = 0

        return {
            **self.stats,
            'queue_size': queue_size,
            'active_threads': len(self.busy_threads),  # Only count threads actually processing
            'running': self.running
        }

    def _get_current_tasks(self) -> list:
        """Get information about currently processing tasks (for user feedback)"""
        current_tasks = []

        # Add basic info about queue status
        try:
            queue_size = self.task_queue.qsize()
        except Exception as e:
            self.logger.warning(f"Error getting queue size in _get_current_tasks: {e}")
            queue_size = 0
        if queue_size > 0:
            if queue_size == 1:
                current_tasks.append({
                    'type': 'queued',
                    'description': '1 document waiting to be processed',
                    'status': 'pending',
                    'count': queue_size
                })
            else:
                current_tasks.append({
                    'type': 'queued',
                    'description': f'{queue_size} documents waiting to be processed',
                    'status': 'pending',
                    'count': queue_size
                })

        # Check if threads are actually busy processing (not just waiting)
        active_threads = len(self.busy_threads)
        if active_threads > 0:
            if queue_size > 0:
                # Both queued and processing
                if active_threads == 1:
                    current_tasks.append({
                        'type': 'processing',
                        'description': '1 document currently being processed',
                        'status': 'active',
                        'count': active_threads
                    })
                else:
                    current_tasks.append({
                        'type': 'processing',
                        'description': f'{active_threads} documents currently being processed',
                        'status': 'active',
                        'count': active_threads
                    })
            elif active_threads == 1:
                current_tasks.append({
                    'type': 'processing',
                    'description': 'Processing document (analyzing with AI models)',
                    'status': 'active',
                    'count': active_threads
                })
            else:
                current_tasks.append({
                    'type': 'processing',
                    'description': f'Processing {active_threads} documents (analyzing with AI models)',
                    'status': 'active',
                    'count': active_threads
                })

        return current_tasks
    
    def _processing_loop(self):
        """Main processing loop for worker threads"""
        thread_name = threading.current_thread().name
        self.logger.debug(f"Processing thread {thread_name} started")
        
        while self.running:
            try:
                # Get task with timeout
                task = self.task_queue.get(timeout=1.0)
                queue_size_after = self.task_queue.qsize()
                self.logger.debug(f"{thread_name} got task from queue (remaining: {queue_size_after})")

                # Mark this thread as busy
                self.busy_threads.add(thread_name)

                try:
                    # Process the task
                    start_time = time.time()
                    success = self._process_task(task)
                    processing_time = time.time() - start_time

                    # Update statistics
                    if success:
                        self.stats['processed_count'] += 1
                    else:
                        self.stats['error_count'] += 1

                    self.stats['processing_time_total'] += processing_time

                    self.logger.debug(f"{thread_name} processed {task.source.value} task in {processing_time:.3f}s")
                finally:
                    # Always mark thread as no longer busy
                    self.busy_threads.discard(thread_name)

            except Empty:
                # No tasks available, continue loop (thread is idle)
                continue
            except Exception as e:
                self.logger.error(f"Error in processing thread {thread_name}: {e}")
                self.stats['error_count'] += 1
                # Ensure thread is marked as not busy on error
                self.busy_threads.discard(thread_name)
        
        self.logger.debug(f"Processing thread {thread_name} stopped")
    
    def _process_task(self, task: ProcessingTask) -> bool:
        """Process a single task based on its source"""
        try:
            if task.source == ProcessingSource.HISTORICAL_SCAN:
                return self._process_historical_scan_task(task)
            elif task.source == ProcessingSource.FILE_SYSTEM:
                return self._process_file_system_task(task)
            elif task.source in [ProcessingSource.WEB_INTERFACE, ProcessingSource.API_REQUEST]:
                return self._process_content_task(task)
            else:
                self.logger.error(f"Unknown task source: {task.source}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error processing {task.source.value} task: {e}")
            return False
    
    def _needs_processing(self, filepath: str, file_mtime: float, force_rescan: bool) -> bool:
        """Check if a file needs processing"""
        if force_rescan:
            return True
        
        # Check if file is new or modified
        if filepath not in self.file_mtimes:
            return True
        
        return self.file_mtimes[filepath] < file_mtime
    
    def _process_historical_scan_task(self, task: ProcessingTask) -> bool:
        """Process a historical scan task (from repository commit)"""
        try:
            commit_info = task.commit_info
            repo_config = task.repository_config
            documentation = task.content

            if not commit_info or not repo_config or not documentation:
                self.logger.error("Historical scan task missing required data")
                return False

            # Generate document ID using centralized logic
            doc_id = self.metadata_extractor.generate_document_id(
                commit_info.repository_id,
                int(commit_info.revision)
            )

            # Parse date using centralized logic with fallbacks
            doc_date = self.metadata_extractor.parse_date_with_fallbacks(commit_info.date)

            # Handle commit message - use AI-generated summary if no original message
            commit_message = commit_info.message
            if not commit_info.message or commit_info.message.strip() == "":
                ai_summary = self.metadata_extractor.extract_ai_summary(documentation)
                if ai_summary:
                    commit_message = f"{ai_summary} (AI-generated summary)"
                else:
                    commit_message = "No commit message available"

            # Create document record for enhanced prompt context
            temp_doc_record = DocumentRecord(
                id=doc_id,
                repository_id=commit_info.repository_id,
                repository_name=commit_info.repository_name,
                revision=int(commit_info.revision),
                date=doc_date,
                filename=f"revision_{commit_info.revision}.md",
                filepath=str(self.output_dir / "repositories" / commit_info.repository_name / "docs" / f"revision_{commit_info.revision}.md"),
                size=len(documentation),
                author=commit_info.author,
                commit_message=commit_message,
                changed_paths=commit_info.changed_paths,
                file_modified_time=time.time(),
                processed_time=datetime.now(),
                repository_url=repo_config.url,
                repository_type='svn'  # TODO: Make this dynamic based on repo type
            )

            # Extract metadata using centralized extractor with enhanced prompts
            # Add timeout protection to prevent hanging (Windows-compatible)
            try:
                import threading
                import queue

                # Use threading with timeout for Windows compatibility
                result_queue: queue.Queue[Tuple[str, Any]] = queue.Queue()

                def extract_metadata():
                    try:
                        result = self.metadata_extractor.extract_all_metadata(documentation, temp_doc_record)
                        result_queue.put(('success', result))
                    except Exception as e:
                        # Log the actual error that occurred during metadata extraction
                        self.logger.error(f"Metadata extraction error in thread: {e}")
                        import traceback
                        self.logger.debug(f"Metadata extraction traceback: {traceback.format_exc()}")
                        result_queue.put(('error', e))

                # Start metadata extraction in separate thread
                extract_thread = threading.Thread(target=extract_metadata, name=f"MetadataExtractor-{doc_id[:8]}")
                extract_thread.daemon = True
                extract_thread.start()

                # Wait for result with timeout - use configurable value based on model complexity
                # For large models like gpt-oss:20b, we need much longer timeouts
                timeout_seconds = self._get_metadata_extraction_timeout()
                try:
                    status, result = result_queue.get(timeout=timeout_seconds)
                    if status == 'success':
                        metadata = result
                        self.logger.debug(f"Metadata extraction completed successfully for {doc_id}")
                    else:
                        self.logger.error(f"Metadata extraction failed with error: {result}")
                        raise result
                except queue.Empty:
                    self.logger.warning(f"Metadata extraction thread is still running after {timeout_seconds}s timeout")
                    raise TimeoutError(f"Metadata extraction timed out after {timeout_seconds} seconds")

            except (TimeoutError, Exception) as e:
                self.logger.warning(f"Metadata extraction failed or timed out: {e}")
                self.logger.info("Using fallback metadata values to ensure document is processed")

                # Try to preserve AI model information from temp_doc_record if available
                ai_model_used = 'fallback-timeout'
                if temp_doc_record and temp_doc_record.ai_model_used:
                    ai_model_used = f"{temp_doc_record.ai_model_used}-timeout"
                    self.logger.info(f"Preserved AI model info from temp record: {ai_model_used}")
                else:
                    # Try to get the model that would have been used
                    try:
                        from config_manager import ConfigManager
                        config_manager = ConfigManager('data/config.json')
                        config = config_manager.load_config()
                        # Check for specialized model configuration
                        if hasattr(config, 'ollama_model_risk_assessment') and config.ollama_model_risk_assessment:
                            ai_model_used = f"{config.ollama_model_risk_assessment}-timeout"
                            self.logger.info(f"Preserved specialized AI model info from config: {ai_model_used}")
                        elif hasattr(config, 'ollama_model') and config.ollama_model:
                            ai_model_used = f"{config.ollama_model}-timeout"
                            self.logger.info(f"Preserved default AI model info from config: {ai_model_used}")
                    except Exception as model_error:
                        self.logger.debug(f"Could not determine AI model for fallback: {model_error}")

                # Provide fallback metadata to ensure document is still processed
                metadata = {
                    'code_review_recommended': True,  # Default to requiring review
                    'code_review_priority': 'MEDIUM',
                    'documentation_impact': True,
                    'risk_level': 'MEDIUM',
                    'ai_model_used': ai_model_used  # Preserve AI model info when possible
                }

            # Create final document record with metadata
            doc_record = DocumentRecord(
                id=doc_id,
                repository_id=commit_info.repository_id,
                repository_name=commit_info.repository_name,
                revision=int(commit_info.revision),
                date=doc_date,
                filename=f"revision_{commit_info.revision}.md",
                filepath=temp_doc_record.filepath,
                size=len(documentation),
                author=commit_info.author,
                commit_message=commit_message,
                changed_paths=commit_info.changed_paths,
                code_review_recommended=metadata.get('code_review_recommended'),
                code_review_priority=metadata.get('code_review_priority'),
                documentation_impact=metadata.get('documentation_impact'),
                risk_level=metadata.get('risk_level'),
                file_modified_time=temp_doc_record.file_modified_time,
                processed_time=datetime.now(),
                ai_model_used=metadata.get('ai_model_used') or temp_doc_record.ai_model_used,  # Use metadata first, fallback to temp record
                repository_url=repo_config.url,
                repository_type='svn'
            )

            # Store in database
            success = self.db.upsert_document(doc_record)
            if success:
                self.logger.info(f"✅ Processed historical scan: {commit_info.repository_name} rev {commit_info.revision}")
            else:
                self.logger.error(f"❌ Failed to store historical scan: {commit_info.repository_name} rev {commit_info.revision}")

            return success

        except Exception as e:
            self.logger.error(f"Error processing historical scan task: {e}")
            return False

    def _process_file_system_task(self, task: ProcessingTask) -> bool:
        """Process a file system task (existing markdown file)"""
        try:
            filepath = task.filepath
            if not filepath:
                self.logger.error("File system task missing filepath")
                return False

            doc_file = Path(filepath)
            if not doc_file.exists():
                self.logger.warning(f"Document file not found: {filepath}")
                return False

            # Read and parse the document file
            doc_record = self._parse_document_file(doc_file)
            if not doc_record:
                self.logger.warning(f"Failed to parse document: {filepath}")
                return False

            # Update file tracking
            file_stat = doc_file.stat()
            self.file_mtimes[filepath] = file_stat.st_mtime

            # Store in database
            success = self.db.upsert_document(doc_record)
            if success:
                self.logger.debug(f"✅ Processed file: {doc_file.name}")
            else:
                self.logger.error(f"❌ Failed to store file: {doc_file.name}")

            return success

        except Exception as e:
            self.logger.error(f"Error processing file system task: {e}")
            return False

    def _process_content_task(self, task: ProcessingTask) -> bool:
        """Process a content task (direct content from web/API)"""
        try:
            content = task.content
            document_record = task.document_record

            if not content or not document_record:
                self.logger.error("Content task missing required data")
                return False

            # Extract metadata using centralized extractor
            metadata = self.metadata_extractor.extract_all_metadata(content, document_record)

            # Update document record with extracted metadata
            document_record.code_review_recommended = metadata.get('code_review_recommended')
            document_record.code_review_priority = metadata.get('code_review_priority')
            document_record.documentation_impact = metadata.get('documentation_impact')
            document_record.risk_level = metadata.get('risk_level')
            document_record.processed_time = datetime.now()

            # Store in database
            success = self.db.upsert_document(document_record)
            if success:
                self.logger.debug(f"✅ Processed content: {document_record.id}")
            else:
                self.logger.error(f"❌ Failed to store content: {document_record.id}")

            return success

        except Exception as e:
            self.logger.error(f"Error processing content task: {e}")
            return False

    def _parse_document_file(self, doc_file: Path) -> Optional[DocumentRecord]:
        """Parse a document file and create a DocumentRecord"""
        try:
            # Read file content
            content = doc_file.read_text(encoding='utf-8')

            # Extract basic information from filename and path
            filename = doc_file.name

            # Parse repository information from path structure
            # Expected: /app/data/output/repositories/{repo_name}/docs/{filename}
            path_parts = doc_file.parts
            repo_name = "unknown"
            repo_id = "unknown"

            if len(path_parts) >= 3 and "repositories" in path_parts:
                repo_index = path_parts.index("repositories")
                if repo_index + 1 < len(path_parts):
                    repo_name = path_parts[repo_index + 1]
                    repo_id = repo_name  # Use repo name as ID for file-based processing

            # Extract revision from filename (e.g., revision_123.md)
            revision = 1
            revision_match = re.search(r'revision_(\d+)', filename)
            if revision_match:
                revision = int(revision_match.group(1))

            # Initialize document metadata
            author = "Unknown"
            commit_message = "No commit message available"
            changed_paths = None
            doc_date = datetime.now()

            # Try to extract metadata from document content first
            author = self.metadata_extractor.extract_field(content, "Author") or author
            commit_message = self.metadata_extractor.extract_field(content, "Message") or commit_message
            date_field = self.metadata_extractor.extract_field(content, "Date")

            if date_field:
                try:
                    doc_date = datetime.fromisoformat(date_field.replace('Z', '+00:00'))
                except ValueError:
                    pass  # Use current time as fallback

            # If no commit message from markdown, try to use AI-generated summary
            if commit_message == "No commit message available":
                ai_summary = self.metadata_extractor.extract_ai_summary(content)
                if ai_summary:
                    commit_message = f"{ai_summary} (AI-generated summary)"

            # Final fallback for date: use filename date or file modification time
            if doc_date == datetime.now():
                file_stat = doc_file.stat()
                doc_date = datetime.fromtimestamp(file_stat.st_mtime)

            # File metadata
            file_stat = doc_file.stat()
            doc_id = self.metadata_extractor.generate_document_id(repo_id, revision)

            # Create a temporary document record for enhanced prompt context
            temp_doc_record = DocumentRecord(
                id=doc_id,
                repository_id=repo_id,
                repository_name=repo_name,
                revision=revision,
                date=doc_date,
                filename=filename,
                filepath=str(doc_file),
                size=file_stat.st_size,
                author=author,
                commit_message=commit_message,
                changed_paths=changed_paths,
                file_modified_time=file_stat.st_mtime,
                processed_time=datetime.now()
            )

            # Extract metadata using centralized extractor with enhanced prompts
            metadata = self.metadata_extractor.extract_all_metadata(content, temp_doc_record)

            # Create final document record with metadata
            return DocumentRecord(
                id=doc_id,
                repository_id=repo_id,
                repository_name=repo_name,
                revision=revision,
                date=doc_date,
                filename=filename,
                filepath=str(doc_file),
                size=file_stat.st_size,
                author=author,
                commit_message=commit_message,
                changed_paths=changed_paths,
                code_review_recommended=metadata.get('code_review_recommended'),
                code_review_priority=metadata.get('code_review_priority'),
                documentation_impact=metadata.get('documentation_impact'),
                risk_level=metadata.get('risk_level'),
                file_modified_time=file_stat.st_mtime,
                processed_time=datetime.now(),
                ai_model_used=metadata.get('ai_model_used') or temp_doc_record.ai_model_used  # Use metadata first, fallback to temp record
            )

        except Exception as e:
            self.logger.error(f"Error parsing document file {doc_file}: {e}")
            return None
