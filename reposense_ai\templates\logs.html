{% extends "base.html" %}

{% block title %}Logs - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Application Logs</h1>
            <p class="page-subtitle">View real-time application logs and system events</p>
        </div>
        <div class="btn-group" role="group">
            <button class="btn btn-outline-primary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button id="pause-btn" class="btn btn-outline-warning" onclick="toggleAutoRefresh()">
                <i class="fas fa-pause"></i> Pause
            </button>
            <button class="btn btn-outline-danger" onclick="cleanupLogs()" title="Clean up large log files">
                <i class="fas fa-broom"></i> Cleanup
            </button>
            <button class="btn btn-outline-success" onclick="downloadLogs()" title="Download log file">
                <i class="fas fa-download"></i> Download
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <!-- Log Level Filter Controls -->
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-filter"></i> Log Level Filters</h6>
            </div>
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="form-check-inline-group">
                            {% for level, count in log_levels.items() %}
                            <div class="form-check form-check-inline">
                                <input class="form-check-input log-level-filter" type="checkbox"
                                       id="level-{{ level.lower() }}" value="{{ level }}" checked>
                                <label class="form-check-label" for="level-{{ level.lower() }}">
                                    <span class="log-level-badge log-level-{{ level.lower() }}">{{ level }}</span>
                                    <small class="text-muted">({{ count }})</small>
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-sm btn-outline-secondary me-2" onclick="selectAllLevels()">
                            <i class="fas fa-check-square"></i> All
                        </button>
                        <button class="btn btn-sm btn-outline-secondary me-2" onclick="selectNoLevels()">
                            <i class="fas fa-square"></i> None
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="applyLogFilter()">
                            <i class="fas fa-filter"></i> Apply Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Filter -->
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-search"></i> Search Filter</h6>
            </div>
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search-input"
                                   placeholder="Search log content..."
                                   onkeyup="applySearchFilter()">
                            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                <i class="fas fa-times"></i> Clear
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            <span id="search-results-count">All entries visible</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log Display -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-file-alt"></i> System Logs</h5>
                <div class="log-status">
                    <span id="log-count-display" class="badge bg-info">{{ logs|length }} entries</span>
                    <span id="filter-status" class="badge bg-secondary ms-2" style="display: none;">Filtered</span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container p-3" id="log-container">
                    {% for log_line in logs %}
                        {% if log_line.strip() %}
                            <div class="log-line">{{ log_line|e }}</div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    <span id="log-footer-text">Showing last {{ log_entries_count or 100 }} log entries. Logs auto-refresh every 10 seconds.</span>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let autoRefreshInterval;
let isFiltering = false;
let isPaused = false;
let isRefreshing = false;

function refreshLogs() {
    // Prevent multiple concurrent refresh requests
    if (isRefreshing) {
        return;
    }

    if (isFiltering) {
        applyLogFilter();
    } else {
        isRefreshing = true;

        // Smooth refresh without full page reload
        let logContainer = document.getElementById('log-container');
        let originalScrollTop = logContainer.scrollTop;
        let wasAtBottom = logContainer.scrollTop >= (logContainer.scrollHeight - logContainer.clientHeight - 10);

        // Show subtle loading indicator
        let refreshBtn = document.querySelector('.btn-outline-primary');
        let originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;

        // Create AbortController for timeout
        let controller = new AbortController();
        let timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

        // Fetch fresh logs and counts without page reload
        fetch('/api/logs/filtered?lines=' + ({{ log_entries_count or 100 }}) + '&include_counts=true', {
            signal: controller.signal
        })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update log display smoothly
                    logContainer.innerHTML = '';
                    data.logs.forEach(logLine => {
                        if (logLine.trim()) {
                            let logDiv = document.createElement('div');
                            logDiv.className = 'log-line';
                            logDiv.textContent = logLine;
                            logContainer.appendChild(logDiv);
                        }
                    });

                    // Apply color coding
                    applyLogColorCoding();

                    // Update log level counts if provided
                    if (data.log_levels) {
                        updateLogLevelCounts(data.log_levels);
                    }

                    // Force scroll to bottom for auto-refresh (always scroll to bottom for new logs)
                    setTimeout(() => {
                        logContainer.scrollTop = logContainer.scrollHeight;
                    }, 50);

                    // Update footer
                    updateFooterText();
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                console.error('Error refreshing logs:', error);

                let errorMessage = error.name === 'AbortError' ?
                    'Request timed out. The server may be busy.' :
                    error.message;

                // Show error message to user
                logContainer.innerHTML = `<div class="text-danger p-3">Error loading logs: ${errorMessage}. Please try refreshing the page.</div>`;
            })
            .finally(() => {
                // Restore refresh button and reset refresh flag
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
                isRefreshing = false;
            });
    }
}

function selectAllLevels() {
    let checkboxes = document.querySelectorAll('.log-level-filter');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    saveFilterState();
}

function selectNoLevels() {
    let checkboxes = document.querySelectorAll('.log-level-filter');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    saveFilterState();
}

function applyLogFilter() {
    let selectedLevels = [];
    let checkboxes = document.querySelectorAll('.log-level-filter:checked');

    checkboxes.forEach(checkbox => {
        selectedLevels.push(checkbox.value);
    });

    // Show loading state
    let logContainer = document.getElementById('log-container');
    let originalContent = logContainer.innerHTML;
    logContainer.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Filtering logs...</div>';

    // Build query parameters
    let params = new URLSearchParams();
    selectedLevels.forEach(level => {
        params.append('levels', level);
    });
    params.append('lines', {{ log_entries_count or 100 }});

    // Make API call
    fetch(`/api/logs/filtered?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Update log display
                logContainer.innerHTML = '';
                data.logs.forEach(logLine => {
                    if (logLine.trim()) {
                        let logDiv = document.createElement('div');
                        logDiv.className = 'log-line';
                        logDiv.textContent = logLine;
                        logContainer.appendChild(logDiv);
                    }
                });

                // Update status displays
                document.getElementById('log-count-display').textContent = `${data.total_lines} entries`;
                let filterStatus = document.getElementById('filter-status');
                let allLevels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];
                let unselectedLevels = allLevels.filter(level => !selectedLevels.includes(level));

                if (selectedLevels.length === 5) {
                    filterStatus.style.display = 'none';
                    isFiltering = false;
                } else if (selectedLevels.length === 0) {
                    filterStatus.textContent = 'No log levels selected';
                    filterStatus.className = 'badge bg-warning ms-2';
                    filterStatus.style.display = 'inline';
                    isFiltering = true;
                } else {
                    filterStatus.textContent = `Hiding: ${unselectedLevels.join(', ')}`;
                    filterStatus.className = 'badge bg-secondary ms-2';
                    filterStatus.style.display = 'inline';
                    isFiltering = true;
                }

                // Update footer text
                updateFooterText();

                // Apply color coding
                applyLogColorCoding();

                // Apply search filter to new content
                applySearchFilter();

                // Scroll to bottom
                setTimeout(() => {
                    logContainer.scrollTop = logContainer.scrollHeight;
                }, 50);

                // Save filter state
                saveFilterState();
            } else {
                logContainer.innerHTML = `<div class="text-danger p-3">Error loading logs: ${data.error}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching filtered logs:', error);
            logContainer.innerHTML = originalContent;
        });
}

function saveFilterState() {
    let selectedLevels = [];
    let checkboxes = document.querySelectorAll('.log-level-filter:checked');
    checkboxes.forEach(checkbox => {
        selectedLevels.push(checkbox.value);
    });
    localStorage.setItem('selectedLogLevels', JSON.stringify(selectedLevels));
}

function updateLogLevelCounts(logLevels) {
    // Update the count displays in the filter badges
    Object.keys(logLevels).forEach(level => {
        const checkbox = document.querySelector(`input[value="${level}"]`);
        if (checkbox) {
            const label = checkbox.nextElementSibling;
            const countSpan = label.querySelector('small');
            if (countSpan) {
                countSpan.textContent = `(${logLevels[level]})`;
            }
        }
    });
}

function cleanupLogs() {
    if (!confirm('This will clean up large log files and may remove older log entries. Continue?')) {
        return;
    }

    const cleanupBtn = document.querySelector('.btn-outline-danger');
    const originalText = cleanupBtn.innerHTML;
    cleanupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cleaning...';
    cleanupBtn.disabled = true;

    fetch('/api/logs/cleanup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ max_size_mb: {{ config.log_cleanup_max_size_mb }} })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Log cleanup completed successfully. Current log file size: ${data.current_size_mb}MB`);
            // Refresh logs to show updated content
            refreshLogs();
        } else {
            alert(`Log cleanup failed: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('Error cleaning up logs:', error);
        alert('Error cleaning up logs. Please try again.');
    })
    .finally(() => {
        cleanupBtn.innerHTML = originalText;
        cleanupBtn.disabled = false;
    });
}

function downloadLogs() {
    let downloadBtn = document.querySelector('.btn-outline-success');
    let originalText = downloadBtn.innerHTML;
    downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing...';
    downloadBtn.disabled = true;

    // Use window.location to trigger download instead of creating a link
    try {
        window.location.href = '/api/logs/download';
    } catch (error) {
        console.error('Error downloading logs:', error);
        alert('Error downloading logs. Please try again.');
    }

    // Reset button after a short delay
    setTimeout(() => {
        downloadBtn.innerHTML = originalText;
        downloadBtn.disabled = false;
    }, 1000);
}

function restoreFilterState() {
    let savedLevels = localStorage.getItem('selectedLogLevels');

    if (savedLevels) {
        try {
            let selectedLevels = JSON.parse(savedLevels);
            let checkboxes = document.querySelectorAll('.log-level-filter');

            // Only modify checkboxes if we have a saved state
            if (selectedLevels && selectedLevels.length > 0) {
                // First uncheck all
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Then check saved ones
                selectedLevels.forEach(level => {
                    let checkbox = document.getElementById(`level-${level.toLowerCase()}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }
        } catch (e) {
            console.warn('Error restoring filter state:', e);
        }
    } else {
        // Save the current default state (all checked) for future use
        saveFilterState();
    }
}

function applyRestoredFilter() {
    let checkboxes = document.querySelectorAll('.log-level-filter');
    let checkedBoxes = document.querySelectorAll('.log-level-filter:checked');

    // If not all checkboxes are checked, we need to apply the filter
    if (checkedBoxes.length < checkboxes.length) {
        // Apply the filter without showing loading state since we're on initial load
        applyLogFilterSilent();
    }
}

function applyLogFilterSilent() {
    let selectedLevels = [];
    let checkboxes = document.querySelectorAll('.log-level-filter:checked');

    checkboxes.forEach(checkbox => {
        selectedLevels.push(checkbox.value);
    });

    // Build query parameters
    let params = new URLSearchParams();
    selectedLevels.forEach(level => {
        params.append('levels', level);
    });
    params.append('lines', {{ log_entries_count or 100 }});

    // Make API call without loading state
    fetch(`/api/logs/filtered?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                let logContainer = document.getElementById('log-container');

                // Update log display
                logContainer.innerHTML = '';
                data.logs.forEach(logLine => {
                    if (logLine.trim()) {
                        let logDiv = document.createElement('div');
                        logDiv.className = 'log-line';
                        logDiv.textContent = logLine;
                        logContainer.appendChild(logDiv);
                    }
                });

                // Update status displays
                document.getElementById('log-count-display').textContent = `${data.total_lines} entries`;
                let filterStatus = document.getElementById('filter-status');
                let allLevels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];
                let unselectedLevels = allLevels.filter(level => !selectedLevels.includes(level));

                if (selectedLevels.length === 5) {
                    filterStatus.style.display = 'none';
                    isFiltering = false;
                } else if (selectedLevels.length === 0) {
                    filterStatus.textContent = 'No log levels selected';
                    filterStatus.className = 'badge bg-warning ms-2';
                    filterStatus.style.display = 'inline';
                    isFiltering = true;
                } else {
                    filterStatus.textContent = `Hiding: ${unselectedLevels.join(', ')}`;
                    filterStatus.className = 'badge bg-secondary ms-2';
                    filterStatus.style.display = 'inline';
                    isFiltering = true;
                }

                // Apply color coding
                applyLogColorCoding();

                // Apply search filter to new content
                applySearchFilter();

                // Scroll to bottom
                setTimeout(() => {
                    logContainer.scrollTop = logContainer.scrollHeight;
                }, 50);

                // Update footer text
                updateFooterText();
            }
        })
        .catch(error => {
            console.error('Error applying restored filter:', error);
        });
}

function applyLogColorCoding() {
    const logLines = document.querySelectorAll('.log-line');

    logLines.forEach(line => {
        const text = line.textContent;

        // Use regex to extract the log level from the specific position
        // Format: "YYYY-MM-DD HH:MM:SS,mmm - LEVEL - message"
        const match = text.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - (\w+) - /);
        if (match) {
            const level = match[1].toUpperCase();

            if (level === 'CRITICAL') {
                line.style.setProperty('color', '#6f42c1', 'important');
                line.style.fontWeight = 'bold';
            } else if (level === 'ERROR') {
                line.style.setProperty('color', '#dc3545', 'important');
                line.style.fontWeight = 'bold';
            } else if (level === 'WARNING') {
                line.style.setProperty('color', '#fd7e14', 'important');
            } else if (level === 'DEBUG') {
                line.style.setProperty('color', '#6c757d', 'important');
            } else if (level === 'INFO') {
                line.style.setProperty('color', '#0dcaf0', 'important');
            }
        }
    });
}

// Toggle auto-refresh pause/resume
function toggleAutoRefresh() {
    const pauseBtn = document.getElementById('pause-btn');
    const footerText = document.getElementById('log-footer-text');

    if (isPaused) {
        // Resume auto-refresh
        isPaused = false;
        startAutoRefresh();
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        pauseBtn.className = 'btn btn-outline-warning';
        updateFooterText();
        localStorage.setItem('logAutoRefreshPaused', 'false');
    } else {
        // Pause auto-refresh
        isPaused = true;
        stopAutoRefresh();
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        pauseBtn.className = 'btn btn-outline-success';
        updateFooterText();
        localStorage.setItem('logAutoRefreshPaused', 'true');
    }
}

// Auto-refresh logs every 10 seconds
function startAutoRefresh() {
    if (isPaused) return; // Don't start if paused

    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    autoRefreshInterval = setInterval(() => {
        // Only auto-refresh if user isn't actively scrolling
        let logContainer = document.getElementById('log-container');
        let now = Date.now();
        if (!logContainer.lastScrollTime || (now - logContainer.lastScrollTime) > 3000) {
            refreshLogs();
        }
    }, 10000);
}

// Stop auto-refresh
function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// Apply search filter to visible log entries
function applySearchFilter() {
    let searchTerm = document.getElementById('search-input').value.toLowerCase().trim();
    let logLines = document.querySelectorAll('.log-line');
    let searchResultsCount = document.getElementById('search-results-count');

    let visibleCount = 0;

    logLines.forEach(line => {
        if (!searchTerm || line.textContent.toLowerCase().includes(searchTerm)) {
            line.style.display = '';
            visibleCount++;
        } else {
            line.style.display = 'none';
        }
    });

    // Update search results count
    if (searchTerm) {
        searchResultsCount.textContent = `${visibleCount} of ${logLines.length} entries match`;
        searchResultsCount.className = 'text-info';
    } else {
        searchResultsCount.textContent = 'All entries visible';
        searchResultsCount.className = 'text-muted';
    }

    // Save search state
    localStorage.setItem('logSearchTerm', document.getElementById('search-input').value);

    // Update footer text
    updateFooterText();
}

// Clear search filter
function clearSearch() {
    document.getElementById('search-input').value = '';
    applySearchFilter();
}

// Update footer text based on current state
function updateFooterText() {
    let footerText = document.getElementById('log-footer-text');
    let selectedLevels = Array.from(document.querySelectorAll('.log-level-filter:checked')).map(cb => cb.value);
    let searchTerm = document.getElementById('search-input').value.trim();

    let baseText;
    let logContainer = document.getElementById('log-container');
    let visibleEntries = Array.from(logContainer.querySelectorAll('.log-line')).filter(line => line.style.display !== 'none');
    let totalEntries = logContainer.querySelectorAll('.log-line').length;

    if (searchTerm) {
        baseText = `Showing ${visibleEntries.length} of ${totalEntries} entries (search: "${searchTerm}")`;
    } else if (selectedLevels.length < 5) {
        baseText = `Showing ${visibleEntries.length} filtered log entries (${selectedLevels.join(', ')})`;
    } else {
        baseText = `Showing ${visibleEntries.length} log entries`;
    }

    if (isPaused) {
        footerText.innerHTML = `<i class="fas fa-pause-circle text-warning"></i> ${baseText}. Auto-refresh paused.`;
    } else {
        footerText.innerHTML = `<i class="fas fa-sync-alt text-success"></i> ${baseText}. Auto-refresh every 10 seconds.`;
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    let logContainer = document.getElementById('log-container');

    // Restore saved height from localStorage
    let savedHeight = localStorage.getItem('logContainerHeight');
    if (savedHeight) {
        logContainer.style.height = savedHeight;
    }

    // Save height when user resizes
    let resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
            let height = entry.target.style.height;
            if (height) {
                localStorage.setItem('logContainerHeight', height);
            }
        }
    });
    resizeObserver.observe(logContainer);

    // Restore filter state (with small delay to ensure DOM is ready)
    setTimeout(() => {
        restoreFilterState();
        // Apply the restored filter state automatically
        applyRestoredFilter();
    }, 100);

    // Restore search state
    let savedSearchTerm = localStorage.getItem('logSearchTerm');
    if (savedSearchTerm) {
        document.getElementById('search-input').value = savedSearchTerm;
    }

    // Restore pause state
    let savedPauseState = localStorage.getItem('logAutoRefreshPaused');
    if (savedPauseState === 'true') {
        isPaused = true;
        let pauseBtn = document.getElementById('pause-btn');
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        pauseBtn.className = 'btn btn-outline-success';
    }

    // Apply initial color coding
    applyLogColorCoding();

    // Apply initial search filter
    applySearchFilter();

    // Scroll to bottom
    setTimeout(() => {
        logContainer.scrollTop = logContainer.scrollHeight;
    }, 100);

    // Update footer text
    updateFooterText();

    // Start auto-refresh (respects pause state)
    startAutoRefresh();

    // Add event listeners to checkboxes for auto-save
    let checkboxes = document.querySelectorAll('.log-level-filter');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', saveFilterState);
    });

    // Track user scrolling to avoid interrupting them during auto-refresh
    if (logContainer) {
        logContainer.addEventListener('scroll', function() {
            this.lastScrollTime = Date.now();
        });
    }
});
</script>

<style>
.log-level-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75em;
    font-weight: bold;
    color: white;
}

.log-level-debug {
    background-color: #6c757d;
}

.log-level-info {
    background-color: #0dcaf0;
}

.log-level-warning {
    background-color: #fd7e14;
}

.log-level-error {
    background-color: #dc3545;
}

.log-level-critical {
    background-color: #6f42c1;
}

.form-check-inline-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.log-container {
    height: 500px;
    overflow-y: auto;
    background-color: #1e1e1e;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    resize: vertical;
    min-height: 200px;
    max-height: 1200px;
}

.log-line {
    padding: 2px 0;
    border-bottom: 1px solid #333;
}

.log-line:hover {
    background-color: #2d2d2d;
}

.log-status {
    display: flex;
    align-items: center;
}
</style>
{% endblock %}
