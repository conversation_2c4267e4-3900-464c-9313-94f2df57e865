# Complete Docker Compose Example with RepoSense AI Integration
# This shows how to add RepoSense AI to your existing setup

version: '3.8'

services:
  # LLM and Chat Interface 
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 2
              capabilities: [gpu]
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - OLLAMA_KEEP_ALIVE=-1
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_NOPRUNE=true
    runtime: nvidia
    volumes:
      - ollama:/root/.ollama
    networks:
      - ollama-network

  # ADD THIS SERVICE TO YOUR EXISTING DOCKER-COMPOSE.YML
  reposense-ai:
    build:
      context: ./reposense_ai
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      # Essential volumes - always mounted
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
      # Development volumes - mount source code for hot reloads (optional)
      - ./reposense_ai:/app
      # Exclude node_modules and other build artifacts to avoid conflicts
      - /app/node_modules
      - /app/.git
    environment:
      # Web interface settings
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      # Development settings (override via .env file)
      - REPOSENSE_AI_LOG_LEVEL=${REPOSENSE_AI_LOG_LEVEL:-INFO}
      - REPOSENSE_AI_DB_DEBUG=${REPOSENSE_AI_DB_DEBUG:-false}
      # Integration with your Ollama service
      - OLLAMA_BASE_URL=http://ollama:11434
      # Optional model override
      - OLLAMA_MODEL=${REPOSENSE_AI_MODEL:-llama3.2}
    networks:
      - ollama-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      - ollama

  # Open WebUI Pipelines Server for Anthropic and Gemini Integration
  pipelines:
    container_name: pipelines
    image: ghcr.io/open-webui/pipelines:main
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "9099:9099"
    volumes:
      - ./pipelines:/app/pipelines
      - pipelines_data:/app/data
    environment:
      - PIPELINES_DIR=/app/pipelines
    depends_on:
      - ollama

  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui
    restart: unless-stopped
    ports:
      - "3000:8080"
    environment:
      - OLLAMA_API_BASE_URL=http://ollama:11434/api
      - WEBUI_AUTH=true
      - ENABLE_SIGNUP=false
      - DEFAULT_USER_ROLE=user
      - ENABLE_ADMIN_EXPORT=true
      - ENABLE_IMAGE_GENERATION=true
      - IMAGE_GENERATION_API_URL=http://automatic1111:7860/sdapi/v1/txt2img
      - ENABLE_FUNCTIONS=true
      - WEBUI_LOG_LEVEL=DEBUG
    depends_on:
      - ollama
      - stable-diffusion-webui
      - pipelines
    volumes:
      - open-webui:/app/backend/data
    networks:
      - ollama-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Stable Diffusion WebUI
  stable-diffusion-webui:
    container_name: automatic1111
    networks:
      - ollama-network
    image: emsi/stable-diffusion-webui
    restart: unless-stopped
    runtime: nvidia
    ports:
      - "7860:7860"
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
    volumes:
      - sd_models:/stable-diffusion-webui/models
      - sd_outputs:/stable-diffusion-webui/outputs
    command: python3 launch.py --listen --medvram --xformers --api
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ['0']

  # Container Management GUI
  portainer:
    container_name: portainer
    image: portainer/portainer-ce:latest
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    environment:
      - TZ=UTC

  # Vector Database for RAG
  qdrant:
    container_name: qdrant
    networks:
      - ollama-network
    image: qdrant/qdrant:latest
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT_ALLOW_RECOVERY=true
    command: ["./qdrant", "--memory-size", "8GB"]

  # Speech-to-Text (Whisper)
  whisper:
    container_name: whisper
    networks:
      - ollama-network
    image: rhasspy/wyoming-whisper:latest
    restart: unless-stopped
    ports:
      - "10300:10300"
    volumes:
      - whisper_data:/data
    environment:
      - WHISPER_MODEL=base
      - COMPUTE_TYPE=int8
    command: --uri 'tcp://0.0.0.0:10300' --data-dir /data --model base --compute-type int8
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ['0']

  # Text-to-Speech
  tts:
    container_name: tts
    networks:
      - ollama-network
    image: synesthesiam/opentts:latest
    restart: unless-stopped
    ports:
      - "5500:5500"
    volumes:
      - tts_data:/app/data
    environment:
      - TZ=UTC

  # Python jupyter notebooks etc.
  jupyterlab:
    container_name: jupyterlab
    image: jupyter/datascience-notebook:latest
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_TOKEN=devuser
    volumes:
      - ./notebooks:/home/<USER>/work

networks:
  ollama-network:
    driver: bridge

volumes:
  open-webui:
  ollama:
  whisper_data:
  tts_data:
  sd_models:
  sd_outputs:
  qdrant_data:
  portainer_data:
  pipelines_data:
