#!/usr/bin/env python3
"""
Analyze current document ID formats and plan migration
"""
import sys
sys.path.append('/app')

from document_database import DocumentDatabase
import re

def analyze_document_ids():
    print("=== DOCUMENT ID FORMAT ANALYSIS ===")
    
    # Initialize database
    db = DocumentDatabase('/app/data/documents.db')
    
    # Get all documents
    all_docs = db.get_documents(limit=1000)
    print(f"Total documents: {len(all_docs)}")
    
    # Categorize by ID format
    uuid_format = []  # b32d6910-a982-4fe7-a65e-67cf40bf3c5c_17
    simple_format = []  # reposense_cpp_test/docs_17
    other_format = []
    
    # UUID pattern: 8-4-4-4-12 characters followed by underscore and number
    uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_\d+$')
    # Simple pattern: repo_name/docs_number
    simple_pattern = re.compile(r'^[^/]+/docs_\d+$')
    
    for doc in all_docs:
        if uuid_pattern.match(doc.id):
            uuid_format.append(doc)
        elif simple_pattern.match(doc.id):
            simple_format.append(doc)
        else:
            other_format.append(doc)
    
    print(f"\n📊 ID Format Distribution:")
    print(f"   UUID format: {len(uuid_format)} documents")
    print(f"   Simple format: {len(simple_format)} documents")
    print(f"   Other format: {len(other_format)} documents")
    
    # Show examples
    if uuid_format:
        print(f"\n✅ UUID Format Examples:")
        for doc in uuid_format[:3]:
            print(f"   {doc.id} (Rev {doc.revision}, Repo: {doc.repository_name})")
    
    if simple_format:
        print(f"\n🔄 Simple Format (TO MIGRATE):")
        for doc in simple_format[:5]:
            print(f"   {doc.id} (Rev {doc.revision}, Repo: {doc.repository_name})")
        if len(simple_format) > 5:
            print(f"   ... and {len(simple_format) - 5} more")
    
    if other_format:
        print(f"\n⚠️  Other Format (NEEDS REVIEW):")
        for doc in other_format:
            print(f"   {doc.id} (Rev {doc.revision}, Repo: {doc.repository_name})")
    
    # Check repository consistency
    print(f"\n🔍 Repository Analysis:")
    repo_ids = set()
    repo_names = set()
    for doc in all_docs:
        repo_ids.add(doc.repository_id)
        repo_names.add(doc.repository_name)
    
    print(f"   Unique repository IDs: {len(repo_ids)}")
    print(f"   Unique repository names: {len(repo_names)}")
    
    for repo_id in sorted(repo_ids):
        # Find a document with this repo ID to get the name
        sample_doc = next((doc for doc in all_docs if doc.repository_id == repo_id), None)
        if sample_doc:
            print(f"   {repo_id} -> {sample_doc.repository_name}")
    
    # Migration plan
    print(f"\n📋 MIGRATION PLAN:")
    print(f"   ✅ Keep {len(uuid_format)} UUID-format documents as-is")
    print(f"   🔄 Migrate {len(simple_format)} simple-format documents to UUID format")
    if other_format:
        print(f"   ⚠️  Review {len(other_format)} documents with unexpected formats")
    
    return {
        'uuid_format': uuid_format,
        'simple_format': simple_format,
        'other_format': other_format,
        'total': len(all_docs)
    }

if __name__ == "__main__":
    analyze_document_ids()
