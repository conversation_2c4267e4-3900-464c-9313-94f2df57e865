<!DOCTYPE html>
<html>
<head>
    <title>Fixed Download Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Fixed Download Test</h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Test Document</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="downloadDocument('markdown')">
                            <i class="fab fa-markdown me-2"></i>Markdown (.md)
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="downloadDocument('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>PDF (.pdf)
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <pre id="rawContent">This is test document content.

It has multiple paragraphs and demonstrates the download functionality.

## Features Tested:
- Document information section
- Clean markdown download
- PDF generation capability
- Proper JavaScript variable handling

This content should appear in both downloads with complete metadata.</pre>
                
                <!-- Hidden element with raw diff content -->
                <div id="rawDiffContent" style="display: none;">Index: test.py
===================================================================
--- test.py	(revision 122)
+++ test.py	(revision 123)
@@ -1,3 +1,4 @@
 def hello_world():
     print("Hello, World!")
+    print("This is a new line!")
 
 if __name__ == "__main__":
     hello_world()</div>
            </div>
        </div>
        
        <div class="mt-3 alert alert-info">
            <p><strong>✅ Fixed Issues:</strong></p>
            <ul>
                <li>JavaScript template variables now properly rendered</li>
                <li>Document data moved to JavaScript object</li>
                <li>Both Markdown and PDF downloads should work</li>
                <li>Complete document information included</li>
            </ul>
        </div>
    </div>

    <script>
        // Document data (simulated - in real app this comes from template)
        const documentData = {
            id: 'test-123',
            displayName: 'Test Document - Revision 123',
            repositoryName: 'test-repository',
            revision: '123',
            author: 'test-user',
            date: '2025-01-01 12:00:00',
            filename: 'test-document.md',
            size: '2.6',
            relativePath: 'repositories/test-repository/docs/test-document.md',
            commitMessage: 'Test commit for download functionality',
            changedFiles: ['/test.py', '/README.md'],
            filesChangedCount: '2 files changed in this commit'
        };

        function downloadDocument(format = 'markdown') {
            if (format === 'pdf') {
                downloadAsPDF();
            } else {
                downloadAsMarkdown();
            }
        }

        function downloadAsMarkdown() {
            const baseFilename = documentData.filename;
            const filename = baseFilename.replace(/\.[^/.]+$/, '') + '.md';

            // Build complete markdown content with document information
            let content = '# ' + documentData.displayName + '\n\n';
            
            // Add document information section
            content += '## Document Information\n\n';
            content += '**Repository:** ' + documentData.repositoryName + '\n\n';
            content += '**Revision:** ' + documentData.revision + '\n\n';
            content += '**Author:** ' + documentData.author + '\n\n';
            content += '**Date:** ' + documentData.date + ' UTC\n\n';
            content += '**Filename:** ' + documentData.filename + '\n\n';
            content += '**Size:** ' + documentData.size + ' KB\n\n';
            content += '**Path:** ' + documentData.relativePath + '\n\n';
            content += '**Commit Message:** ' + documentData.commitMessage + '\n\n';
            
            if (documentData.changedFiles && documentData.changedFiles.length > 0) {
                content += '**Files Changed:**\n\n';
                documentData.changedFiles.forEach(function(filePath) {
                    content += '- `' + filePath + '`\n';
                });
                content += '\n' + documentData.filesChangedCount + '\n\n';
            }
            
            content += '---\n\n';
            
            // Add main document content
            content += '## Document Content\n\n';
            content += document.getElementById('rawContent').textContent;

            // Add diff content if available
            let diffElement = document.getElementById('rawDiffContent');
            if (diffElement && diffElement.textContent.trim()) {
                const cleanDiffContent = diffElement.textContent.trim();
                content += '\n\n---\n\n## Code Changes (Diff)\n\n```diff\n' + cleanDiffContent + '\n```';
            }

            const blob = new Blob([content], { type: 'text/markdown' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        function downloadAsPDF() {
            alert('PDF download would work with the real API endpoint!\n\nIn the actual application, this would:\n1. Send document data to /api/documents/' + documentData.id + '/download/pdf\n2. Generate a professional PDF with ReportLab\n3. Download the PDF file\n\nThe Markdown download above should work perfectly!');
        }
    </script>
</body>
</html>
