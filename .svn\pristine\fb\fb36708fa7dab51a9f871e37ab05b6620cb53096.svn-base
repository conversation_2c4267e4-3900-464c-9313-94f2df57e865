{% extends "base.html" %}

{% block title %}Documents - RepoSense AI{% endblock %}

{% block content %}
<style>
/* Custom styles for different view modes */
.card-view .card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-view .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.repository-group .card-header {
    cursor: pointer;
}

.repository-group .card-header:hover {
    background-color: rgba(13, 202, 240, 0.9) !important;
}

.view-mode-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

/* Responsive adjustments for cards */
@media (max-width: 768px) {
    .card-view .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Repository groups styling */
.repository-group .table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.repository-group .table td {
    vertical-align: middle;
}
</style>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Generated Documents</h1>
            <p class="page-subtitle">View and manage AI-generated documentation for repository commits</p>
        </div>

        <!-- Processing Status Indicator -->
        <div id="documents-processing-status" class="d-flex align-items-center" style="display: none;">
            <div class="alert alert-info mb-0 me-3">
                <i class="fas fa-spinner fa-spin me-2"></i>
                <span id="processing-message">Processing documents...</span>
            </div>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshDocuments()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ total_count }}</h5>
                <p class="card-text text-muted">Total Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-success" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-success">{{ stats|length }}</h5>
                <p class="card-text text-muted">Repositories</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-warning" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ (total_size / 1024)|round(1) }} KB</h5>
                <p class="card-text text-muted">Total Size</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-info" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-info">{% if documents %}{{ documents[0].date.strftime('%Y-%m-%d') }}{% else %}N/A{% endif %}</h5>
                <p class="card-text text-muted">Latest Document</p>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filters and Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-info" style="border-width: 2px;">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-filter me-2"></i> Filters & Organization</h6>
                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="false">
                    <i class="fas fa-chevron-down"></i> Toggle Filters
                </button>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('documents_page') }}" id="filtersForm">
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="search" name="search"
                                           value="{{ search_query or '' }}"
                                           placeholder="Search commit messages, authors, repositories...">
                                </div>
                            </div>

                            <!-- Repository Filter -->
                            <div class="col-md-4">
                                <label for="repository" class="form-label">Repository</label>
                                <select class="form-select" id="repository" name="repository">
                                    <option value="">All Repositories</option>
                                    {% for repo in available_repositories %}
                                        <option value="{{ repo.id }}"
                                                {% if repository_filter == repo.id %}selected{% endif %}>
                                            {{ repo.name }} ({{ repo.doc_count }} docs)
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Author Filter -->
                            <div class="col-md-4">
                                <label for="author" class="form-label">Author</label>
                                <select class="form-select" id="author" name="author">
                                    <option value="">All Authors</option>
                                    {% for author in available_authors %}
                                        <option value="{{ author }}"
                                                {% if author_filter == author %}selected{% endif %}>
                                            {{ author }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Date Range -->
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Date From</label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                       value="{{ date_from or '' }}">
                            </div>

                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Date To</label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                       value="{{ date_to or '' }}">
                            </div>

                            <!-- Code Review Filter -->
                            <div class="col-md-2">
                                <label for="code_review" class="form-label">Code Review</label>
                                <select class="form-select" id="code_review" name="code_review">
                                    <option value="">All</option>
                                    <option value="true" {% if code_review_filter == true %}selected{% endif %}>Required</option>
                                    <option value="false" {% if code_review_filter == false %}selected{% endif %}>Not Required</option>
                                </select>
                            </div>

                            <!-- Documentation Impact Filter -->
                            <div class="col-md-2">
                                <label for="doc_impact" class="form-label">Docs Impact</label>
                                <select class="form-select" id="doc_impact" name="doc_impact">
                                    <option value="">All</option>
                                    <option value="true" {% if doc_impact_filter == true %}selected{% endif %}>Required</option>
                                    <option value="false" {% if doc_impact_filter == false %}selected{% endif %}>Not Required</option>
                                </select>
                            </div>

                            <!-- Risk Level Filter -->
                            <div class="col-md-2">
                                <label for="risk_level" class="form-label">Risk Level</label>
                                <select class="form-select" id="risk_level" name="risk_level">
                                    <option value="">All</option>
                                    <option value="HIGH" {% if risk_level_filter == 'HIGH' %}selected{% endif %}>High</option>
                                    <option value="MEDIUM" {% if risk_level_filter == 'MEDIUM' %}selected{% endif %}>Medium</option>
                                    <option value="LOW" {% if risk_level_filter == 'LOW' %}selected{% endif %}>Low</option>
                                </select>
                            </div>
                        </div>

                        <!-- Sorting and View Controls -->
                        <div class="row g-3 mt-2">
                            <div class="col-md-3">
                                <label for="sort_by" class="form-label">Sort By</label>
                                <select class="form-select" id="sort_by" name="sort_by">
                                    <option value="date" {% if sort_by == 'date' %}selected{% endif %}>Date</option>
                                    <option value="repository" {% if sort_by == 'repository' %}selected{% endif %}>Repository</option>
                                    <option value="author" {% if sort_by == 'author' %}selected{% endif %}>Author</option>
                                    <option value="revision" {% if sort_by == 'revision' %}selected{% endif %}>Revision</option>
                                    <option value="size" {% if sort_by == 'size' %}selected{% endif %}>Size</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="sort_order" class="form-label">Order</label>
                                <select class="form-select" id="sort_order" name="sort_order">
                                    <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>Descending</option>
                                    <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>Ascending</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="per_page" class="form-label">Per Page</label>
                                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="view_mode" class="form-label">View Mode</label>
                                <select class="form-select" id="view_mode" name="view_mode">
                                    <option value="table" {% if view_mode == 'table' %}selected{% endif %}>Table</option>
                                    <option value="cards" {% if view_mode == 'cards' %}selected{% endif %}>Cards</option>
                                    <option value="repository_groups" {% if view_mode == 'repository_groups' %}selected{% endif %}>By Repository</option>
                                </select>
                            </div>

                            <div class="col-md-3 d-flex align-items-end">
                                <div class="btn-group w-100" role="group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Apply Filters
                                    </button>
                                    <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden fields to preserve pagination -->
                        <input type="hidden" name="page" value="1">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documents Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i> Documents
                    {% if total_count > 0 %}
                        <span class="badge bg-light text-primary ms-2">{{ total_count }} found</span>
                    {% endif %}
                    <!-- View Mode Indicator -->
                    <span class="badge bg-secondary ms-2">
                        {% if view_mode == 'cards' %}
                            <i class="fas fa-th-large me-1"></i>Cards View
                        {% elif view_mode == 'repository_groups' %}
                            <i class="fas fa-layer-group me-1"></i>By Repository
                        {% else %}
                            <i class="fas fa-table me-1"></i>Table View
                        {% endif %}
                    </span>
                </h5>
                <div>
                    <button class="btn btn-outline-light btn-sm" onclick="cleanupOrphanedDocuments()" title="Remove orphaned documents">
                        <i class="fas fa-broom"></i> Cleanup
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="deleteAllDocuments()" title="Delete ALL documents">
                        <i class="fas fa-trash"></i> Delete All
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="resetDatabase()" title="Reset database (creates backup)">
                        <i class="fas fa-database"></i> Reset DB
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="clearDocumentCache()" title="Clear document cache">
                        <i class="fas fa-trash-alt"></i> Clear Cache
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="refreshDocuments()" title="Refresh documents">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if documents %}
                    <!-- Table View -->
                    {% if view_mode == 'table' or not view_mode %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Repository</th>
                                    <th>Revision</th>
                                    <th>Date</th>
                                    <th>Author</th>
                                    <th>Commit Message</th>
                                    <th>Code Review</th>
                                    <th>Docs Impact</th>
                                    <th>User Feedback</th>
                                    <th>Size</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in documents %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ doc.repository_name }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ doc.revision }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ doc.date.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </td>
                                    <td>{{ doc.author }}</td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 300px;" title="{{ doc.commit_message }}">
                                            {{ doc.commit_message }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if doc.code_review_recommended is not none %}
                                            {% if doc.code_review_recommended %}
                                                <span class="badge bg-warning text-dark" title="Code review recommended">
                                                    <i class="fas fa-eye"></i>
                                                    {% if doc.code_review_priority %}{{ doc.code_review_priority }}{% else %}YES{% endif %}
                                                </span>
                                            {% else %}
                                                <span class="badge bg-success" title="No code review needed">
                                                    <i class="fas fa-check"></i> NO
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary" title="Not analyzed">
                                                <i class="fas fa-question"></i> N/A
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if doc.documentation_impact is not none %}
                                            {% if doc.documentation_impact %}
                                                <span class="badge bg-info" title="Documentation updates needed">
                                                    <i class="fas fa-file-alt"></i> YES
                                                </span>
                                            {% else %}
                                                <span class="badge bg-success" title="No documentation impact">
                                                    <i class="fas fa-check"></i> NO
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary" title="Not analyzed">
                                                <i class="fas fa-question"></i> N/A
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <!-- User Feedback Indicators -->
                                        <div class="d-flex gap-1">
                                            {% if doc.user_code_review_status %}
                                                <span class="badge bg-{{ 'success' if doc.user_code_review_status == 'approved' else 'warning' if doc.user_code_review_status == 'needs_changes' else 'danger' if doc.user_code_review_status == 'rejected' else 'info' }}"
                                                      title="Code Review: {{ doc.user_code_review_status|title }}">
                                                    <i class="fas fa-code-branch"></i>
                                                </span>
                                            {% endif %}
                                            {% if doc.user_documentation_rating %}
                                                <span class="badge bg-{{ 'success' if doc.user_documentation_rating >= 4 else 'warning' if doc.user_documentation_rating >= 3 else 'danger' }}"
                                                      title="Documentation Rating: {{ doc.user_documentation_rating }}/5">
                                                    <i class="fas fa-star"></i> {{ doc.user_documentation_rating }}
                                                </span>
                                            {% endif %}
                                            {% if doc.user_risk_assessment_override %}
                                                <span class="badge bg-{{ 'danger' if doc.user_risk_assessment_override == 'HIGH' else 'warning' if doc.user_risk_assessment_override == 'MEDIUM' else 'success' }}"
                                                      title="Risk Override: {{ doc.user_risk_assessment_override }}">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </span>
                                            {% endif %}
                                            {% if not doc.user_code_review_status and not doc.user_documentation_rating and not doc.user_risk_assessment_override %}
                                                <small class="text-muted">No feedback</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ (doc.size / 1024)|round(1) }} KB</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <div class="dropdown">
                                                <button class="btn btn-outline-primary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false" title="View Document">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url_for('view_document', doc_id=doc.id) }}">
                                                            <i class="fas fa-file-alt me-2"></i>Document Only
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><h6 class="dropdown-header">Document + Diff</h6></li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url_for('view_document', doc_id=doc.id, include_diff='true', diff_format='unified') }}">
                                                            <i class="fas fa-code me-2"></i>Unified Diff
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url_for('view_document', doc_id=doc.id, include_diff='true', diff_format='side-by-side') }}">
                                                            <i class="fas fa-columns me-2"></i>Side-by-Side Diff
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <button class="btn btn-outline-info"
                                                    onclick="toggleDiff('{{ doc.id }}')"
                                                    title="View Diff">
                                                <i class="fas fa-code"></i>
                                            </button>
                                            <button class="btn btn-outline-danger"
                                                    onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')"
                                                    title="Delete Document">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- Diff content row (initially hidden) -->
                                <tr id="diff-row-{{ doc.id }}" class="diff-row" style="display: none;">
                                    <td colspan="8" class="p-0">
                                        <div class="bg-light border-top">
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0 text-muted">
                                                        <i class="fas fa-code"></i> Code Changes (Diff)
                                                    </h6>
                                                    <div class="d-flex align-items-center gap-2">
                                                        <div class="btn-group btn-group-sm" role="group">
                                                            <input type="radio" class="btn-check" name="diffFormat{{ doc.id }}" id="unified{{ doc.id }}" value="unified" checked>
                                                            <label class="btn btn-outline-secondary" for="unified{{ doc.id }}" onclick="changeDiffFormat('{{ doc.id }}', 'unified')">Unified</label>

                                                            <input type="radio" class="btn-check" name="diffFormat{{ doc.id }}" id="sideBySide{{ doc.id }}" value="side-by-side">
                                                            <label class="btn btn-outline-secondary" for="sideBySide{{ doc.id }}" onclick="changeDiffFormat('{{ doc.id }}', 'side-by-side')">Side-by-Side</label>
                                                        </div>
                                                        <button class="btn btn-sm btn-outline-secondary"
                                                                onclick="toggleDiff('{{ doc.id }}')" title="Hide Diff">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div id="diff-content-{{ doc.id }}" class="diff-content">
                                                    <div class="text-center text-muted py-3">
                                                        <i class="fas fa-spinner fa-spin"></i> Loading diff...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    <!-- Cards View -->
                    {% if view_mode == 'cards' %}
                    <div class="row card-view">
                        {% for doc in documents %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-primary">{{ doc.repository_name }}</span>
                                    <small class="text-muted">Rev {{ doc.revision }}</small>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-user me-1"></i>{{ doc.author }}
                                    </h6>
                                    <p class="card-text text-truncate" title="{{ doc.commit_message }}">
                                        {{ doc.commit_message }}
                                    </p>
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            {% if doc.code_review_recommended %}
                                                <span class="badge bg-warning">Review Required</span>
                                            {% else %}
                                                <span class="badge bg-success">No Review</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-4">
                                            {% if doc.documentation_impact %}
                                                <span class="badge bg-info">Docs Update</span>
                                            {% else %}
                                                <span class="badge bg-secondary">No Docs</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-4">
                                            {% if doc.risk_level %}
                                                <span class="badge bg-{{ 'danger' if doc.risk_level == 'HIGH' else 'warning' if doc.risk_level == 'MEDIUM' else 'success' }}">
                                                    {{ doc.risk_level }}
                                                </span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">Unknown</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ doc.date.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('view_document', doc_id=doc.id) }}" class="btn btn-outline-primary" title="View Document">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-info" onclick="toggleDiff('{{ doc.id }}')" title="View Diff">
                                            <i class="fas fa-code"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Repository Groups View -->
                    {% if view_mode == 'repository_groups' %}
                    {% set grouped_docs = documents | groupby('repository_name') %}
                    {% for repository_name, repo_docs in grouped_docs %}
                    <div class="mb-4 repository-group">
                        <div class="card shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-folder me-2"></i>{{ repository_name }}
                                    <span class="badge bg-light text-info ms-2">{{ repo_docs | list | length }} documents</span>
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Revision</th>
                                                <th>Date</th>
                                                <th>Author</th>
                                                <th>Commit Message</th>
                                                <th>Code Review</th>
                                                <th>Docs Impact</th>
                                                <th>Risk</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for doc in repo_docs %}
                                            <tr>
                                                <td><strong>{{ doc.revision }}</strong></td>
                                                <td><small class="text-muted">{{ doc.date.strftime('%Y-%m-%d %H:%M') }}</small></td>
                                                <td>{{ doc.author }}</td>
                                                <td>
                                                    <span class="text-truncate d-inline-block" style="max-width: 250px;" title="{{ doc.commit_message }}">
                                                        {{ doc.commit_message }}
                                                    </span>
                                                </td>
                                                <td>
                                                    {% if doc.code_review_recommended %}
                                                        <span class="badge bg-warning">Required</span>
                                                    {% else %}
                                                        <span class="badge bg-success">Not Required</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if doc.documentation_impact %}
                                                        <span class="badge bg-info">Required</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Not Required</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if doc.risk_level %}
                                                        <span class="badge bg-{{ 'danger' if doc.risk_level == 'HIGH' else 'warning' if doc.risk_level == 'MEDIUM' else 'success' }}">
                                                            {{ doc.risk_level }}
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-light text-dark">Unknown</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ url_for('view_document', doc_id=doc.id) }}" class="btn btn-outline-primary" title="View Document">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button class="btn btn-outline-info" onclick="toggleDiff('{{ doc.id }}')" title="View Diff">
                                                            <i class="fas fa-code"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Documents Found</h5>
                        <p class="text-muted">Documents will appear here after commits are processed and analyzed by the AI.</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                {% endif %}

                <!-- Pagination Controls -->
                {% if total_count > 0 %}
                <div class="d-flex justify-content-between align-items-center mt-4 px-3 pb-3">
                    <div class="d-flex align-items-center">
                        <div class="text-muted me-3">
                            Showing {{ ((page - 1) * per_page + 1) }} to {{ [page * per_page, total_count]|min }} of {{ total_count }} documents
                        </div>
                        <div class="d-flex align-items-center">
                            <label for="perPageSelect" class="form-label text-muted me-2 mb-0">Per page:</label>
                            <select id="perPageSelect" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                                <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                                <option value="200" {% if per_page == 200 %}selected{% endif %}>200</option>
                            </select>
                        </div>
                    </div>
                    {% if total_pages > 1 %}
                    <nav aria-label="Documents pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <!-- Previous Page -->
                            {% if page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('documents_page', page=page-1, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </span>
                                </li>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% set start_page = [1, page - 2]|max %}
                            {% set end_page = [total_pages, page + 2]|min %}

                            {% if start_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('documents_page', page=1, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">1</a>
                                </li>
                                {% if start_page > 2 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endif %}

                            {% for p in range(start_page, end_page + 1) %}
                                {% if p == page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ p }}</span>
                                    </li>
                                {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('documents_page', page=p, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">{{ p }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if end_page < total_pages %}
                                {% if end_page < total_pages - 1 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('documents_page', page=total_pages, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">{{ total_pages }}</a>
                                </li>
                            {% endif %}

                            <!-- Next Page -->
                            {% if page < total_pages %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('documents_page', page=page+1, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </span>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the document:</p>
                <p><strong id="deleteDocumentName"></strong></p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteDocumentForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Document
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshDocuments() {
    location.reload();
}

function changePerPage(newPerPage) {
    // Get current URL parameters
    const urlParams = new URLSearchParams(window.location.search);

    // Update per_page parameter
    urlParams.set('per_page', newPerPage);

    // Reset to page 1 when changing per_page to avoid empty pages
    urlParams.set('page', '1');

    // Navigate to new URL
    window.location.search = urlParams.toString();
}

// Enhanced filter functionality
function clearAllFilters() {
    // Clear all form inputs
    document.getElementById('filtersForm').reset();

    // Navigate to clean URL
    window.location.href = "{{ url_for('documents_page') }}";
}

function applyQuickFilter(filterType, filterValue) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set(filterType, filterValue);
    urlParams.set('page', '1'); // Reset to first page
    window.location.search = urlParams.toString();
}

// Auto-submit form when certain filters change
document.addEventListener('DOMContentLoaded', function() {
    const autoSubmitElements = ['repository', 'author', 'sort_by', 'sort_order', 'view_mode'];

    autoSubmitElements.forEach(function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener('change', function() {
                // Reset to page 1 when filters change
                document.querySelector('input[name="page"]').value = '1';
                document.getElementById('filtersForm').submit();
            });
        }
    });

    // Add search functionality with debounce
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                if (searchInput.value.length >= 3 || searchInput.value.length === 0) {
                    document.querySelector('input[name="page"]').value = '1';
                    document.getElementById('filtersForm').submit();
                }
            }, 500); // 500ms debounce
        });
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+F to focus search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            document.getElementById('search').focus();
        }

        // Escape to clear search
        if (e.key === 'Escape' && document.activeElement === document.getElementById('search')) {
            document.getElementById('search').value = '';
            document.getElementById('filtersForm').submit();
        }
    });
});

function cleanupOrphanedDocuments() {
    if (confirm('This will remove all documents that belong to repositories that no longer exist. Continue?')) {
        fetch('/api/documents/cleanup-orphaned', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Success: ${data.message}`);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cleaning up orphaned documents.');
        });
    }
}

function clearDocumentCache() {
    if (confirm('This will clear the document cache and force a refresh from the database. Continue?')) {
        fetch('/api/documents/clear-cache', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Success: ${data.message}`);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing the cache.');
        });
    }
}

function deleteAllDocuments() {
    if (confirm('⚠️ WARNING: This will permanently delete ALL documents (database records AND physical files). This action cannot be undone. Are you sure?')) {
        if (confirm('This is your final confirmation. Delete ALL documents?')) {
            fetch('/api/documents/delete-all', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Success: ${data.message}`);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting documents.');
            });
        }
    }
}

function resetDatabase() {
    if (confirm('⚠️ WARNING: This will reset the entire database and delete all documents. A backup will be created. This action cannot be undone. Are you sure?')) {
        if (confirm('This is your final confirmation. Reset the database?')) {
            fetch('/api/database/reset', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Success: ' + data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while resetting the database.');
            });
        }
    }
}

function deleteDocument(docId, docName) {
    document.getElementById('deleteDocumentName').textContent = docName;
    document.getElementById('deleteDocumentForm').action = '/api/documents/' + docId + '/delete';
    
    new bootstrap.Modal(document.getElementById('deleteDocumentModal')).show();
}

// Handle delete form submission
document.getElementById('deleteDocumentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and refresh page
            bootstrap.Modal.getInstance(document.getElementById('deleteDocumentModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the document.');
    });
});

// Auto-refresh every 30 seconds
setInterval(refreshDocuments, 30000);

// Diff toggle functionality
function toggleDiff(docId) {
    const diffRow = document.getElementById(`diff-row-${docId}`);
    const diffContent = document.getElementById(`diff-content-${docId}`);

    if (diffRow.style.display === 'none') {
        // Show diff row
        diffRow.style.display = '';

        // Load diff content if not already loaded
        if (diffContent.innerHTML.includes('Loading diff...')) {
            loadDiffContent(docId, 'unified');
        }
    } else {
        // Hide diff row
        diffRow.style.display = 'none';
    }
}

// Load diff content with specified format
function loadDiffContent(docId, format) {
    const diffContent = document.getElementById(`diff-content-${docId}`);

    // Show loading indicator
    diffContent.innerHTML = `
        <div class="text-center text-muted py-3">
            <i class="fas fa-spinner fa-spin"></i> Loading ${format} diff...
        </div>
    `;

    fetch(`/api/documents/${docId}/diff?format=${format}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                diffContent.innerHTML = `
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle"></i> ${data.error}
                    </div>
                `;
            } else {
                if (data.format === 'side-by-side') {
                    // Side-by-side format is already HTML
                    diffContent.innerHTML = `
                        <div style="max-height: 500px; overflow-y: auto;">
                            ${data.diff}
                        </div>
                    `;
                } else {
                    // Unified format needs to be wrapped in pre/code
                    diffContent.innerHTML = `
                        <pre class="bg-dark text-light p-3 rounded mb-0" style="max-height: 400px; overflow-y: auto;"><code>${escapeHtml(data.diff)}</code></pre>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error loading diff:', error);
            diffContent.innerHTML = `
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-circle"></i> Error loading diff content
                </div>
            `;
        });
}

// Change diff format
function changeDiffFormat(docId, format) {
    loadDiffContent(docId, format);
}

// Helper function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Processing Status Management for Documents Page
let documentsProcessingInterval = null;

function updateDocumentsProcessingStatus() {
    fetch('/api/processing-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const processing = data.processing;
                const statusDiv = document.getElementById('documents-processing-status');
                const messageSpan = document.getElementById('processing-message');

                if (processing.queue_size > 0 || processing.current_tasks.length > 0) {
                    // Show processing status
                    statusDiv.style.display = 'flex';
                    statusDiv.classList.remove('d-none');

                    // Update message based on current state
                    let message = '';
                    if (processing.queue_size > 0) {
                        message = `Processing ${processing.queue_size} documents...`;
                    } else if (processing.current_tasks.length > 0) {
                        const activeTask = processing.current_tasks.find(t => t.status === 'active');
                        if (activeTask) {
                            message = activeTask.description;
                        } else {
                            message = 'Processing documents...';
                        }
                    }
                    messageSpan.textContent = message;

                    // Auto-refresh document list every 10 seconds when processing
                    if (!window.processingRefreshActive) {
                        window.processingRefreshActive = true;
                        setTimeout(() => {
                            if (processing.queue_size === 0 && processing.current_tasks.length === 0) {
                                // Processing finished, refresh the document list
                                loadDocuments(1);
                                window.processingRefreshActive = false;
                            }
                        }, 10000);
                    }
                } else {
                    // Hide processing status
                    statusDiv.style.display = 'none';
                    statusDiv.classList.add('d-none');

                    // If we were processing before, refresh the document list
                    if (window.wasProcessing) {
                        loadDocuments(1);
                        window.wasProcessing = false;
                    }
                }

                // Track processing state
                window.wasProcessing = (processing.queue_size > 0 || processing.current_tasks.length > 0);
            }
        })
        .catch(error => {
            console.error('Error fetching processing status:', error);
        });
}

// Start processing status updates when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initial update
    updateDocumentsProcessingStatus();

    // Poll every 5 seconds (slightly longer than dashboard)
    documentsProcessingInterval = setInterval(updateDocumentsProcessingStatus, 5000);
});

// Clean up interval when page unloads
window.addEventListener('beforeunload', function() {
    if (documentsProcessingInterval) {
        clearInterval(documentsProcessingInterval);
    }
});


</script>
{% endblock %}
