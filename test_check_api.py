#!/usr/bin/env python3
"""
Test the /api/check endpoint directly to see what's happening
"""

import requests
import json
import sys
import os
from pathlib import Path

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from config_manager import ConfigManager
from monitor_service import MonitorService

def test_check_api():
    """Test the /api/check endpoint"""
    print("🧪 Testing /api/check endpoint...")
    
    try:
        # Test the API endpoint
        response = requests.post('http://localhost:5000/api/check', timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Check succeeded!")
            print(f"   Success: {data.get('success')}")
            print(f"   Status: {data.get('status')}")
            print(f"   Repositories checked: {data.get('repositories_checked')}")
        else:
            data = response.json()
            print(f"❌ Check failed!")
            print(f"   Success: {data.get('success')}")
            print(f"   Error: {data.get('error')}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to http://localhost:5000")
        print("   Make sure the RepoSense AI web interface is running")
    except Exception as e:
        print(f"❌ Error testing API: {e}")

def test_monitor_service_directly():
    """Test the monitor service directly"""
    print("\n🔧 Testing Monitor Service Directly...")
    
    try:
        # Initialize monitor service the same way the web app does
        config_path = "/app/data/config.json" if os.path.exists("/app/data/config.json") else "data/config.json"
        print(f"Using config path: {config_path}")
        
        monitor_service = MonitorService(config_path)
        
        # Check repositories
        enabled_repos = monitor_service.config.get_enabled_repositories()
        print(f"Enabled repositories: {len(enabled_repos)}")
        
        if enabled_repos:
            for repo in enabled_repos:
                print(f"  - {repo.name} ({repo.type}): {repo.url}")
                print(f"    Enabled: {repo.enabled}, Last revision: {repo.last_revision}")
        
        # Test run_once method
        print(f"\n🧪 Testing run_once method...")
        if enabled_repos:
            try:
                monitor_service.run_once()
                print(f"✅ run_once completed successfully")
            except Exception as e:
                print(f"❌ run_once failed: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"❌ No repositories to check")
            
    except Exception as e:
        print(f"❌ Error testing monitor service: {e}")
        import traceback
        traceback.print_exc()

def check_config_files():
    """Check all possible config file locations"""
    print("\n📁 Checking Config File Locations...")
    
    possible_paths = [
        "/app/data/config.json",
        "/app/config.dev.json", 
        "data/config.json",
        "config.json",
        "../data/config.json"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r') as f:
                    config_data = json.load(f)
                repo_count = len(config_data.get('repositories', []))
                print(f"✅ {path}: {repo_count} repositories")
                if repo_count > 0:
                    for repo in config_data['repositories']:
                        print(f"    - {repo.get('name', 'Unknown')} ({repo.get('type', 'Unknown')})")
            except Exception as e:
                print(f"❌ {path}: Error reading - {e}")
        else:
            print(f"❌ {path}: Not found")

if __name__ == "__main__":
    check_config_files()
    test_monitor_service_directly()
    test_check_api()
