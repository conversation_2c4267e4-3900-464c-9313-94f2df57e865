#!/usr/bin/env python3
"""
Fix corrupted document ID in the database.
The document with ID 'reposense_cpp_test/docs_17' should be 'b32d6910-a982-4fe7-a65e-67cf40bf3c5c_17'
"""

import sys
import os
sys.path.append('/app')

from document_database import DocumentDatabase
from config_manager import ConfigManager

def fix_document_id():
    """Fix the corrupted document ID"""
    print("🔧 Fixing corrupted document ID...")
    
    # Initialize database and config
    db = DocumentDatabase('/app/data/documents.db')
    config_manager = ConfigManager('/app/data/config.json')
    config = config_manager.load_config()
    
    # Find the corrupted document
    corrupted_id = "reposense_cpp_test/docs_17"
    doc = db.get_document_by_id(corrupted_id)
    
    if not doc:
        print(f"❌ Document with ID '{corrupted_id}' not found")
        return False
    
    print(f"✅ Found corrupted document:")
    print(f"   Current ID: {doc.id}")
    print(f"   Repository ID: {doc.repository_id}")
    print(f"   Repository Name: {doc.repository_name}")
    print(f"   Revision: {doc.revision}")
    
    # Find the correct repository configuration
    repo_config = None
    for repo in config.repositories:
        if repo.name == "reposense_cpp_test":
            repo_config = repo
            break
    
    if not repo_config:
        print(f"❌ Repository configuration not found for 'reposense_cpp_test'")
        return False
    
    print(f"✅ Found repository configuration:")
    print(f"   Correct Repository ID: {repo_config.id}")
    print(f"   Repository Name: {repo_config.name}")
    
    # Generate the correct document ID
    correct_doc_id = f"{repo_config.id}_{doc.revision}"
    print(f"✅ Correct document ID should be: {correct_doc_id}")
    
    # Check if a document with the correct ID already exists
    existing_correct_doc = db.get_document_by_id(correct_doc_id)
    if existing_correct_doc:
        print(f"⚠️  Document with correct ID already exists!")
        print(f"   Deleting corrupted document: {corrupted_id}")
        db.delete_document(corrupted_id)
        print(f"✅ Corrupted document deleted")
        return True
    
    # Update the document with the correct IDs
    print(f"🔄 Updating document IDs...")
    
    # Create a new document record with correct IDs
    from document_database import DocumentRecord
    corrected_doc = DocumentRecord(
        id=correct_doc_id,
        repository_id=repo_config.id,  # Use the correct UUID
        repository_name=doc.repository_name,
        revision=doc.revision,
        date=doc.date,
        filename=doc.filename,
        filepath=doc.filepath,
        size=doc.size,
        author=doc.author,
        commit_message=doc.commit_message,
        changed_paths=doc.changed_paths,
        code_review_recommended=doc.code_review_recommended,
        code_review_priority=doc.code_review_priority,
        documentation_impact=doc.documentation_impact,
        risk_level=doc.risk_level,
        file_modified_time=doc.file_modified_time,
        processed_time=doc.processed_time,
        ai_model_used=doc.ai_model_used,
        risk_aggressiveness_used=doc.risk_aggressiveness_used,
        repository_url=getattr(doc, 'repository_url', None),
        repository_type=getattr(doc, 'repository_type', None),
        # User feedback fields
        user_code_review_status=getattr(doc, 'user_code_review_status', None),
        user_code_review_comments=getattr(doc, 'user_code_review_comments', None),
        user_code_review_reviewer=getattr(doc, 'user_code_review_reviewer', None),
        user_code_review_date=getattr(doc, 'user_code_review_date', None),
        user_documentation_rating=getattr(doc, 'user_documentation_rating', None),
        user_documentation_comments=getattr(doc, 'user_documentation_comments', None),
        user_documentation_updated_by=getattr(doc, 'user_documentation_updated_by', None),
        user_documentation_updated_date=getattr(doc, 'user_documentation_updated_date', None),
        user_risk_assessment_override=getattr(doc, 'user_risk_assessment_override', None),
        user_risk_assessment_comments=getattr(doc, 'user_risk_assessment_comments', None),
        user_risk_assessment_updated_by=getattr(doc, 'user_risk_assessment_updated_by', None),
        user_risk_assessment_updated_date=getattr(doc, 'user_risk_assessment_updated_date', None),
        user_documentation_input=getattr(doc, 'user_documentation_input', None),
        user_documentation_suggestions=getattr(doc, 'user_documentation_suggestions', None),
        user_documentation_input_by=getattr(doc, 'user_documentation_input_by', None),
        user_documentation_input_date=getattr(doc, 'user_documentation_input_date', None),
        ai_summary=getattr(doc, 'ai_summary', None),
        heuristic_context=getattr(doc, 'heuristic_context', None)
    )
    
    # Delete the old corrupted document
    print(f"🗑️  Deleting corrupted document: {corrupted_id}")
    db.delete_document(corrupted_id)
    
    # Insert the corrected document
    print(f"💾 Inserting corrected document: {correct_doc_id}")
    success = db.upsert_document(corrected_doc)
    
    if success:
        print(f"✅ Document ID fixed successfully!")
        print(f"   Old ID: {corrupted_id}")
        print(f"   New ID: {correct_doc_id}")
        return True
    else:
        print(f"❌ Failed to insert corrected document")
        return False

if __name__ == "__main__":
    success = fix_document_id()
    if success:
        print(f"\n🎉 Document ID fix completed successfully!")
    else:
        print(f"\n❌ Document ID fix failed!")
    sys.exit(0 if success else 1)
