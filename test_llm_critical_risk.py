#!/usr/bin/env python3
"""
Test LLM analysis with CRITICAL risk level and confidence ratings
"""

import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from datetime import datetime
from reposense_ai.metadata_extractor import <PERSON><PERSON>taExtractor
from reposense_ai.document_database import <PERSON>ume<PERSON><PERSON><PERSON><PERSON>

def test_llm_critical_analysis():
    """Test LLM analysis with CRITICAL risk scenarios"""
    
    print("🧪 Testing LLM analysis with CRITICAL risk level and confidence ratings...")
    
    # Create metadata extractor (without Ollama for prompt testing)
    extractor = MetadataExtractor(ollama_client=None)
    
    # Test CRITICAL risk scenario
    critical_documentation = """
    ## Summary
    Emergency security fix for critical buffer overflow vulnerability in authentication system
    
    ## Changes
    - Fixed buffer overflow in password validation function
    - Resolved race condition in session token generation
    - Patched memory leak in crypto library usage
    - Added critical section protection for user data access
    - Fixed segmentation fault in production SSL handshake
    
    ## Security Impact
    CRITICAL - Multiple security vulnerabilities that could lead to:
    - Remote code execution via buffer overflow
    - Authentication bypass via race condition
    - Memory corruption and system crashes
    - Data exposure through memory leaks
    
    ## Risk Assessment
    This is a critical security patch that must be deployed immediately.
    The vulnerabilities affect production systems and pose immediate risk.
    """
    
    # Test the prompt generation
    system_prompt, user_prompt = extractor._get_basic_metadata_prompts_with_context(
        critical_documentation, 
        "Heuristic analysis indicates CRITICAL risk with confidence 0.95"
    )
    
    print("🔍 Generated LLM Prompts:")
    print("\n📋 SYSTEM PROMPT:")
    print("=" * 80)
    print(system_prompt[:500] + "..." if len(system_prompt) > 500 else system_prompt)
    
    print("\n📋 USER PROMPT:")
    print("=" * 80)
    print(user_prompt[:300] + "..." if len(user_prompt) > 300 else user_prompt)
    
    # Check if CRITICAL is mentioned in the prompts
    if "CRITICAL" in system_prompt:
        print("\n✅ CRITICAL risk level found in system prompt")
    else:
        print("\n❌ CRITICAL risk level NOT found in system prompt")
    
    if "risk_confidence" in system_prompt:
        print("✅ Risk confidence field found in system prompt")
    else:
        print("❌ Risk confidence field NOT found in system prompt")
    
    if "documentation_confidence" in system_prompt:
        print("✅ Documentation confidence field found in system prompt")
    else:
        print("❌ Documentation confidence field NOT found in system prompt")
    
    # Test heuristic analysis with the enhanced system
    print("\n🔍 Testing Enhanced Heuristic Analysis:")
    heuristic_context = extractor._gather_heuristic_context(critical_documentation)
    
    if heuristic_context and 'indicators' in heuristic_context:
        indicators = heuristic_context['indicators']
        print(f"  Risk Assessment: {indicators.get('risk_assessment', 'Unknown')}")
        print(f"  Risk Confidence: {indicators.get('risk_confidence', 'Unknown')}")
        print(f"  Doc Assessment: {indicators.get('doc_assessment', 'Unknown')}")
        print(f"  Doc Confidence: {indicators.get('doc_confidence', 'Unknown')}")
        
        if 'CRITICAL' in indicators.get('risk_assessment', ''):
            print("✅ Heuristic analysis correctly identifies CRITICAL risk")
        else:
            print("❌ Heuristic analysis does not identify CRITICAL risk")
    
    # Test a simulated LLM response parsing
    print("\n🧪 Testing LLM Response Parsing:")
    
    # Simulate what an LLM might return for CRITICAL risk
    simulated_llm_response = {
        'code_review_recommended': True,
        'code_review_priority': 'CRITICAL',
        'code_review_confidence': 0.95,
        'risk_level': 'CRITICAL',
        'risk_confidence': 0.92,
        'documentation_impact': True,
        'documentation_confidence': 0.88,
        'reasoning': 'Multiple critical security vulnerabilities including buffer overflow and race conditions require immediate attention'
    }
    
    print("Simulated LLM Response:")
    print(json.dumps(simulated_llm_response, indent=2))
    
    # Test if the voting system would handle CRITICAL correctly
    votes = {'llm': 'CRITICAL', 'heuristic': 'HIGH'}
    confidence_scores = {'llm': 0.92, 'heuristic': 0.85}
    
    final_risk = extractor._vote_on_risk_simple(votes, confidence_scores)
    print(f"\n🗳️  Voting Result: {final_risk}")
    
    if final_risk == 'CRITICAL':
        print("✅ Voting system correctly handles CRITICAL risk level")
    else:
        print("❌ Voting system does not handle CRITICAL risk level correctly")

def test_llm_confidence_extraction():
    """Test confidence extraction from LLM responses"""
    
    print("\n🧪 Testing LLM Confidence Extraction...")
    
    # Create metadata extractor
    extractor = MetadataExtractor(ollama_client=None)
    
    # Simulate LLM result with confidence scores
    llm_result = {
        'risk_level': 'HIGH',
        'risk_confidence': 0.85,
        'documentation_impact': True,
        'documentation_confidence': 0.78
    }
    
    # Test risk confidence extraction
    risk_confidence = llm_result.get('risk_confidence', 0.7)
    doc_confidence = llm_result.get('documentation_confidence', 0.7)
    
    print(f"Risk Confidence: {risk_confidence}")
    print(f"Documentation Confidence: {doc_confidence}")
    
    if isinstance(risk_confidence, (int, float)) and 0.0 <= risk_confidence <= 1.0:
        print("✅ Risk confidence is valid float between 0.0 and 1.0")
    else:
        print("❌ Risk confidence is not valid")
    
    if isinstance(doc_confidence, (int, float)) and 0.0 <= doc_confidence <= 1.0:
        print("✅ Documentation confidence is valid float between 0.0 and 1.0")
    else:
        print("❌ Documentation confidence is not valid")

if __name__ == "__main__":
    test_llm_critical_analysis()
    test_llm_confidence_extraction()
    
    print("\n🎉 LLM CRITICAL Risk and Confidence Testing Complete!")
    print("The LLM analysis system now supports:")
    print("  - CRITICAL risk level (in addition to HIGH/MEDIUM/LOW)")
    print("  - Probabilistic confidence ratings for all assessments")
    print("  - Enhanced prompts with CRITICAL risk guidance")
    print("  - Proper voting integration between heuristic and LLM analysis")
