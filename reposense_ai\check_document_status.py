#!/usr/bin/env python3
"""
Check the status of document c42f3018-3ffc-4434-91df-e1d1d892bb9e_17
"""

import sys
import os
sys.path.append('/app')

from config_manager import ConfigManager
from document_service import DocumentService
from unified_document_processor import UnifiedDocumentProcessor
from repository_backends.svn_backend import SVNBackend
from models import CommitInfo
from datetime import datetime

def check_document_status():
    """Check the status of the missing document"""
    print("🔍 Checking Document Status")
    print("=" * 60)
    
    doc_id = "c42f3018-3ffc-4434-91df-e1d1d892bb9e_17"
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Create document service
        document_service = DocumentService(
            output_dir="/app/data/output",
            config_manager=config_manager
        )
        
        print(f"1. Checking database for document: {doc_id}")
        
        # Check if document exists in database
        document = document_service.get_document_by_id(doc_id)
        if document:
            print(f"   ✅ Document found in database:")
            print(f"      Repository: {document.repository_name}")
            print(f"      Revision: {document.revision}")
            print(f"      Author: {document.author}")
            print(f"      Size: {document.size} bytes")
            print(f"      File: {document.filepath}")
            
            # Check if file exists
            if os.path.exists(document.filepath):
                file_size = os.path.getsize(document.filepath)
                print(f"   ✅ Document file exists: {file_size} bytes")
            else:
                print(f"   ❌ Document file missing: {document.filepath}")
        else:
            print(f"   ❌ Document NOT found in database")
        
        print(f"\n2. Checking repository configuration...")
        
        # Get repository config
        repo_config = config.get_repository_by_id('c42f3018-3ffc-4434-91df-e1d1d892bb9e')
        if repo_config:
            print(f"   ✅ Repository found: {repo_config.name}")
            print(f"      URL: {repo_config.url}")
            print(f"      Type: {repo_config.type}")
            
            # Check if revision 17 exists in repository
            print(f"\n3. Checking SVN repository for revision 17...")
            backend = SVNBackend(repo_config)
            try:
                commit_info = backend.get_commit_info(repo_config, '17')
                if commit_info:
                    print(f"   ✅ Revision 17 found in repository:")
                    print(f"      Author: {commit_info.author}")
                    print(f"      Message: {commit_info.message}")
                    print(f"      Files: {len(commit_info.changed_paths)} changed")
                    print(f"      Diff: {len(commit_info.diff)} characters")
                    
                    # Offer to recreate the document
                    print(f"\n4. Document can be recreated from repository data")
                    return True
                else:
                    print(f"   ❌ Revision 17 not found in repository")
                    return False
            except Exception as e:
                print(f"   ❌ Error checking repository: {e}")
                return False
        else:
            print(f"   ❌ Repository configuration not found")
            return False
        
    except Exception as e:
        print(f"❌ Error checking document status: {e}")
        import traceback
        traceback.print_exc()
        return False

def recreate_document():
    """Recreate the missing document from repository data"""
    print("\n" + "=" * 60)
    print("🔧 RECREATING DOCUMENT")
    print("=" * 60)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Get repository config
        repo_config = config.get_repository_by_id('c42f3018-3ffc-4434-91df-e1d1d892bb9e')
        
        # Get commit info from repository
        backend = SVNBackend(repo_config)
        commit_info = backend.get_commit_info(repo_config, '17')
        
        if not commit_info:
            print("❌ Cannot get commit info for revision 17")
            return False
        
        print(f"1. Creating CommitInfo for revision 17...")
        print(f"   Repository: {commit_info.repository_name}")
        print(f"   Author: {commit_info.author}")
        print(f"   Message: {commit_info.message}")
        
        # Create unified processor
        print(f"\n2. Creating UnifiedDocumentProcessor...")
        unified_processor = UnifiedDocumentProcessor(
            output_dir="/app/data/output",
            config_manager=config_manager
        )
        
        # Process the commit
        print(f"\n3. Processing commit to recreate document...")
        success = unified_processor.process_commit(
            commit_info=commit_info,
            repository_config=repo_config,
            documentation="",  # Will be regenerated
            priority=10  # High priority
        )
        
        if success:
            print(f"   ✅ Document processing queued successfully")
            
            # Wait for processing to complete
            import time
            print(f"   ⏳ Waiting for processing to complete...")
            
            max_wait = 60
            waited = 0
            while waited < max_wait:
                time.sleep(2)
                waited += 2
                
                # Check if document was created
                # repo_config.name now includes branch path (e.g., "reposense_cpp_test/trunk")
                expected_file = f"/app/data/output/repositories/{repo_config.name}/docs/revision_17.md"
                if os.path.exists(expected_file) and os.path.getsize(expected_file) > 0:
                    file_size = os.path.getsize(expected_file)
                    print(f"   ✅ Document created successfully: {file_size} bytes")
                    return True
                
                # Check queue status
                stats = unified_processor.get_stats()
                if stats['queue_size'] == 0 and stats['active_threads'] == 0:
                    time.sleep(1)  # Final check
                    if os.path.exists(expected_file) and os.path.getsize(expected_file) > 0:
                        file_size = os.path.getsize(expected_file)
                        print(f"   ✅ Document created successfully: {file_size} bytes")
                        return True
            
            print(f"   ❌ Document creation timed out after {max_wait} seconds")
            return False
        else:
            print(f"   ❌ Failed to queue document processing")
            return False
            
    except Exception as e:
        print(f"❌ Error recreating document: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DOCUMENT STATUS CHECK")
    print("=" * 60)
    
    # Check current status
    can_recreate = check_document_status()
    
    if can_recreate:
        print(f"\n📋 SUMMARY:")
        print(f"✅ Repository data available for revision 17")
        print(f"🔧 Document can be recreated from repository")
        print(f"💡 Running recreation process...")
        
        success = recreate_document()
        
        if success:
            print(f"\n🎉 SUCCESS!")
            print(f"✅ Document has been recreated successfully")
            print(f"🌐 You can now use the rescan functionality")
        else:
            print(f"\n❌ FAILED!")
            print(f"❌ Document recreation failed")
            print(f"🔧 Manual intervention may be required")
    else:
        print(f"\n❌ CANNOT RECREATE:")
        print(f"❌ Repository data not available for revision 17")
        print(f"🔧 Manual intervention required")
    
    exit(0 if can_recreate else 1)
