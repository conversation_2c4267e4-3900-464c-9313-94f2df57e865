#!/usr/bin/env python3
"""
File management operations for the RepoSense AI application
Handles file I/O, directory setup, and document saving
"""

import logging
import os
from pathlib import Path
from typing import Optional

from models import Config, CommitInfo


class FileManager:
    """Manager for file operations and directory setup"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def setup_directories(self):
        """Create necessary directories"""
        try:
            Path(self.config.output_dir).mkdir(parents=True, exist_ok=True)

            # Create directories for each repository
            for repo in self.config.repositories:
                repo_dir = self.get_repository_output_dir(repo.id)
                Path(f"{repo_dir}/docs").mkdir(parents=True, exist_ok=True)
                Path(f"{repo_dir}/emails").mkdir(parents=True, exist_ok=True)

            # Legacy directories for backward compatibility
            Path(f"{self.config.output_dir}/docs").mkdir(parents=True, exist_ok=True)
            Path(f"{self.config.output_dir}/emails").mkdir(parents=True, exist_ok=True)

            # Ensure data directory exists (for Docker volume)
            Path("/app/data").mkdir(parents=True, exist_ok=True)

            self.logger.info("Directories setup completed")
        except Exception as e:
            self.logger.error(f"Error setting up directories: {e}")
            raise

    def get_repository_output_dir(self, repo_id: str) -> str:
        """Get output directory for a specific repository"""
        # Sanitize repo_id for filesystem use
        safe_repo_id = "".join(c for c in repo_id if c.isalnum() or c in ('-', '_'))
        return f"{self.config.output_dir}/repositories/{safe_repo_id}"
    
    def save_documentation(self, commit: CommitInfo, documentation: str):
        """Save generated documentation to file"""
        try:
            # Use repository-specific directory if available
            if commit.repository_id:
                repo_dir = self.get_repository_output_dir(commit.repository_id)
                filename = f"{repo_dir}/docs/revision_{commit.revision}_{commit.date[:10]}.md"
            else:
                # Fallback to legacy directory
                filename = f"{self.config.output_dir}/docs/revision_{commit.revision}_{commit.date[:10]}.md"

            # Ensure directory exists
            Path(filename).parent.mkdir(parents=True, exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# Repository Commit Analysis - Revision {commit.revision}\n\n")
                if commit.repository_name:
                    f.write(f"**Repository:** {commit.repository_name}\n")
                f.write(f"**Author:** {commit.author}\n")
                f.write(f"**Date:** {commit.date}\n")
                f.write(f"**Message:** {commit.message}\n\n")
                f.write("## Changed Files\n\n")
                for path in commit.changed_paths:
                    f.write(f"- {path}\n")
                f.write("\n---\n\n")
                f.write("# AI-Generated Analysis\n\n")
                f.write(documentation)
            
            self.logger.info(f"Documentation saved to {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error saving documentation: {e}")
            return None
    
    def save_email_copy(self, subject: str, body: str, commit: CommitInfo):
        """Save a copy of the sent email"""
        try:
            # Use repository-specific directory if available
            if commit.repository_id:
                repo_dir = self.get_repository_output_dir(commit.repository_id)
                email_filename = f"{repo_dir}/emails/revision_{commit.revision}_{commit.date[:10]}.txt"
            else:
                # Fallback to legacy directory
                email_filename = f"{self.config.output_dir}/emails/revision_{commit.revision}_{commit.date[:10]}.txt"

            # Ensure directory exists
            Path(email_filename).parent.mkdir(parents=True, exist_ok=True)

            full_body = f"""
{body}

---
Commit Details:
- Repository: {commit.repository_name or 'Unknown'}
- Revision: {commit.revision}
- Author: {commit.author}
- Date: {commit.date}
- Message: {commit.message}

Changed Files:
{chr(10).join('- ' + path for path in commit.changed_paths)}
"""

            # Get all recipients for this repository
            recipients = self.config.get_all_recipients_for_repository(commit.repository_id)

            with open(email_filename, 'w', encoding='utf-8') as f:
                f.write(f"To: {', '.join(recipients)}\n")
                f.write(f"Subject: {subject}\n\n")
                f.write(full_body)
            
            self.logger.info(f"Email copy saved to {email_filename}")
            return email_filename
            
        except Exception as e:
            self.logger.error(f"Error saving email copy: {e}")
            return None
    
    def get_log_file_path(self) -> str:
        """Get the path to the log file"""
        return '/app/data/reposense_ai.log'

    def cleanup_old_logs(self, max_size_mb: Optional[int] = None) -> bool:
        """Clean up old log files if they exceed the maximum size"""
        try:
            # Use configuration values if not provided
            if max_size_mb is None:
                max_size_mb = self.config.log_cleanup_max_size_mb
            lines_to_keep = self.config.log_cleanup_lines_to_keep

            log_file = self.get_log_file_path()
            if not os.path.exists(log_file):
                return True

            # Check file size
            file_size_mb = os.path.getsize(log_file) / (1024 * 1024)

            if file_size_mb > max_size_mb:
                # Create backup of recent logs before truncation
                backup_file = f"{log_file}.backup"

                # Read all lines
                with open(log_file, 'r') as f:
                    lines = f.readlines()

                # Save full log as backup
                with open(backup_file, 'w') as f:
                    f.writelines(lines)

                # Keep only recent lines in main log
                recent_lines = lines[-lines_to_keep:] if len(lines) > lines_to_keep else lines
                with open(log_file, 'w') as f:
                    f.writelines(recent_lines)

                self.logger.info(f"Log file truncated from {file_size_mb:.1f}MB to {len(recent_lines)} lines. Backup saved to {backup_file}")
                return True

            return True
        except Exception as e:
            self.logger.error(f"Error cleaning up logs: {e}")
            return False
    
    def read_recent_logs(self, lines: int = 100, log_levels: Optional[list] = None) -> list:
        """Read recent log entries with optional log level filtering"""
        try:
            log_file = self.get_log_file_path()
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    all_logs = f.read().split('\n')

                # Filter by log levels if specified
                if log_levels:
                    filtered_logs = []
                    for log_line in all_logs:
                        if self._matches_log_levels(log_line, log_levels):
                            filtered_logs.append(log_line)
                    logs = filtered_logs[-lines:] if filtered_logs else ['No logs match the selected filters']
                else:
                    logs = all_logs[-lines:]

                return logs
            else:
                return ['No logs available']
        except Exception as e:
            self.logger.error(f"Error reading logs: {e}")
            return [f'Error reading logs: {str(e)}']

    def _matches_log_levels(self, log_line: str, log_levels: list) -> bool:
        """Check if a log line matches any of the specified log levels"""
        if not log_line.strip():
            return False

        # Convert log levels to uppercase for comparison
        log_levels_upper = [level.upper() for level in log_levels]

        # Use regex to extract the log level from the specific position
        # Format: "YYYY-MM-DD HH:MM:SS,mmm - LEVEL - message"
        import re
        match = re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - (\w+) - ', log_line)
        if match:
            actual_level = match.group(1).upper()
            return actual_level in log_levels_upper

        return False

    def get_available_log_levels(self, count_from_full_file: bool = True) -> dict:
        """Get available log levels with counts from either full file or recent entries"""
        try:
            log_file = self.get_log_file_path()
            if not os.path.exists(log_file):
                return {}

            level_counts = {
                'DEBUG': 0,
                'INFO': 0,
                'WARNING': 0,
                'ERROR': 0,
                'CRITICAL': 0
            }

            import re

            if count_from_full_file:
                # Count from entire log file for accurate totals
                with open(log_file, 'r') as f:
                    for log_line in f:
                        if not log_line.strip():
                            continue

                        match = re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - (\w+) - ', log_line)
                        if match:
                            actual_level = match.group(1).upper()
                            if actual_level in level_counts:
                                level_counts[actual_level] += 1
            else:
                # Count from recent entries only (for backwards compatibility)
                with open(log_file, 'r') as f:
                    recent_logs = f.read().split('\n')[-1000:]

                for log_line in recent_logs:
                    if not log_line.strip():
                        continue

                    match = re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - (\w+) - ', log_line)
                    if match:
                        actual_level = match.group(1).upper()
                        if actual_level in level_counts:
                            level_counts[actual_level] += 1

            return level_counts
        except Exception as e:
            self.logger.error(f"Error getting log levels: {e}")
            return {}
