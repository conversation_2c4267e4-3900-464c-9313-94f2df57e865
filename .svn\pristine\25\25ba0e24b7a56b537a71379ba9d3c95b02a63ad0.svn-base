#!/usr/bin/env python3
"""
Main monitoring service that orchestrates all components
Coordinates repository monitoring, content generation, and notifications
"""

import logging
import sys
import threading
import time
from datetime import datetime
from pathlib import Path

from config_manager import ConfigManager
from email_service import EmailService
from file_manager import FileManager
from models import Config, RepositoryConfig
from ollama_client import OllamaClient
from repository_backends import get_backend_manager
from unified_document_processor import UnifiedDocumentProcessor
from document_database import DocumentDatabase
from metadata_extractor import MetadataExtractor

from typing import Optional


class MonitorService:
    """Main service that orchestrates repository monitoring and processing"""

    unified_processor: Optional[UnifiedDocumentProcessor]

    def __init__(self, config_path: str = "/app/data/config.json"):
        self.config_path = config_path
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.load_config()
        
        self.setup_logging()
        
        # Initialize components
        self.backend_manager = get_backend_manager()
        self.ollama_client = OllamaClient(self.config)
        self.email_service = EmailService(self.config)
        self.file_manager = FileManager(self.config)


        # Initialize unified document processor for database integration
        try:
            self.unified_processor = UnifiedDocumentProcessor(
                output_dir=self.config.output_dir,
                db_path="/app/data/documents.db",
                ollama_client=self.ollama_client,
                config_manager=self.config_manager,
                max_concurrent_tasks=2  # Lower concurrency for monitor service
            )
            self.unified_processor.start()
            self.logger.info("✅ Unified document processor initialized for monitor service")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize unified document processor: {e}")
            self.unified_processor = None

        # Setup directories
        self.file_manager.setup_directories()
        
        # Monitoring state
        self.running = False
        self.monitor_thread = None
        self.last_check_time = None

        # Cached status to avoid slow dashboard loads
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_cache_duration = 30  # Cache for 30 seconds

        # Cached models list
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None
        self.ollama_models_cache_duration = 300  # Cache models for 5 minutes

        self.logger.info("Monitor service initialized")
    
    def setup_logging(self):
        """Setup logging configuration with rotation"""
        from logging.handlers import RotatingFileHandler

        handlers = [logging.StreamHandler(sys.stdout)]

        # Get rotation settings from configuration
        max_bytes = self.config.log_rotation_max_size_mb * 1024 * 1024  # Convert MB to bytes
        backup_count = self.config.log_rotation_backup_count

        # Try to add rotating file handler, but don't fail if directory doesn't exist
        try:
            log_dir = Path('/app/data')
            log_dir.mkdir(parents=True, exist_ok=True)

            # Use RotatingFileHandler with configurable size and backup count
            rotating_handler = RotatingFileHandler(
                '/app/data/reposense_ai.log',
                maxBytes=max_bytes,
                backupCount=backup_count
            )
            handlers.append(rotating_handler)
        except (OSError, PermissionError):
            # Fallback to local directory or temp directory
            try:
                log_dir = Path('./data')
                log_dir.mkdir(parents=True, exist_ok=True)

                rotating_handler = RotatingFileHandler(
                    './data/reposense_ai.log',
                    maxBytes=max_bytes,
                    backupCount=backup_count
                )
                handlers.append(rotating_handler)
            except (OSError, PermissionError):
                # Just use console logging if file logging fails
                pass

        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        self.logger = logging.getLogger(__name__)
    
    def reload_config(self):
        """Reload configuration and update components"""
        self.config = self.config_manager.load_config()

        # Update components with new config
        self.ollama_client.config = self.config
        self.email_service.config = self.config
        self.file_manager.config = self.config

        # Clear cached status to force refresh with new config
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None

        self.logger.info("Configuration reloaded")
    
    def save_config(self):
        """Save current configuration"""
        self.config_manager.save_config(self.config)
    
    def update_config(self, new_config: Config):
        """Update configuration and save it"""
        self.config = new_config
        self.save_config()
        self.reload_config()
    
    def process_commit(self, repo: 'RepositoryConfig', revision: str) -> bool:
        """Process a single commit for a specific repository

        Returns:
            bool: True if processing completed successfully, False otherwise
        """
        backend = self.backend_manager.get_backend_for_repository(repo, self.config)
        if not backend:
            self.logger.error(f"No backend available for repository {repo.name}")
            return False

        commit = backend.get_commit_info(repo, revision)
        if not commit:
            self.logger.error(f"Could not get commit info for revision {revision} in {repo.name}")
            return False

        self.logger.info(f"Processing commit {revision} by {commit.author} in {repo.name}")

        # Generate documentation
        documentation_success = True
        if self.config.generate_docs:
            try:
                documentation = self.ollama_client.generate_documentation(commit)
                if documentation:
                    if documentation.startswith("Error:"):
                        self.logger.error(f"Documentation generation failed for revision {revision} in {repo.name}: {documentation}")
                        documentation_success = False
                    else:
                        saved_path = self.file_manager.save_documentation(commit, documentation)
                        if saved_path:
                            self.logger.info(f"Documentation successfully generated and saved for revision {revision}")

                            # Verify the file was actually created and is readable
                            import os
                            if os.path.exists(saved_path) and os.path.getsize(saved_path) > 0:
                                self.logger.debug(f"✅ Verified document file exists: {saved_path}")

                                # Use unified processor to handle database insertion and metadata extraction
                                # This ensures consistency with historical scanner processing
                                if hasattr(self, 'unified_processor') and self.unified_processor:
                                    try:
                                        success = self.unified_processor.process_commit(
                                            commit_info=commit,
                                            repository_config=repo,
                                            documentation=documentation,
                                            priority=5  # Normal priority for real-time commits
                                        )
                                        if not success:
                                            self.logger.error(f"Failed to process commit {revision} through unified processor")
                                            documentation_success = False
                                    except Exception as e:
                                        self.logger.error(f"Exception during unified processor handling for revision {revision}: {e}")
                                        documentation_success = False
                                else:
                                    self.logger.warning(f"Unified processor not available - document {revision} saved to file but not processed for database")
                            else:
                                self.logger.error(f"❌ Document file verification failed: {saved_path}")
                                documentation_success = False
                        else:
                            self.logger.error(f"Documentation generated but failed to save for revision {revision} - check file permissions and disk space")
                            documentation_success = False
                else:
                    self.logger.warning(f"No documentation generated for revision {revision} - this may indicate an Ollama service issue or model unavailability")
                    documentation_success = False
            except Exception as e:
                self.logger.error(f"Critical error during documentation generation for revision {revision} in {repo.name}: {e}")
                documentation_success = False
        else:
            self.logger.debug("Documentation generation disabled")

        # Generate and send email
        email_success = True
        if self.config.send_emails and self.email_service.is_configured():
            try:
                subject, body = self.ollama_client.generate_email_content(commit)
                if subject and body:
                    if self.email_service.send_email(subject, body, commit):
                        self.file_manager.save_email_copy(subject, body, commit)
                    else:
                        email_success = False
            except Exception as e:
                self.logger.error(f"Error sending email for revision {revision}: {e}")
                email_success = False

        # Return success only if all enabled features completed successfully
        overall_success = documentation_success and email_success
        if overall_success:
            self.logger.info(f"✅ Commit {revision} processing completed successfully")
        else:
            self.logger.warning(f"⚠️ Commit {revision} processing completed with some failures")

        return overall_success
    
    def check_for_new_commits(self):
        """Check for new commits and process them across all enabled repositories"""
        # Get enabled repositories and expand any with monitor_all_branches=True
        enabled_repos = self.config.get_enabled_repositories()

        if not enabled_repos:
            self.logger.warning("No enabled repositories to monitor")
            return

        # Use enabled repositories directly
        effective_repos = enabled_repos

        self.logger.info(f"Monitoring {len(effective_repos)} effective repositories (expanded from {len(enabled_repos)} configured repositories)")

        for repo in effective_repos:
            if not self.running:  # Check if we should stop
                break

            self.check_repository_for_new_commits(repo)

    def check_repository_for_new_commits(self, repo: RepositoryConfig):
        """Check for new commits in a specific repository"""
        try:
            backend = self.backend_manager.get_backend_for_repository(repo, self.config)
            if not backend:
                self.logger.error(f"No backend available for repository {repo.name}")
                return

            latest_revision_str = backend.get_latest_revision(repo)
            if not latest_revision_str:
                self.logger.warning(f"Could not get latest revision for {repo.name}")
                return

            # Convert to int for SVN (other backends may need different handling)
            try:
                latest_revision = int(latest_revision_str)
            except ValueError:
                self.logger.error(f"Invalid revision format for {repo.name}: {latest_revision_str}")
                return

            if repo.last_revision == 0:
                # First run for this repository, just set the current revision
                repo.last_revision = latest_revision
                repo.last_processed_time = datetime.now()

                # Get the commit date for the latest revision
                commit_info = backend.get_commit_info(repo, latest_revision_str)
                if commit_info and commit_info.date:
                    try:
                        # Parse the commit date (SVN format: 2025-08-02T20:14:16.000000Z)
                        commit_date_str = commit_info.date.replace('Z', '+00:00')
                        repo.last_commit_date = datetime.fromisoformat(commit_date_str.replace('T', ' ').split('+')[0])
                    except (ValueError, AttributeError) as e:
                        self.logger.warning(f"Could not parse commit date for {repo.name}: {e}")

                self.save_config()
                self.logger.info(f"Initial setup for {repo.name}: setting last revision to {latest_revision}")
                return

            if latest_revision > repo.last_revision:
                new_commits = latest_revision - repo.last_revision
                self.logger.info(f"Found {new_commits} new commits in {repo.name}")

                # Process each new commit individually and update last_revision after each success
                for revision in range(repo.last_revision + 1, latest_revision + 1):
                    if not self.running:  # Check if we should stop
                        break

                    try:
                        # Process the commit and wait for completion
                        processing_success = self.process_commit(repo, str(revision))

                        if processing_success:
                            # Only update last_revision AFTER successful processing
                            repo.last_revision = revision
                            repo.last_processed_time = datetime.now()

                            # Get the commit date for this revision
                            commit_info = backend.get_commit_info(repo, str(revision))
                            if commit_info and commit_info.date:
                                try:
                                    # Parse the commit date (SVN format: 2025-08-02T20:14:16.000000Z)
                                    commit_date_str = commit_info.date.replace('Z', '+00:00')
                                    repo.last_commit_date = datetime.fromisoformat(commit_date_str.replace('T', ' ').split('+')[0])
                                except (ValueError, AttributeError) as e:
                                    self.logger.warning(f"Could not parse commit date for {repo.name}: {e}")

                            # Save config after each successful revision
                            self.save_config()
                            self.logger.info(f"✅ Successfully processed and saved revision {revision} for {repo.name}")
                        else:
                            self.logger.error(f"❌ Processing failed for revision {revision} in {repo.name} - not updating last_revision")
                            # Don't update last_revision if processing failed, but continue with next revision
                            # This allows the system to retry this revision on the next monitoring cycle

                    except Exception as e:
                        self.logger.error(f"❌ Exception during processing of revision {revision} for {repo.name}: {e}")
                        # Don't update last_revision if processing failed
                        break

        except Exception as e:
            self.logger.error(f"Error checking repository {repo.name}: {e}")
    
    def run_once(self):
        """Run a single check cycle"""
        try:
            self.logger.info("Checking for new commits...")
            self.check_for_new_commits()
        except Exception as e:
            self.logger.error(f"Error during check cycle: {e}")
    
    def run_daemon(self):
        """Run continuously as a daemon"""
        self.logger.info(f"Starting RepoSense AI daemon (checking every {self.config.check_interval} seconds)")
        
        while self.running:
            try:
                self.run_once()
                self.last_check_time = datetime.now().isoformat()
                time.sleep(self.config.check_interval)
            except Exception as e:
                self.logger.error(f"Unexpected error in daemon loop: {e}")
                time.sleep(60)  # Wait a minute before retrying
        
        self.logger.info("Daemon stopped")
    
    def start_monitoring(self):
        """Start the monitoring process in a separate thread"""
        if not self.running:
            self.running = True
            self.monitor_thread = threading.Thread(target=self.run_daemon, daemon=True)
            self.monitor_thread.start()
            self.logger.info("Monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring process"""
        if self.running:
            self.running = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)

            # Stop unified processor if it exists
            if self.unified_processor:
                try:
                    self.unified_processor.stop()
                    self.logger.info("✅ Unified document processor stopped")
                except Exception as e:
                    self.logger.error(f"❌ Error stopping unified document processor: {e}")

            self.logger.info("Monitoring stopped")
    
    def is_running(self):
        """Check if monitoring is currently running"""
        return self.running and self.monitor_thread and self.monitor_thread.is_alive()

    def get_ollama_connection_status(self):
        """Get Ollama connection status with caching to avoid slow dashboard loads"""
        current_time = time.time()

        # Return cached result if still valid
        if (self.ollama_connected_cache is not None and
            self.ollama_cache_time is not None and
            current_time - self.ollama_cache_time < self.ollama_cache_duration):
            return self.ollama_connected_cache

        # Test connection and cache result
        try:
            self.ollama_connected_cache = self.ollama_client.test_connection()
            self.ollama_cache_time = current_time
            return self.ollama_connected_cache
        except Exception as e:
            self.logger.warning(f"Error testing Ollama connection: {e}")
            self.ollama_connected_cache = False
            self.ollama_cache_time = current_time
            return False

    def get_available_models(self):
        """Get available Ollama models with caching"""
        current_time = time.time()

        # Return cached result if still valid
        if (self.ollama_models_cache is not None and
            self.ollama_models_cache_time is not None and
            current_time - self.ollama_models_cache_time < self.ollama_models_cache_duration):
            return self.ollama_models_cache

        # Get models and cache result
        try:
            models = self.ollama_client.get_available_models()
            self.ollama_models_cache = models
            self.ollama_models_cache_time = current_time
            return models
        except Exception as e:
            self.logger.warning(f"Error getting Ollama models: {e}")
            # Return cached result if available, otherwise empty list
            return self.ollama_models_cache if self.ollama_models_cache is not None else []
    
    def get_status(self):
        """Get current status information"""
        enabled_repos = self.config.get_enabled_repositories()

        # Repository status information
        repositories_status = []
        for repo in self.config.repositories:
            repositories_status.append({
                "id": repo.id,
                "name": repo.name,
                "url": repo.url,
                "enabled": repo.enabled,
                "last_revision": repo.last_revision,
                "last_commit_date": repo.last_commit_date.isoformat() if repo.last_commit_date else None,
                "last_processed_time": repo.last_processed_time.isoformat() if repo.last_processed_time else None
            })



        return {
            "running": self.is_running(),
            "repositories": repositories_status,
            "enabled_repositories_count": len(enabled_repos),
            "total_repositories_count": len(self.config.repositories),
            "config_valid": len(enabled_repos) > 0,
            "ollama_connected": self.get_ollama_connection_status(),
            "last_check": self.last_check_time,
            "email_configured": self.email_service.is_configured(),

        }
