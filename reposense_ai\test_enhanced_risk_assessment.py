#!/usr/bin/env python3
"""
Test Enhanced Risk Assessment Features

Tests the new features:
1. Repository-specific aggressiveness configuration
2. Diff complexity analysis
3. Multi-factor voting system (heuristic + LLM + diff complexity)
"""

import sys
import logging
from config_manager import ConfigManager
from metadata_extractor import MetadataExtractor
from diff_complexity_analyzer import DiffComplexityAnalyzer
from models import RepositoryConfig
from document_database import DocumentRecord
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_aggressiveness_thresholds():
    """Test that different aggressiveness levels produce different thresholds"""
    print("🧪 Testing Aggressiveness Threshold Configuration...")
    
    extractor = MetadataExtractor()
    
    # Test all aggressiveness levels
    levels = ["CONSERVATIVE", "BALANCED", "AGGRESSIVE", "VERY_AGGRESSIVE"]
    
    for level in levels:
        critical, high, medium = extractor._get_risk_thresholds(level)
        print(f"{level:15}: CRITICAL≥{critical:3.1f}, HIGH≥{high:3.1f}, MEDIUM≥{medium:3.1f}")
    
    print("✅ Threshold configuration working correctly\n")

def test_diff_complexity_analyzer():
    """Test the diff complexity analyzer"""
    print("🧪 Testing Diff Complexity Analyzer...")
    
    analyzer = DiffComplexityAnalyzer()
    
    # Test with sample diff content
    sample_diff = """
diff --git a/src/main.cpp b/src/main.cpp
index 1234567..abcdefg 100644
--- a/src/main.cpp
+++ b/src/main.cpp
@@ -10,6 +10,15 @@
 #include <iostream>
 #include <vector>
 
+class SecurityManager {
+public:
+    bool authenticate(const std::string& password) {
+        if (password == "admin123") {
+            return true;
+        }
+        return false;
+    }
+};
+
 int main() {
     std::cout << "Hello World" << std::endl;
+    SecurityManager sm;
+    if (sm.authenticate("test")) {
+        std::cout << "Authenticated!" << std::endl;
+    }
     return 0;
 }
"""
    
    file_paths = ["src/main.cpp", "include/security.h"]
    
    metrics, confidence = analyzer.analyze_diff(sample_diff, file_paths)
    
    print(f"Lines added: {metrics.lines_added}")
    print(f"Lines removed: {metrics.lines_removed}")
    print(f"Files changed: {metrics.files_changed}")
    print(f"New classes: {metrics.new_classes}")
    print(f"Code files: {metrics.code_files}")
    print(f"Complexity score: {metrics.complexity_score():.3f}")
    print(f"Analysis confidence: {confidence:.3f}")
    
    # Test risk assessment with different aggressiveness levels
    for level in ["CONSERVATIVE", "BALANCED", "AGGRESSIVE"]:
        risk, risk_conf = analyzer.get_risk_assessment(metrics, level)
        print(f"{level:12}: {risk:8} (confidence: {risk_conf:.3f})")
    
    print("✅ Diff complexity analyzer working correctly\n")

def test_repository_aggressiveness_retrieval():
    """Test retrieving repository aggressiveness settings"""
    print("🧪 Testing Repository Aggressiveness Retrieval...")
    
    try:
        config_manager = ConfigManager('data/config.json')
        extractor = MetadataExtractor(config_manager=config_manager)
        
        # Create a mock document record
        mock_doc = DocumentRecord(
            id="test-doc",
            repository_id="test-repo",
            repository_name="reposense_cpp_test",
            revision="1",
            date=datetime.now(),
            filename="test.cpp",
            filepath="/test.cpp",
            size=1000,
            author="test",
            commit_message="test commit"
        )
        
        aggressiveness = extractor._get_repository_aggressiveness(mock_doc)
        print(f"Retrieved aggressiveness for reposense_cpp_test: {aggressiveness}")
        
        # Test threshold retrieval
        critical, high, medium = extractor._get_risk_thresholds(aggressiveness)
        print(f"Thresholds: CRITICAL≥{critical}, HIGH≥{high}, MEDIUM≥{medium}")
        
        print("✅ Repository aggressiveness retrieval working correctly\n")
        
    except Exception as e:
        print(f"❌ Error testing aggressiveness retrieval: {e}\n")

def test_voting_system():
    """Test the enhanced voting system with multiple factors"""
    print("🧪 Testing Enhanced Voting System...")
    
    try:
        config_manager = ConfigManager('data/config.json')
        extractor = MetadataExtractor(config_manager=config_manager)
        
        # Test sample content with risk keywords
        test_content = """
        This commit introduces critical security fixes for authentication vulnerabilities.
        
        Changes include:
        - Fixed race condition in login system
        - Added proper password hashing
        - Implemented secure session management
        - Updated cryptographic functions
        
        This is a hotfix for production deployment.
        """
        
        # Create mock document record
        mock_doc = DocumentRecord(
            id="test-doc",
            repository_id="test-repo", 
            repository_name="reposense_cpp_test",
            revision="test",
            date=datetime.now(),
            filename="auth.cpp",
            filepath="/src/auth.cpp",
            size=2000,
            author="developer",
            commit_message="Critical security fixes",
            changed_paths=["src/auth.cpp", "include/crypto.h", "config/security.json"]
        )
        
        # Test risk level extraction with new voting system
        risk_level = extractor.extract_risk_level(test_content, mock_doc)
        print(f"Final risk assessment: {risk_level}")
        
        print("✅ Enhanced voting system test completed\n")
        
    except Exception as e:
        print(f"❌ Error testing voting system: {e}\n")

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced Risk Assessment Features")
    print("=" * 60)
    
    test_aggressiveness_thresholds()
    test_diff_complexity_analyzer()
    test_repository_aggressiveness_retrieval()
    test_voting_system()
    
    print("🎯 All tests completed!")

if __name__ == "__main__":
    main()
