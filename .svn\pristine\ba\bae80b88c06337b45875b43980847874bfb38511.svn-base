services:
  reposense-ai:
    build:
      context: ./reposense_ai
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5001:5000"
    volumes:
      # Essential volumes - always mounted for data persistence
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
      # Development volumes - mount source code for hot reloads
      - ./reposense_ai:/app
      # Exclude build artifacts to avoid conflicts (but keep data visible)
      - /app/node_modules
      - /app/.git
    environment:
      # Web interface settings (minimal environment)
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      # Development settings (override via .env file)
      - REPOSENSE_AI_LOG_LEVEL=${REPOSENSE_AI_LOG_LEVEL:-INFO}
      - REPOSENSE_AI_DB_DEBUG=${REPOSENSE_AI_DB_DEBUG:-false}
      # Standalone mode - use external Ollama service (uncomment to override web config)
      # - OLLAMA_BASE_URL=http://localhost:11434
      # Optional deployment overrides (uncomment if needed):
      # - OLLAMA_MODEL=qwen3
    networks:
      - reposense-ai-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "sundc:***********"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mailhog:
    image: mailhog/mailhog:latest
    container_name: reposense-ai-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - reposense-ai-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

# Network for standalone operation
networks:
  reposense-ai-network:
    driver: bridge
