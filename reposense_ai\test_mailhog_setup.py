#!/usr/bin/env python3
"""
Test script to verify MailHog configuration with admin and manager user creation
"""

import sys
import os
import json
import requests
sys.path.append('/app')

from config_manager import ConfigManager
from models import UserRole
from user_management_service import UserManagementService

def test_mailhog_configuration():
    """Test MailHog email configuration setup"""
    print("📧 Testing MailHog Configuration Setup")
    print("=" * 50)
    
    try:
        # Load current configuration
        config_manager = ConfigManager('/app/data/config.json')
        config = config_manager.load_config()
        
        print("📋 Current Email Configuration:")
        print(f"   SMTP Host: {config.smtp_host}")
        print(f"   SMTP Port: {config.smtp_port}")
        print(f"   From Address: {config.email_from}")
        print(f"   Global Recipients: {config.email_recipients}")
        print()
        
        # Test MailHog configuration
        print("🔧 Setting up MailHog configuration...")
        config.smtp_host = "mailhog"
        config.smtp_port = 1025
        config.smtp_username = None
        config.smtp_password = None
        config.email_from = "reposense-ai@localhost"
        config.email_recipients = ["<EMAIL>", "<EMAIL>"]
        
        print("✅ MailHog configuration applied:")
        print(f"   SMTP Host: {config.smtp_host}")
        print(f"   SMTP Port: {config.smtp_port}")
        print(f"   From Address: {config.email_from}")
        print(f"   Global Recipients: {config.email_recipients}")
        
        # Save configuration
        config_manager.save_config(config)
        print("✅ Configuration saved")
        
    except Exception as e:
        print(f"❌ Error configuring MailHog: {e}")

def test_user_creation():
    """Test creating admin and manager users"""
    print(f"\n👥 Testing Admin and Manager User Creation")
    print("=" * 50)
    
    try:
        # Load configuration and initialize user service
        config_manager = ConfigManager('/app/data/config.json')
        config = config_manager.load_config()
        user_service = UserManagementService(config)
        
        # Define default users
        default_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'full_name': 'System Administrator',
                'role': UserRole.ADMIN,
                'receive_all_notifications': True
            },
            {
                'username': 'manager',
                'email': '<EMAIL>',
                'full_name': 'Project Manager',
                'role': UserRole.MANAGER,
                'receive_all_notifications': True
            }
        ]
        
        created_users = []
        
        for user_data in default_users:
            print(f"\n🔨 Creating user: {user_data['username']}")
            
            # Check if user already exists
            existing_user = config.get_user_by_email(user_data['email'])
            if existing_user:
                print(f"   ℹ️  User {user_data['username']} already exists")
                print(f"   📧 Email: {existing_user.email}")
                print(f"   👤 Role: {existing_user.role.value}")
                print(f"   🔔 Receive All Notifications: {existing_user.receive_all_notifications}")
                continue
            
            # Create user
            success, message, user = user_service.create_user(**user_data)
            
            if success:
                print(f"   ✅ User created successfully")
                print(f"   📧 Email: {user.email}")
                print(f"   👤 Role: {user.role.value}")
                print(f"   🔔 Receive All Notifications: {user.receive_all_notifications}")
                created_users.append(user)
            else:
                print(f"   ❌ Failed to create user: {message}")
        
        # Save configuration if users were created
        if created_users:
            config_manager.save_config(config)
            print(f"\n✅ Configuration saved with {len(created_users)} new users")
        
        # Display all users
        print(f"\n📊 Current Users Summary:")
        for user in config.users:
            if user.enabled:
                print(f"   👤 {user.username} ({user.email}) - {user.role.value}")
                if user.receive_all_notifications:
                    print(f"      🔔 Receives all notifications")
        
    except Exception as e:
        print(f"❌ Error creating users: {e}")

def test_email_integration():
    """Test email integration with created users"""
    print(f"\n📬 Testing Email Integration")
    print("=" * 40)
    
    try:
        # Load configuration
        config_manager = ConfigManager('/app/data/config.json')
        config = config_manager.load_config()
        
        print("📋 Email Recipients Analysis:")
        print(f"   Global Recipients (legacy): {config.email_recipients}")
        
        # Get all recipients for a test repository
        all_recipients = config.get_all_recipients_for_repository("test-repo")
        print(f"   All Recipients (users + global): {all_recipients}")
        
        # Check user notification settings
        admin_users = [u for u in config.users if u.role == UserRole.ADMIN and u.enabled]
        manager_users = [u for u in config.users if u.role == UserRole.MANAGER and u.enabled]
        
        print(f"\n👑 Admin Users:")
        for user in admin_users:
            print(f"   📧 {user.email} - Notifications: {'✅' if user.receive_all_notifications else '❌'}")
        
        print(f"\n👔 Manager Users:")
        for user in manager_users:
            print(f"   📧 {user.email} - Notifications: {'✅' if user.receive_all_notifications else '❌'}")
        
        # Test MailHog connectivity
        print(f"\n🔗 Testing MailHog Connectivity:")
        try:
            response = requests.get('http://localhost:8025/api/v1/messages', timeout=5)
            if response.status_code == 200:
                messages = response.json()
                print(f"   ✅ MailHog API accessible")
                print(f"   📨 Current messages in MailHog: {len(messages)}")
            else:
                print(f"   ⚠️  MailHog API returned status: {response.status_code}")
        except Exception as e:
            print(f"   ❌ MailHog not accessible: {e}")
            print(f"   💡 Make sure MailHog container is running")
        
    except Exception as e:
        print(f"❌ Error testing email integration: {e}")

def test_web_interface_integration():
    """Test web interface integration"""
    print(f"\n🌐 Testing Web Interface Integration")
    print("=" * 45)
    
    try:
        # Test configuration page access
        response = requests.get('http://localhost:5000/config', timeout=10)
        if response.status_code == 200:
            print("✅ Configuration page accessible")
            
            # Check for MailHog button and JavaScript functions
            if 'setMailHogConfig' in response.text:
                print("✅ MailHog configuration function found")
            else:
                print("❌ MailHog configuration function NOT found")
            
            if 'createDefaultUsers' in response.text:
                print("✅ User creation function found")
            else:
                print("❌ User creation function NOT found")
            
            if '<EMAIL>, <EMAIL>' in response.text:
                print("✅ Default recipients integration found")
            else:
                print("❌ Default recipients integration NOT found")
        else:
            print(f"❌ Configuration page not accessible: {response.status_code}")
        
        # Test users page
        response = requests.get('http://localhost:5000/users', timeout=10)
        if response.status_code == 200:
            print("✅ Users page accessible")
        else:
            print(f"❌ Users page not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing web interface: {e}")

if __name__ == "__main__":
    test_mailhog_configuration()
    test_user_creation()
    test_email_integration()
    test_web_interface_integration()
    
    print(f"\n🎉 MailHog Setup Tests Completed!")
    print(f"📧 MailHog Web UI: http://localhost:8025")
    print(f"⚙️  Configuration Page: http://localhost:5000/config")
    print(f"👥 Users Page: http://localhost:5000/users")
    print(f"\n🚀 Ready for email testing with admin and manager accounts!")
