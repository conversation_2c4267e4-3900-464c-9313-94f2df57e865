#!/usr/bin/env python3
"""
Debug Today Query

Test the exact query that the Today button generates
"""

import sqlite3
from datetime import datetime, timedelta

def debug_today_query():
    """Debug the Today button query"""
    print("🔍 Debugging Today Button Query")
    print("=" * 50)
    
    # Test the exact same query that the Today button would generate
    today = '2025-08-19'
    print(f"Testing Today button query: {today} to {today}")
    
    conn = sqlite3.connect('/app/data/documents.db')
    cursor = conn.cursor()
    
    # This is what the backend should be doing for Today button
    from_param = f'{today}T00:00:00'
    end_date = datetime.strptime(today, '%Y-%m-%d') + timedelta(days=1)
    to_param = end_date.strftime('%Y-%m-%dT%H:%M:%S')
    
    print(f"Database query: processed_time >= '{from_param}' AND processed_time < '{to_param}'")
    
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time >= ? AND processed_time < ?', (from_param, to_param))
    count = cursor.fetchone()[0]
    print(f"Result: {count} documents")
    
    # Show some sample data for comparison
    cursor.execute('SELECT processed_time FROM documents LIMIT 3')
    results = cursor.fetchall()
    print(f"Sample processing dates:")
    for row in results:
        print(f"  {row[0]}")
        
    # Check if any documents match the date pattern
    cursor.execute('SELECT COUNT(*) FROM documents WHERE processed_time LIKE ?', (f'{today}%',))
    like_count = cursor.fetchone()[0]
    print(f"Documents with LIKE '{today}%': {like_count}")
    
    # Test the range boundaries
    print(f"\nTesting range boundaries:")
    print(f"  From: {from_param}")
    print(f"  To: {to_param}")
    
    # Check if any documents fall in this range
    cursor.execute('SELECT processed_time FROM documents WHERE processed_time >= ? AND processed_time < ? LIMIT 3', (from_param, to_param))
    range_results = cursor.fetchall()
    print(f"Documents in range:")
    for row in range_results:
        print(f"  {row[0]}")
    
    conn.close()

if __name__ == "__main__":
    debug_today_query()
