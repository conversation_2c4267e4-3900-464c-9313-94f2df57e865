#!/usr/bin/env python3
"""
Check the database directly for reposense_cpp_test revisions
"""

import sqlite3
import os

def check_database_direct():
    """Check database directly for reposense_cpp_test revisions"""
    print("🔍 Checking database directly for reposense_cpp_test revisions...")
    
    try:
        db_path = "reposense_ai/data/documents.db"
        
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check all reposense_cpp_test documents
        cursor.execute("""
            SELECT repository_name, revision, date, author, commit_message, processed_time
            FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%' 
            ORDER BY revision ASC
        """)
        
        docs = cursor.fetchall()
        print(f"\n📋 All reposense_cpp_test documents in database ({len(docs)} total):")
        
        revisions_found = []
        for doc in docs:
            repo_name, revision, date, author, commit_msg, processed_time = doc
            revisions_found.append(revision)
            status = "✅ REVISION 9!" if revision == 9 else ""
            print(f"  Revision {revision}: {commit_msg[:50]}{'...' if len(commit_msg) > 50 else ''} {status}")
            print(f"    Date: {date}, Author: {author}")
            print(f"    Processed: {processed_time}")
            print()
        
        # Summary
        if revisions_found:
            min_rev = min(revisions_found)
            max_rev = max(revisions_found)
            print(f"📊 Summary:")
            print(f"  Total revisions in DB: {len(revisions_found)}")
            print(f"  Revision range: {min_rev} to {max_rev}")
            print(f"  Revisions found: {sorted(revisions_found)}")
            
            if 9 in revisions_found:
                print(f"  ✅ Revision 9 IS in the database")
            else:
                print(f"  ❌ Revision 9 is NOT in the database")
                if max_rev < 9:
                    print(f"  ⏳ System has only processed up to revision {max_rev}")
                    print(f"  🔄 Revision 9 may still be pending processing")
        else:
            print(f"❌ No reposense_cpp_test documents found in database")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        import traceback
        traceback.print_exc()

def check_repository_config():
    """Check what repositories are configured"""
    print(f"\n🔧 Checking repository configuration...")
    
    try:
        config_path = "reposense_ai/data/config.json"
        
        if not os.path.exists(config_path):
            print(f"❌ Config file not found at {config_path}")
            return
            
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        repositories = config.get('repositories', [])
        print(f"📋 Configured repositories ({len(repositories)} total):")
        
        for repo in repositories:
            name = repo.get('name', 'Unknown')
            url = repo.get('url', 'Unknown')
            enabled = repo.get('enabled', False)
            last_revision = repo.get('last_revision', 0)
            repo_type = repo.get('type', 'Unknown')
            
            print(f"  - {name} ({repo_type})")
            print(f"    URL: {url}")
            print(f"    Enabled: {'✅' if enabled else '❌'}")
            print(f"    Last revision: {last_revision}")
            
            if 'reposense_cpp_test' in name.lower():
                print(f"    🎯 This is the reposense_cpp_test repository!")
                if enabled:
                    print(f"    ✅ Repository is enabled for monitoring")
                    if last_revision >= 9:
                        print(f"    ✅ Last revision ({last_revision}) >= 9, should be processed")
                    else:
                        print(f"    ⏳ Last revision ({last_revision}) < 9, revision 9 not yet detected")
                else:
                    print(f"    ❌ Repository is disabled - won't be scanned")
            print()
            
    except Exception as e:
        print(f"❌ Error checking config: {e}")

if __name__ == "__main__":
    check_database_direct()
    check_repository_config()
    
    print(f"\n💡 Next Steps:")
    print(f"  1. If revision 9 is not in DB but repo is enabled: Wait for next scan cycle")
    print(f"  2. If repo is disabled: Enable it in the web interface")
    print(f"  3. If last_revision < 9: The system hasn't detected revision 9 yet")
    print(f"  4. Check the repository URL to ensure revision 9 actually exists")
