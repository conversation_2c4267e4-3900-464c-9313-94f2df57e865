#!/usr/bin/env python3
"""
Test that specialized models are actually being used in practice
"""

import logging
from datetime import datetime
from document_service import DocumentService
from document_database import DocumentRecord
from monitor_service import MonitorService

def setup_logging():
    """Setup logging to see specialized model usage"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def create_test_document_record():
    """Create a test document record for testing"""
    return DocumentRecord(
        id="test-specialized-models",
        repository_id="test-repo",
        repository_name="test_repository",
        revision=123,
        date=datetime.now(),
        filename="test_revision.md",
        filepath="/app/data/output/test_repo/docs/test_revision.md",
        size=1500,
        author="test_user",
        commit_message="Add OAuth2 authentication with security improvements",
        changed_paths=[
            "src/auth/oauth_handler.py",
            "src/auth/security_manager.py",
            "config/auth_config.json"
        ],
        repository_type="git"
    )

def test_metadata_extraction_with_specialized_models():
    """Test metadata extraction to see if specialized models are used"""
    print("=== Testing Metadata Extraction with Specialized Models ===")
    
    # Initialize services
    monitor_service = MonitorService("data/config.json")
    document_service = DocumentService(
        monitor_service.config.output_dir,
        ollama_client=monitor_service.ollama_client,
        config_manager=monitor_service.config_manager
    )
    
    # Create test document
    test_doc = create_test_document_record()
    
    # Test content that should trigger risk assessment
    test_content = """
    ## Code Review Recommendation
    This change introduces OAuth2 authentication with enhanced security measures.
    
    ## Impact Assessment
    - Security: HIGH - New authentication mechanism with token validation
    - Performance: MEDIUM - Additional validation overhead
    - Compatibility: LOW - Backward compatible API
    
    ## Risk Level
    HIGH RISK - Authentication changes require careful security review
    
    ## Documentation Impact
    YES - API documentation needs updates for new OAuth2 flow
    """
    
    print("🧪 Testing metadata extraction...")
    print(f"📋 Test content involves: OAuth2, authentication, security")
    print(f"🎯 Expected: Should use specialized models for code review/risk assessment")
    
    # This should trigger specialized model usage
    try:
        metadata = document_service._extract_metadata_with_llm(test_content, test_doc)
        print(f"\n✅ Metadata extraction completed")
        print(f"📊 Extracted metadata keys: {list(metadata.keys()) if metadata else 'None'}")
        
        if metadata:
            print(f"🔍 Risk level: {metadata.get('risk_level', 'Not found')}")
            print(f"🔍 Code review recommended: {metadata.get('code_review_recommended', 'Not found')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during metadata extraction: {e}")
        return False

def test_product_documentation_suggestions():
    """Test product documentation suggestions to see if documentation model is used"""
    print("\n=== Testing Product Documentation Suggestions ===")
    
    # Initialize services
    monitor_service = MonitorService("data/config.json")
    document_service = DocumentService(
        monitor_service.config.output_dir,
        ollama_client=monitor_service.ollama_client,
        config_manager=monitor_service.config_manager
    )
    
    # Create test document
    test_doc = create_test_document_record()
    
    # Test content for product documentation
    test_content = """
    ## Summary
    Added OAuth2 authentication support to the RepoSense AI platform.
    
    ## Changes Made
    - Implemented OAuth2 flow with JWT tokens
    - Added user authentication middleware
    - Updated API endpoints for token validation
    
    ## Impact Assessment
    This change affects user authentication and requires documentation updates.
    """
    
    print("🧪 Testing product documentation suggestions...")
    print(f"🎯 Expected: Should use specialized documentation model")
    
    try:
        # This should trigger documentation model usage
        suggestions = document_service._generate_product_documentation_suggestions(test_doc, test_content)
        print(f"\n✅ Product documentation suggestions completed")
        print(f"📝 Generated suggestions: {'Yes' if suggestions else 'No'}")
        
        if suggestions:
            print(f"📄 Suggestions length: {len(suggestions)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during documentation suggestions: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Specialized Models Usage in Practice")
    print("=" * 60)
    
    setup_logging()
    
    try:
        # Test metadata extraction (should use risk assessment/code review models)
        metadata_success = test_metadata_extraction_with_specialized_models()
        
        # Test product documentation (should use documentation model)
        doc_success = test_product_documentation_suggestions()
        
        print("\n" + "=" * 60)
        if metadata_success and doc_success:
            print("✅ SUCCESS: Specialized models are being used!")
            print("🔍 Check the log output above for '🎯' messages showing model usage")
        else:
            print("⚠️  Some tests failed - check the error messages above")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
