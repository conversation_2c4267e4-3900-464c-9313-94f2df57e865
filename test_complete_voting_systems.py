#!/usr/bin/env python3
"""
Comprehensive test script to demonstrate all enhanced voting mechanisms for metadata extraction
"""

def test_complete_voting_systems():
    """Test all four voting systems: code review recommendation, priority, risk, and documentation impact"""
    
    def vote_on_boolean(votes: dict, confidence_scores: dict, default_value: bool = False) -> bool:
        """Generic boolean voting mechanism"""
        if not votes:
            return default_value
        
        if len(votes) == 1:
            method, value = next(iter(votes.items()))
            confidence = confidence_scores.get(method, 0.5)
            return value if confidence >= 0.3 else default_value
        
        value_scores = {"True": 0.0, "False": 0.0}
        total_weight = 0.0
        
        for method, value in votes.items():
            confidence = confidence_scores.get(method, 0.5)
            value_key = "True" if value else "False"
            value_scores[value_key] += confidence
            total_weight += confidence
            
        if total_weight > 0:
            for val in value_scores:
                value_scores[val] /= total_weight
                
        winning_value = max(value_scores.keys(), key=lambda k: value_scores[k])
        winning_score = value_scores[winning_value]
        
        return (winning_value == "True") if winning_score >= 0.4 else default_value

    def vote_on_categorical(votes: dict, confidence_scores: dict, categories: list, default_value: str) -> str:
        """Generic categorical voting mechanism"""
        if not votes:
            return default_value
        
        if len(votes) == 1:
            method, value = next(iter(votes.items()))
            confidence = confidence_scores.get(method, 0.5)
            return value if confidence >= 0.3 else default_value
        
        category_scores = {cat: 0.0 for cat in categories}
        total_weight = 0.0
        
        for method, value in votes.items():
            confidence = confidence_scores.get(method, 0.5)
            if value in category_scores:
                category_scores[value] += confidence
                total_weight += confidence
                
        if total_weight > 0:
            for cat in category_scores:
                category_scores[cat] /= total_weight
                
        winning_category = max(category_scores.keys(), key=lambda k: category_scores[k])
        winning_score = category_scores[winning_category]
        
        return winning_category if winning_score >= 0.4 else default_value

    # Comprehensive test scenarios
    test_scenarios = [
        {
            "name": "🔒 Critical Security Vulnerability",
            "code_review_votes": {"heuristic": True, "llm": True, "file_analysis": True},
            "code_review_confidence": {"heuristic": 0.9, "llm": 0.9, "file_analysis": 0.8},
            "priority_votes": {"heuristic": "HIGH", "llm": "HIGH", "file_analysis": "HIGH"},
            "priority_confidence": {"heuristic": 0.9, "llm": 0.8, "file_analysis": 0.7},
            "risk_votes": {"heuristic": "HIGH", "llm": "HIGH", "file_analysis": "HIGH"},
            "risk_confidence": {"heuristic": 0.9, "llm": 0.8, "file_analysis": 0.8},
            "doc_votes": {"heuristic": True, "llm": True, "file_analysis": True},
            "doc_confidence": {"heuristic": 0.8, "llm": 0.9, "file_analysis": 0.6},
            "expected": {"code_review": True, "priority": "HIGH", "risk": "HIGH", "doc_impact": True}
        },
        {
            "name": "📝 Simple Documentation Update",
            "code_review_votes": {"heuristic": False, "llm": False},
            "code_review_confidence": {"heuristic": 0.8, "llm": 0.7},
            "priority_votes": {"heuristic": "LOW", "llm": "LOW"},
            "priority_confidence": {"heuristic": 0.8, "llm": 0.7},
            "risk_votes": {"heuristic": "LOW", "llm": "LOW"},
            "risk_confidence": {"heuristic": 0.9, "llm": 0.8},
            "doc_votes": {"heuristic": False, "llm": False},
            "doc_confidence": {"heuristic": 0.9, "llm": 0.8},
            "expected": {"code_review": False, "priority": "LOW", "risk": "LOW", "doc_impact": False}
        },
        {
            "name": "🤖 LLM Override Scenario",
            "code_review_votes": {"heuristic": False, "llm": True, "file_analysis": False},
            "code_review_confidence": {"heuristic": 0.4, "llm": 0.9, "file_analysis": 0.3},
            "priority_votes": {"heuristic": "LOW", "llm": "HIGH", "file_analysis": "MEDIUM"},
            "priority_confidence": {"heuristic": 0.4, "llm": 0.9, "file_analysis": 0.5},
            "risk_votes": {"heuristic": "LOW", "llm": "HIGH", "file_analysis": "MEDIUM"},
            "risk_confidence": {"heuristic": 0.3, "llm": 0.9, "file_analysis": 0.4},
            "doc_votes": {"heuristic": False, "llm": True, "file_analysis": True},
            "doc_confidence": {"heuristic": 0.3, "llm": 0.9, "file_analysis": 0.6},
            "expected": {"code_review": True, "priority": "HIGH", "risk": "HIGH", "doc_impact": True}
        },
        {
            "name": "⚖️ Balanced Mixed Signals",
            "code_review_votes": {"heuristic": True, "llm": False, "file_analysis": True},
            "code_review_confidence": {"heuristic": 0.6, "llm": 0.6, "file_analysis": 0.5},
            "priority_votes": {"heuristic": "MEDIUM", "llm": "LOW", "file_analysis": "HIGH"},
            "priority_confidence": {"heuristic": 0.5, "llm": 0.6, "file_analysis": 0.4},
            "risk_votes": {"heuristic": "MEDIUM", "llm": "LOW", "file_analysis": "HIGH"},
            "risk_confidence": {"heuristic": 0.6, "llm": 0.7, "file_analysis": 0.4},
            "doc_votes": {"heuristic": True, "llm": False, "file_analysis": True},
            "doc_confidence": {"heuristic": 0.5, "llm": 0.6, "file_analysis": 0.4},
            "expected": {"code_review": True, "priority": "MEDIUM", "risk": "LOW", "doc_impact": True}
        },
        {
            "name": "🛡️ Low Confidence Fallback",
            "code_review_votes": {"heuristic": True, "llm": False},
            "code_review_confidence": {"heuristic": 0.2, "llm": 0.2},
            "priority_votes": {"heuristic": "HIGH", "llm": "LOW"},
            "priority_confidence": {"heuristic": 0.2, "llm": 0.2},
            "risk_votes": {"heuristic": "HIGH", "llm": "LOW"},
            "risk_confidence": {"heuristic": 0.2, "llm": 0.2},
            "doc_votes": {"heuristic": True, "llm": False},
            "doc_confidence": {"heuristic": 0.2, "llm": 0.2},
            "expected": {"code_review": True, "priority": "MEDIUM", "risk": "MEDIUM", "doc_impact": False}
        }
    ]
    
    print("🗳️  Testing Complete Enhanced Voting Systems")
    print("=" * 70)
    print("Testing: Code Review Recommendation, Priority, Risk Level, Documentation Impact")
    print("=" * 70)
    
    total_tests = 0
    passed_tests = 0
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{scenario['name']}")
        print("-" * 50)
        
        # Test Code Review Recommendation
        code_review_result = vote_on_boolean(
            scenario["code_review_votes"], 
            scenario["code_review_confidence"], 
            default_value=True
        )
        code_review_status = "✅" if code_review_result == scenario["expected"]["code_review"] else "❌"
        print(f"Code Review: {scenario['code_review_votes']} → {code_review_result} {code_review_status}")
        
        # Test Priority
        priority_result = vote_on_categorical(
            scenario["priority_votes"], 
            scenario["priority_confidence"], 
            ["HIGH", "MEDIUM", "LOW"], 
            "MEDIUM"
        )
        priority_status = "✅" if priority_result == scenario["expected"]["priority"] else "❌"
        print(f"Priority:    {scenario['priority_votes']} → {priority_result} {priority_status}")
        
        # Test Risk Level
        risk_result = vote_on_categorical(
            scenario["risk_votes"], 
            scenario["risk_confidence"], 
            ["HIGH", "MEDIUM", "LOW"], 
            "MEDIUM"
        )
        risk_status = "✅" if risk_result == scenario["expected"]["risk"] else "❌"
        print(f"Risk:        {scenario['risk_votes']} → {risk_result} {risk_status}")
        
        # Test Documentation Impact
        doc_result = vote_on_boolean(
            scenario["doc_votes"], 
            scenario["doc_confidence"], 
            default_value=False
        )
        doc_status = "✅" if doc_result == scenario["expected"]["doc_impact"] else "❌"
        print(f"Doc Impact:  {scenario['doc_votes']} → {doc_result} {doc_status}")
        
        # Calculate test results
        test_results = [
            code_review_result == scenario["expected"]["code_review"],
            priority_result == scenario["expected"]["priority"],
            risk_result == scenario["expected"]["risk"],
            doc_result == scenario["expected"]["doc_impact"]
        ]
        
        scenario_passed = all(test_results)
        total_tests += 4
        passed_tests += sum(test_results)
        
        overall_status = "✅ PASS" if scenario_passed else "❌ FAIL"
        print(f"Overall: {overall_status} ({sum(test_results)}/4 tests passed)")

    print(f"\n🎉 Complete Voting Systems Test Results")
    print("=" * 50)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n📊 Enhanced Voting System Benefits:")
    print("• 🎯 Multi-method validation for all metadata fields")
    print("• 🛡️ Error resilience through cross-validation")
    print("• 📈 Confidence-weighted decision making")
    print("• 🔍 Comprehensive audit trail for all decisions")
    print("• ⚖️ Balanced approach between speed and accuracy")

if __name__ == "__main__":
    test_complete_voting_systems()
