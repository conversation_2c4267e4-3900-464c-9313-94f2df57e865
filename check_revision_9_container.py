#!/usr/bin/env python3
"""
Check if revision 9 for reposense_cpp_test was scanned in the container environment
"""

import sqlite3
import os
from datetime import datetime

def check_revision_9_container():
    """Check if revision 9 for reposense_cpp_test was processed in container"""
    print("🔍 Checking if revision 9 for reposense_cpp_test was scanned (Container Environment)...")
    
    try:
        # Container database path
        db_path = "/app/data/documents.db"
        
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            # Try alternative paths
            alt_paths = ["/app/data/reposense_ai.db", "data/documents.db"]
            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    db_path = alt_path
                    print(f"✅ Found database at: {alt_path}")
                    break
            else:
                print("❌ No database file found")
                return
        else:
            print(f"✅ Using database at: {db_path}")
            
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check for reposense_cpp_test documents
        print(f"\n📊 Searching for reposense_cpp_test documents...")
        
        # Search for reposense_cpp_test specifically
        cursor.execute("""
            SELECT id, repository_name, revision, date, author, commit_message, processed_time
            FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%' 
            ORDER BY revision DESC
            LIMIT 10
        """)
        
        docs = cursor.fetchall()
        print(f"\n📋 Found {len(docs)} recent documents for reposense_cpp_test:")
        
        revision_9_found = False
        for doc in docs:
            doc_id, repo_name, revision, date, author, commit_msg, processed_time = doc
            status = "✅ REVISION 9!" if revision == 9 else ""
            print(f"  Revision {revision}: {commit_msg[:60]}{'...' if len(commit_msg) > 60 else ''} {status}")
            print(f"    Date: {date}, Author: {author}")
            print(f"    Processed: {processed_time}")
            
            if revision == 9:
                revision_9_found = True
            print()
        
        # Check specifically for revision 9
        cursor.execute("""
            SELECT id, repository_name, revision, date, author, commit_message, processed_time
            FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%' AND revision = 9
        """)
        
        rev_9_docs = cursor.fetchall()
        
        if rev_9_docs:
            print(f"🎯 Revision 9 Details:")
            for doc in rev_9_docs:
                doc_id, repo_name, revision, date, author, commit_msg, processed_time = doc
                print(f"  ✅ FOUND REVISION 9!")
                print(f"  Repository: {repo_name}")
                print(f"  Date: {date}")
                print(f"  Author: {author}")
                print(f"  Commit Message: {commit_msg}")
                print(f"  Processed Time: {processed_time}")
                print(f"  Document ID: {doc_id}")
        else:
            print(f"❌ Revision 9 NOT found in database")
        
        # Get revision range
        cursor.execute("""
            SELECT MIN(revision), MAX(revision), COUNT(*) FROM documents 
            WHERE repository_name LIKE '%reposense_cpp_test%'
        """)
        min_rev, max_rev, count = cursor.fetchone()
        
        if min_rev and max_rev:
            print(f"\n📈 Repository Statistics:")
            print(f"  Total documents: {count}")
            print(f"  Revision range: {min_rev} to {max_rev}")
            if max_rev >= 9:
                print(f"  ✅ System has processed up to revision {max_rev} (includes revision 9)")
            else:
                print(f"  ⏳ System has only processed up to revision {max_rev} (revision 9 not yet scanned)")
        
        conn.close()
        
        return revision_9_found
        
    except Exception as e:
        print(f"❌ Error checking revision 9: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_container_logs():
    """Check container logs for recent scanning activity"""
    print(f"\n📋 Checking container logs for scanning activity...")
    
    try:
        log_path = "/app/data/reposense_ai.log"
        
        if not os.path.exists(log_path):
            print(f"❌ Log file not found at {log_path}")
            return
            
        print(f"📄 Reading logs from: {log_path}")
        
        # Look for recent scanning activity
        with open(log_path, 'r') as f:
            lines = f.readlines()
        
        # Get last 50 lines and look for relevant activity
        recent_lines = lines[-50:]
        
        scanning_activity = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in [
                'checking for new commits', 
                'revision 9', 
                'reposense_cpp_test', 
                'queued', 
                'processed',
                'new commits in',
                'process_commit'
            ]):
                scanning_activity.append(line.strip())
        
        if scanning_activity:
            print(f"🔍 Recent scanning activity:")
            for line in scanning_activity[-15:]:  # Show last 15 relevant lines
                print(f"  {line}")
        else:
            print(f"❌ No recent scanning activity found in logs")
            
        # Also check for any errors
        error_lines = []
        for line in recent_lines:
            if 'error' in line.lower() or 'exception' in line.lower():
                error_lines.append(line.strip())
        
        if error_lines:
            print(f"\n⚠️ Recent errors in logs:")
            for line in error_lines[-5:]:  # Show last 5 error lines
                print(f"  {line}")
            
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

if __name__ == "__main__":
    found = check_revision_9_container()
    check_container_logs()
    
    print(f"\n💡 Summary:")
    if found:
        print(f"  ✅ Revision 9 for reposense_cpp_test WAS scanned and processed")
        print(f"  📄 You can view it in the web interface at http://localhost:5000")
    else:
        print(f"  ❌ Revision 9 for reposense_cpp_test was NOT found")
        print(f"  ⏳ It may still be processing or there might be an issue")
        print(f"  🔄 The system scans every 5 minutes, so check again soon")
