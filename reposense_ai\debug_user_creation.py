#!/usr/bin/env python3
"""
Debug script to test user creation and configuration saving
"""

import sys
import os
sys.path.append('/app')

from config_manager import ConfigManager
from user_management_service import UserManagementService
from models import UserRole
import json

def debug_user_creation():
    """Debug user creation process"""
    print("🔍 Debugging User Creation Process")
    print("=" * 50)
    
    try:
        # Initialize config manager
        print("1. Initializing ConfigManager...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print(f"   ✅ Config loaded from: {config_manager.config_path}")
        print(f"   📊 Current users count: {len(config.users)}")
        
        if config.users:
            print("   👥 Existing users:")
            for user in config.users:
                print(f"      - {user.username} ({user.email}) - {user.role.value}")
        else:
            print("   ⚠️  No users found in configuration")
        
        # Initialize user service
        print("\n2. Initializing UserManagementService...")
        user_service = UserManagementService(config)
        print("   ✅ UserManagementService initialized")
        
        # Test user creation
        print("\n3. Testing user creation...")
        test_username = "debug_admin"
        test_email = "<EMAIL>"
        test_full_name = "Debug Admin User"
        
        # Check if user already exists
        existing_user = config.get_user_by_username(test_username)
        if existing_user:
            print(f"   ⚠️  User {test_username} already exists, skipping creation")
            return
        
        success, message, user = user_service.create_user(
            username=test_username,
            email=test_email,
            full_name=test_full_name,
            role=UserRole.ADMIN
        )
        
        print(f"   📝 User creation result: {success}")
        print(f"   💬 Message: {message}")
        
        if success and user:
            print(f"   👤 Created user: {user.username} ({user.email}) - {user.role.value}")
            print(f"   🆔 User ID: {user.id}")
            
            # Check if user is in config (use same config object)
            print("\n4. Checking if user is in configuration...")
            print(f"   📊 Users count after creation: {len(config.users)}")

            found_user = config.get_user_by_username(test_username)
            if found_user:
                print(f"   ✅ User found in config: {found_user.username}")
            else:
                print(f"   ❌ User NOT found in config!")

            # Try to save config manually
            print("\n5. Testing manual config save...")
            try:
                config_manager.save_config(config)
                print("   ✅ Config saved successfully")

                # Verify save by reloading
                reloaded_config = config_manager.load_config()
                print(f"   📊 Users count after reload: {len(reloaded_config.users)}")

                if reloaded_config.get_user_by_username(test_username):
                    print("   ✅ User persisted successfully!")
                else:
                    print("   ❌ User NOT persisted!")

            except Exception as e:
                print(f"   ❌ Config save failed: {e}")
        
        # Show current config file content
        print("\n6. Current config file content:")
        try:
            with open(config_manager.config_path, 'r') as f:
                config_data = json.load(f)
                users_data = config_data.get('users', [])
                print(f"   📄 Config file users count: {len(users_data)}")
                
                if users_data:
                    print("   👥 Users in config file:")
                    for user_data in users_data:
                        username = user_data.get('username', 'Unknown')
                        email = user_data.get('email', 'Unknown')
                        role = user_data.get('role', 'Unknown')
                        print(f"      - {username} ({email}) - {role}")
                else:
                    print("   ⚠️  No users in config file")
                    
        except Exception as e:
            print(f"   ❌ Error reading config file: {e}")
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_user_creation()
