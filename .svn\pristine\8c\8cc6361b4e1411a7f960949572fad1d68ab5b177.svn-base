#!/usr/bin/env python3
"""
Check what revisions actually exist in the repository
"""

import requests
import json

def check_repository_revisions():
    """Check what the repository scanning logic sees"""
    print("🔍 Checking what revisions exist in the repository...")
    
    try:
        # Get repository information from the web interface
        response = requests.get('http://localhost:5000/repositories', timeout=10)
        
        if response.status_code == 200:
            print("✅ Got repository page")
            # The page should show the repositories and their current status
            
        # Try to get repository details via API
        # Note: We need to find the right API endpoint for repository details
        
        print("\n💡 Manual Steps to Check:")
        print("1. Go to http://localhost:5000 in your browser")
        print("2. Look at the Repository Management section")
        print("3. Check the 'Last Revision' column for reposense_cpp_test")
        print("4. If Last Revision < 9: The repository hasn't been scanned recently")
        print("5. If Last Revision >= 9: The scanning worked but documents weren't created")
        
        # Try to trigger a repository update
        print(f"\n🔄 Attempting to trigger repository scan...")
        
        # Force a manual check which should update repository revisions
        check_response = requests.post('http://localhost:5000/api/check', timeout=120)
        
        if check_response.status_code == 200:
            check_data = check_response.json()
            print(f"✅ Manual check completed")
            print(f"  Repositories checked: {check_data.get('repositories_checked', 0)}")
            
            print(f"\n📋 After manual check:")
            print(f"  1. Check the web interface repository list")
            print(f"  2. Look for updated 'Last Revision' numbers")
            print(f"  3. If Last Revision is now >= 9, the system found your commits")
            
        else:
            print(f"❌ Manual check failed: {check_response.text}")
            
    except Exception as e:
        print(f"❌ Error checking repository: {e}")

def check_logs_for_scanning():
    """Check logs for any recent scanning activity"""
    print(f"\n📋 Checking logs for repository scanning activity...")
    
    try:
        log_path = "reposense_ai/data/reposense_ai.log"
        
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Look for the most recent scanning activity
        recent_lines = lines[-200:]  # Check more lines
        
        scanning_activity = []
        for line in recent_lines:
            if any(keyword in line.lower() for keyword in [
                'checking for new commits',
                'found new commits',
                'latest revision',
                'new commits in',
                'process_commit',
                'revision',
                'reposense_cpp_test'
            ]):
                scanning_activity.append(line.strip())
        
        if scanning_activity:
            print(f"🔍 Recent scanning activity:")
            for line in scanning_activity[-15:]:  # Show last 15
                print(f"  {line}")
        else:
            print(f"❌ No scanning activity found in recent logs")
            print(f"   This confirms that periodic monitoring is not working")
            
    except Exception as e:
        print(f"❌ Error checking logs: {e}")

def suggest_next_steps():
    """Suggest next steps based on findings"""
    print(f"\n💡 Next Steps:")
    print(f"")
    print(f"🔍 FIRST - Verify revision 9 exists:")
    print(f"  1. Check your SVN repository directly")
    print(f"  2. Confirm revision 9 was actually committed")
    print(f"  3. Check the repository URL is correct")
    print(f"")
    print(f"🔧 IF revision 9 exists but not detected:")
    print(f"  1. Restart the Docker container")
    print(f"  2. This will reset the monitoring state")
    print(f"  3. Fresh start should detect all revisions")
    print(f"")
    print(f"⚙️ IF monitoring is not working:")
    print(f"  1. The periodic scanning daemon may have crashed")
    print(f"  2. Container restart will fix this")
    print(f"  3. After restart, monitoring should auto-start")
    print(f"")
    print(f"🎯 Expected after restart:")
    print(f"  1. System scans repository from last known revision")
    print(f"  2. Discovers revisions 5, 6, 7, 8, 9")
    print(f"  3. Processes and documents each revision")
    print(f"  4. Revision 9 appears in the web interface")

if __name__ == "__main__":
    check_repository_revisions()
    check_logs_for_scanning()
    suggest_next_steps()
