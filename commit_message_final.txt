fix: historical scan progress bar calculation and state management

## 🔧 Progress Tracking Fixes

### **Historical Scan Progress Bar Issues**
- Fix progress bar jumping to 100% when scanning earlier revision ranges (e.g., 1-4 after 5-8)
- Implement proper progress state cleanup before starting new scans
- Add explicit clearing of both active_scans and completed_scans dictionaries
- Prevent race conditions where multiple progress trackers could exist simultaneously

### **Progress State Management**
- Ensure progress always initializes with processed_revisions=0 and correct total_revisions
- Implement clean state transitions from active to completed scans
- Add proper memory management to remove stale progress data
- Fix progress calculation to use current scan range instead of cumulative counts

### **Enhanced Error Handling & Reliability**
- Add fallback repository lookup mechanism for corrupted document IDs
- Implement repository lookup by name when ID lookup fails
- Enhance Flask route handling with path converter for complex document IDs
- Add global error handlers ensuring JSON responses for all API endpoints

## 🤖 AI Model Management & Transparency

### **AI Model Visibility**
- Add AI model display across all document views (Table, Card, Repository Groups)
- Implement professional badges with robot icons showing processing models
- Add clear distinction between AI-analyzed and legacy documents
- Enhance user confidence through complete AI workflow transparency

### **Document Rescan Functionality**
- Implement advanced document reprocessing with AI model selection
- Add modal interface for selecting from available Ollama models
- Support risk assessment aggressiveness configuration per document
- Add option to preserve existing user feedback during reprocessing
- Include real-time model availability validation and error handling

## 🚀 Performance & Caching Improvements

### **Cache-Busting Technology**
- Implement versioned URLs using document processing timestamps
- Add no-store cache headers for document views and PDF downloads
- Create automatic cache invalidation after document processing
- Enhance processing status monitoring with 3-second polling intervals

### **Auto-Refresh System**
- Add intelligent auto-refresh with processing completion detection
- Implement multiple refresh strategies: immediate, backup, and processing-completion
- Create non-disruptive updates that preserve user workflow
- Add enhanced processing status monitoring for real-time feedback

## 🛠️ Technical Infrastructure

### **URL Handling & API Improvements**
- Update all document routes to use Flask path converter (<path:doc_id>)
- Fix "API endpoint not found" errors for documents with slashes in IDs
- Enhance URL encoding/decoding for repository and document identification
- Add comprehensive error handling with proper JSON responses

### **Database & Configuration**
- Implement fallback repository lookup for data integrity
- Add ai_model_used field tracking to document database schema
- Enhance Flask URL configuration for better slash handling
- Improve cache management with namespace-specific invalidation

## 📚 Documentation & Marketing Updates

### **Comprehensive Documentation**
- Update CHANGELOG.md with detailed feature descriptions
- Enhance features.md with AI model management section
- Update marketing materials highlighting AI transparency and control
- Improve quick-start guide with AI model selection tips
- Add business value descriptions for new capabilities

### **User Experience Documentation**
- Document AI model visibility features and benefits
- Explain document rescan functionality and use cases
- Describe cache-busting technology and performance improvements
- Provide troubleshooting guidance for common issues

## 🔍 Code Quality & Maintenance

### **Clean Code Practices**
- Remove all temporary debug logging while preserving core fixes
- Implement proper error handling with descriptive messages
- Add comprehensive logging for troubleshooting without debug noise
- Maintain backward compatibility with legacy documents

### **System Reliability**
- Ensure enterprise-grade reliability and performance
- Add robust error recovery mechanisms
- Implement intelligent fallback systems for data integrity
- Maintain complete data privacy with on-premises AI processing

This release significantly enhances system reliability, user experience, and AI transparency while maintaining the core privacy-first architecture. The progress tracking fixes ensure accurate progress reporting for all scanning scenarios, and the AI model management features provide complete transparency and control over AI processing.

Breaking Changes: None - fully backward compatible
Dependencies: No new dependencies required
Testing: Comprehensive manual testing completed for all fixes and features
