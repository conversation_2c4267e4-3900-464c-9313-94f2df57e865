
#!/usr/bin/env python3
"""
RepoSense AI with Private AI-powered Documentation Generation
Monitors repositories (SVN, Git) for changes and generates documentation/emails using local LLM
Designed to run in Docker containers with proper volume management

This is the main entry point that uses the refactored modular components.
"""

import sys
import time
from pathlib import Path

from monitor_service import MonitorService
from web_interface import WebInterface


def main():
    """Main entry point - no arguments, all config from files"""
    # Ensure data directory exists
    Path("/app/data").mkdir(parents=True, exist_ok=True)

    # Initialize monitor service
    # Use development config if available, otherwise default
    import os
    config_path = "/app/config.dev.json" if os.path.exists("/app/config.dev.json") else "/app/data/config.json"
    monitor_service = MonitorService(config_path)

    # If web interface is enabled, start it
    if monitor_service.config.web_enabled:
        web_interface = WebInterface(monitor_service)

        # Start monitoring if any repositories are configured
        enabled_repos = monitor_service.config.get_enabled_repositories()
        if enabled_repos:
            monitor_service.start_monitoring()
            print(f"Monitoring {len(enabled_repos)} repositories")

        print(f"Web interface starting on http://{monitor_service.config.web_host}:{monitor_service.config.web_port}")
        print("Access the web interface to configure and monitor repositories")

        try:
            web_interface.run()
        except KeyboardInterrupt:
            print("\nShutting down...")
            monitor_service.stop_monitoring()
    else:
        # Run in daemon mode without web interface
        enabled_repos = monitor_service.config.get_enabled_repositories()
        if not enabled_repos:
            print("No repositories configured and web interface is disabled.")
            print("Please configure repositories via config.json file or enable web interface.")
            sys.exit(1)

        try:
            print(f"Starting daemon mode monitoring for {len(enabled_repos)} repositories")
            monitor_service.start_monitoring()
            # Keep main thread alive
            while monitor_service.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
            monitor_service.stop_monitoring()


if __name__ == "__main__":
    main()
